﻿//#define USE_HL_KEY // Use Hardware Key
#define USE_EOM // Use EngineObjectManager （インスタンス管理用）

#define SSO_DISABLED // SSO認証しない場合はこちらを有効にすること

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using eDoktorTaikoban.FaceClientCommon;

namespace eDoktor.Taikoban.FaceInsert
{
    /// <summary>
    /// 顔データ登録・削除クライアント メニュー画面
    /// </summary>
    public partial class FaceInsertMenu : Form
    {
        #region Private Fields

        /// <summary>
        /// 要求送信クラス
        /// </summary>
        RequestSendClass _requestSend = new RequestSendClass();

        /// <summary>
        /// 認証エラーフラグ
        /// </summary>
        private bool authErrFlg = false;

        /// <summary>
        /// カメラデバイス ホワイトリスト
        /// </summary>
        private List<string> _whiteList;

        /// <summary>
        /// カメラデバイス ブラックリスト
        /// </summary>
        private List<string> _blackList;

        /// <summary>
        /// 設定情報
        /// </summary>
        private SettingsInfo _settingInfo;

        #endregion

        #region Constructors

        /// <summary>
        /// コンストラクタ
        /// </summary>
        public FaceInsertMenu()
        {
            InitializeComponent();
        }

        #endregion 

        /// <summary>
        /// フォームロード
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FaceInsertFormMain_Load(object sender, EventArgs e)
        {

            // サーバーに接続する
            if (this.ServerConnect() == false)
            {
                // エラー画面を表示したので、メニュー画面も閉じる
                this.Close();
                return;
            }

#if SSO_DISABLED
#else
            // サーバーに接続できたらSSO認証を行う。
            bool ret = SSOAuthCheck();

            // 認証失敗
            if (ret)
            {
                Trace.OutputErrorTrace("SSO認証に失敗しました。");
                this.Close();
                return;
            }
#endif // SSO_DISABLED

            if (this.GetSettingsInfo() == false)
            {
                MessageBox.Show(Messages.Message(MessageId.Message006));
                this.Close();
            }
        }

        /// <summary>
        /// ファイル選択ボタン押下時
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnFileSelect_Click(object sender, EventArgs e)
        {
            using (FaceInsertFileSelect faceInsertFileSelect = new FaceInsertFileSelect(this._settingInfo))
            {
                this.Hide();
                faceInsertFileSelect.ShowDialog(this);
                this.Show();
            }
        }

        /// <summary>
        /// 撮影登録ボタン押下時
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnPhotoEnrollment_Click(object sender, EventArgs e)
        {
            using (FaceInsertIdentification faceInsertIdentification = new FaceInsertIdentification(_whiteList, _blackList, this._settingInfo))
            {
                this.Hide();
                faceInsertIdentification.ShowDialog(this);
                this.Show();
            }
        }

        /// <summary>
        /// 一括登録押下時
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnLumpEnrollment_Click(object sender, EventArgs e)
        {
            using (FaceInsertBulkRegister faceInsertBulkRegister = new FaceInsertBulkRegister(this._settingInfo))
            {
                this.Hide();
                faceInsertBulkRegister.ShowDialog(this);
                this.Show();
            }
        }

        /// <summary>
        /// 削除ボタン押下時
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnClear_Click(object sender, EventArgs e)
        {
            using (FaceInsertDelete faceInsertDelete = new FaceInsertDelete())
            {
                this.Hide();
                faceInsertDelete.ShowDialog(this);
                this.Show();
            }
        }

        /// <summary>
        /// 閉じるボタン押下時
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnCloses_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// マウスカーソルがボタン領域に入った時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseEnter(object sender, EventArgs e)
        {
            try
            {
                Button btn = (Button)sender;
                btn.BackgroundImage = eDoktor.Taikoban.FaceInsert.Properties.Resources.btn_2;
                btn.ForeColor = System.Drawing.Color.FromArgb(24, 109, 178);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }

        }

        /// <summary>
        /// マウスカーソルがボタン領域から抜けた時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseLeave(object sender, EventArgs e)
        {
            try
            {
                Button btn = (Button)sender;
                btn.BackgroundImage = eDoktor.Taikoban.FaceInsert.Properties.Resources.btn_1;
                btn.ForeColor = System.Drawing.SystemColors.Window;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// ボタン押下時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                Button btn = (Button)sender;
                btn.BackgroundImage = eDoktor.Taikoban.FaceInsert.Properties.Resources.btn_3;
                btn.ForeColor = System.Drawing.SystemColors.Window;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// マウスカーソルが閉じるボタン領域に入った時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void BtnCloses_MouseEnter(object sender, EventArgs e)
        {
            try
            {
                Button btn = (Button)sender;
                btn.ForeColor = System.Drawing.Color.FromArgb(180, 209, 234);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }

        }

        /// <summary>
        /// マウスカーソルが閉じるボタン領域から抜けた時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void BtnCloses_MouseLeave(object sender, EventArgs e)
        {
            try
            {
                Button btn = (Button)sender;
                btn.ForeColor = System.Drawing.SystemColors.Window;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// ボタン押下時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void BtnCloses_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                Button btn = (Button)sender;
                btn.ForeColor = System.Drawing.Color.FromArgb(46, 177, 214);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }



        /// <summary>
        /// server接続処理
        /// </summary>
        /// <returns>処理結果</returns>
        private bool ServerConnect()
        {
            if (!this._requestSend.ServerConnect())
            {
                // 接続に失敗したので、エラー画面を表示する。
                FaceInsertConnectErr faceInsertConnectErr = new FaceInsertConnectErr();
                DialogResult dialogResult = faceInsertConnectErr.ShowDialog(this);

                // 閉じるボタン押下時は、メニュー画面も閉じるのでフラグを立てる
                if (dialogResult == DialogResult.Cancel)
                {
                    return false;
                }
                else
                {
                    // 再接続ボタン押下時は再帰的に再接続処理をコールする
                    return this.ServerConnect();
                }
            }

            return true;
        }

        /// <summary>
        /// 設定値を取得する
        /// </summary>
        /// <param name="client"></param>
        /// <returns>処理結果</returns>
        private bool GetSettingsInfo()
        {
            var res = this._requestSend.RequestGetSettingInfo(out FaceInterprocess.FaceSettingGetResData resData);

            if (res)
            {
                this._whiteList = resData.DeviceWhiteList;
                this._blackList = resData.DeviceBlackList;

                this._settingInfo = resData.SettingInfo;
            }

            return res;
        }

        #region SSO

#if SSO_DISABLED
        // デバッグ時はSSO画面は表示しない。Authenticate()でエラーになる。
#else
        const string AppAuthorityName = "can_admin_console";

        static bool _originalAuth;
        public static bool OriginalAuth
        {
            get
            {
                return _originalAuth;
            }
        }

        static int _authUserId;
        public static int AuthUserId
        {
            get
            {
                return _authUserId;
            }
        }

        static int _authUserLevel;
        public static int AuthUserLevel
        {
            get
            {
                return _authUserLevel;
            }
        }
#endif // SSO_DISABLED

#if SSO_DISABLED
        // デバッグ時はSSO画面は表示しない。Authenticate()でエラーになる。
#else
        // SSO認証を行う
        private bool SSOAuthCheck()
        {
            bool error = false;
            System.Configuration.ConnectionStringSettings connectionStringSettings;
            Database Database = null;

        #region データベース設定
            if (!error)
            {
                eDoktor.Common.Configuration.Reload();
                connectionStringSettings = eDoktor.Common.Configuration.ConnectionStringSettings(eDoktor.Taikoban.FaceInsert.Properties.Settings.Default.ConnectionStringSettingsLabel);
                // ▼ MOD Common.dll修正対応 2015/09/25
                //connectionStringSettings.ConnectionString = eDoktor.Common.Crypto.DecryptString(connectionStringSettings.ConnectionString, "=o8^2a<~\"/R*", "E-/~mo?Z", 1226, "Rijndael");
                connectionStringSettings.ConnectionString = eDoktor.Taikoban.FaceInsert.Configuration.DecryptString(connectionStringSettings.ConnectionString);
                // ▲ MOD Common.dll修正対応 2015/09/25
                Database = new Database(connectionStringSettings);
            }
        #endregion

        #region 認証
            using (Extension appExtension = new Extension(eDoktor.Taikoban.FaceInsert.Configuration.SystemId, "security"))
            {
                string logonId;
                bool originalAuth;
                int authority = 0;
                if (appExtension.Authenticate(Database, AppAuthorityName, out logonId, out _authUserId, out originalAuth, ref authority) < 0)
                {
                    error = true;

                    // 認証に失敗したので、メニュー画面を終了する。
                    authErrFlg = true;
                }
            }
        #endregion

            return error;

        }
#endif // SSO_DISABLED

        #endregion
    }
}
