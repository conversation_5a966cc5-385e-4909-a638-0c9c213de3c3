﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktorTaikoban.FaceClientCommon;

namespace eDoktor.Taikoban.FaceInsert
{
    /// <summary>
    /// 削除確認画面
    /// </summary>
    public partial class FaceInsertDeleteConfirm : Form
    {
        /// <summary>
        /// 要求送信クラス
        /// </summary>
        RequestSendClass _requestSend = new RequestSendClass();

        private int _accountId;

        /// <summary>
        /// ボタンアクションクラス
        /// </summary>
        BtnImageCahnge _btnImageCahnge = new BtnImageCahnge();

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="accountId">アカウントID</param>
        /// <param name="staffId">職員ID</param>
        /// <param name="staffName">職員名</param>
        public FaceInsertDeleteConfirm(int accountId, string staffId, string staffName)
        {
            InitializeComponent();

            this._accountId = accountId;
            this.label_staffid.Text = staffId;
            this.label_staffName.Text = staffName;
        }

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="requestSend">要求送信クラス</param>
        /// <param name="accountId">アカウントID</param>
        /// <param name="staffId">職員ID</param>
        /// <param name="staffName">職員名</param>
        public FaceInsertDeleteConfirm(RequestSendClass requestSend, int accountId, string staffId, string staffName)
           : this(accountId, staffId, staffName)
        {
            this._requestSend = requestSend;
        }

        /// <summary>
        /// 「はい」ボタン押下時
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void button1_Click(object sender, EventArgs e)
        {
            // 「顔テンプレート削除要求」を送信する
            var res = this._requestSend.RequestFaceTemplateDelete(this._accountId, out FaceInterprocess.FaceTemplateDelResData resData);

            if (!res)
            {
                // サーバーからの応答がありません。
                MessageBox.Show(Messages.Message(MessageId.Message004));
                return;
            }

            // 処理結果が正常終了
            if (resData.ProcResult == EnumProcResult.Success)
            {
                // 削除が完了しました。
                MessageBox.Show(Messages.Message(MessageId.Message019, this.label_staffid.Text, this.label_staffName.Text));
            }
            else
            {
                // 処理結果が異常終了
                var errMsg = ClientCommonProc.ErrorIdToErrorMessage(resData.ErrorId, this.label_staffid.Text);

                if (!string.IsNullOrEmpty(errMsg))
                {
                    MessageBox.Show(errMsg);
                }
            }

            this.Close();
        }

        /// <summary>
        /// 「いいえ」ボタン押下時
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>        
        private void button2_Click(object sender, EventArgs e)
        {
            this.Close();
        }


        /// <summary>
        /// マウスカーソルがボタン領域に入った時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseEnter(object sender, EventArgs e)
        {
            _btnImageCahnge.btn_MouseEnter(sender, e);
        }

        /// <summary>
        /// マウスカーソルがボタン領域から抜けた時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseLeave(object sender, EventArgs e)
        {
            _btnImageCahnge.btn_MouseLeave(sender, e);
        }

        /// <summary>
        /// ボタン押下時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            _btnImageCahnge.btn_MouseDown(sender, e);
        }


    }
}
