//#define DEBUG_MODE // デバッグしたい場合はこちらを有効にすること
#define FACE_RECOGNITION              //  顔認証を行う時定義
//#define SAVE_THE_BITMAP_USED          // 顔認証で使用したビットマップをセーブするかどうか(デバッグ用)
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Security.Permissions;
using System.Windows.Forms;
using System.Reflection;
using AForge.Video;
using AForge.Video.DirectShow;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using eDoktorTaikoban.FaceClientCommon;
using eDoktor.Taikoban.FaceDetection;
using eDoktor.Taikoban.FaceImageCntl;

namespace eDoktor.Taikoban.FaceClient
{
    /// <summary>
    /// 顔データ認証クライアント
    /// </summary>
    public partial class FaceClientFormMain : Form
    {
        #region Private Const

        /// <summary>
        /// 生体認証結果通知用メッセージコード
        /// </summary>
        private const int WM_APP_MSG_2 = 0x8001;

        /// <summary>
        /// インターフェース画面からの命令受信用メッセージコード
        /// </summary>
        private const int WM_COPYDATA = 0x004A;

        /// <summary>
        /// 画面ロード完了メッセージ
        /// </summary>
        private const int WM_APP_AUTH_FORM_LOADED = 0x8004;

        /// <summary>
        /// 顔画像表示エリアMAX 高さ
        /// </summary>
        private const int CST_MAX_FACE_AREA_HEIGHT = 300;
        /// <summary>
        /// 顔画像表示エリアMAX 幅
        /// </summary>
        private const int CST_MAX_FACE_AREA_WIDTH = 440;
        /// <summary>
        /// 顔検出実行カウンター MAX値
        /// </summary>
        private const int CST_FACE_DETECTION_EXECUTION_COUNTER_MAX = 10;
        /// <summary>
        /// 顔検出状態表示間隔(ms)
        /// </summary>
        private const int CST_FACE_DETECTION_STATE_DISPLAY_INTERVAL = 100;

        private const string CST_RESOURCE_PROPERTY_NAME = "eDoktor.Taikoban.FaceClient.Properties.Resources";

        // ▼ ADD 認証タイムアウト時のアプリ終了対応 2024/01/26 Whizz Y.Tani
        /// <summary>
        /// 顔検出タイムアウトアプリ終了タイマー間隔(ms)
        /// </summary>
        private const int CST_FACE_DETECTION_TIMEOUT_APP_CLOSE_TIMER_INTERVAL = 10 * 1000;
        // ▲ ADD 認証タイムアウト時のアプリ終了対応 2024/01/26 Whizz Y.Tani
        #endregion

        #region Private Fields

        /// <summary>
        /// 起動時引数
        /// </summary>
        private Arguments _args = new Arguments();

        /// <summary>
        /// 親設定済みフラグ
        /// </summary>
        private bool _parentSet = false;

        /// <summary>
        /// 表示時のサイズ
        /// </summary>
        private System.Drawing.Size _fullFormSize = new System.Drawing.Size(0, 0);

        /// <summary>
        /// カメラデバイス ホワイトリスト
        /// </summary>
        private List<string> _whiteList = new List<string>();

        /// <summary>
        /// カメラデバイス ブラックリスト
        /// </summary>
        private List<string> _blackList = new List<string>();

        /// <summary>
        /// 社員ID
        /// </summary>
        private EnumUserAuthMode _userAuthMode = EnumUserAuthMode.UserSearchAuthMode;

        /// <summary>
        /// アカウントID
        /// </summary>
        private int _accountId = 0;

        /// <summary>
        /// 現在のリトライ回数
        /// </summary>
        private int _RetryCount = 0;

        /// <summary>
        /// 設定情報
        /// </summary>
        private SettingsInfo _settingsInfo = new SettingsInfo();

        /// <summary>
        /// 要求送信クラス
        /// </summary>
        private RequestSendClass _requestSend = new RequestSendClass();

        /// <summary>
        /// カメラ映像取得
        /// </summary>
        private VideoCaptureDevice _videoCaptureDevice = new VideoCaptureDevice();

        /// <summary>
        /// 顔画像表示エリアの高さ
        /// </summary>
        private int _height = 0;

        /// <summary>
        /// 顔画像表示エリアの幅
        /// </summary>
        private int _width = 0;

        /// <summary>
        /// カメラ画像反転フラグ(true時反転==>画像が鏡の画像となる)
        /// </summary>
        private bool _isFlipHorizon = true;

        /// <summary>
        /// 顔登録がない場合に、登録画面に遷移する（true：顔登録、false：終了）
        /// </summary>
        private bool _isTransitionRegist = false;

        /// <summary>
        /// 顔登録画面に遷移する際に同意画面を表示するか (false=表示しない true=表示する)
        /// </summary>
        private bool _viewConsentScreen = false;

        /// <summary>
        /// 登録画面に遷移する際のメッセージ文画像
        /// </summary>
        private string _displayMessageImageFilePath = string.Empty;

        /// <summary>
        /// 顔登録時の応答値 1=顔認証に遷移 2=初期画面に戻る
        /// </summary>
        private int _returnValueWhenFaceRegisterd = 2;

        /// <summary>
        /// 取得イメージ設定済みフラグ
        /// </summary>
        /// <remarks>
        /// ・起動時、最初にカメラからのデータを取得するまで認証しない
        /// ・認証失敗時、取得イメージが更新されていない場合、認証しない
        /// 　（初回起動時のアクセス許可のポップアップを押すまでの間に同じ画像で何回も認証を行ってしまっていたため）
        /// </remarks>
        private bool _isSetImage = false;

        /// <summary>
        /// なりすまし画像設定済みフラグ
        /// </summary>
        /// <remarks>
        /// ・起動時、最初にカメラからのデータを取得するまで認証しない
        /// ・認証失敗時、取得イメージが更新されていない場合、認証しない
        /// 　（初回起動時のアクセス許可のポップアップを押すまでの間に同じ画像で何回も認証を行ってしまっていたため）
        /// </remarks>
        private bool _isSetFakeImage = false;

        /// <summary>
        /// 現在の取得イメージ
        /// </summary>
        private Bitmap _currentImage;

        /// <summary>
        /// なりすまし画像
        /// </summary>
        private Bitmap _fakeImage;

        /// <summary>
        /// VIDEO画像のフレームサイズ
        /// </summary>
        private System.Drawing.Size _originalFrameSize = new System.Drawing.Size(640, 480);

        /// <summary>
        /// VIDEO画像を表示する時の画像サイズ
        /// </summary>
        private System.Drawing.Size _videoImageDisplaySize = new System.Drawing.Size(0, 0);

        /// <summary>
        /// Video画像を切り取る位置
        /// </summary>
        private System.Drawing.Point _cuttingPoint = new System.Drawing.Point(0, 0);

        /// <summary>
        /// Video画像を表示する時に使用する矩形サイズ
        /// </summary>
        private System.Drawing.Size _cutSize = new System.Drawing.Size(0, 0);

        /// <summary>
        /// 実行アセンブリー
        /// </summary>
        private Assembly _assembly;

        /// <summary>
        /// イメージデータのディクショナリー
        /// </summary>
        private Dictionary<ButtonKind, ImageData> _dicImageData;

        /// <summary>
        /// 顔認証可能かどうかの状態
        /// </summary>
        private bool _faceRecognitionPossible = false;

        /// <summary>
        /// なりすまし検出処理クラス
        /// </summary>
        private FaceDetection.LivenessCheckDetection _livenessCheckDetection = null;

        /// <summary>
        /// 顔検出実行処理用カウンター
        /// </summary>
        private int _faceDetectionCounter = 0;

        /// <summary>
        /// 顔認識状態表示用タイマー
        /// </summary>
        private System.Windows.Forms.Timer _faceDetectionStateTimer = null;

        /// <summary>
        /// 顔ガイド画像用
        /// </summary>
        private Bitmap _faceGuid = null;

        /// <summary>
        /// 生体判定エラーメッセージダイアログ
        /// </summary>
        private MessageDialog _messageDialog;

        /// <summary>
        /// デキュー用ビットマップ
        /// </summary>
        private Bitmap[] _dequeueBitmap;

        /// <summary>
        /// タイムアウト検知用フラグ
        /// </summary>
        private bool _timeoutFlg = true;

        /// <summary>
        /// なりすましログ初回登録フラグ
        /// </summary>
        private bool _isFirstLivenessCheckLog = true;

        /// <summary>
        /// なりすまし画像初回保存フラグ
        /// </summary>
        private bool _isFirstFakeImage = true;

        // ▼ ADD 認証タイムアウト時のアプリ終了対応 2024/01/26 Whizz Y.Tani
        /// <summary>
        /// 顔認証タイムアウトアプリ終了タイマー
        /// </summary>
        private System.Windows.Forms.Timer _timeoutAppCloseTimer = null;
        // ▲ ADD 認証タイムアウト時のアプリ終了対応 2024/01/26 Whizz Y.Tani

        #endregion

        #region Property
        /// <summary>
        /// 現在の取得イメージプロパティ
        /// </summary>
        private Bitmap _CurrentImage
        {
            get
            {
                this._isSetImage = false;

                lock (this._currentImage)
                {
                    return new Bitmap(this._currentImage, this._width, this._height);
                }
            }
            set
            {
                // Cloneはこのプロパティに設定する前に行う必要がある。
                lock (this._currentImage)
                {
                    var old = this._currentImage;
                    this._currentImage = value;
                    if (old != null)
                    {
                        old.Dispose();
                    }
                }
                this._isSetImage = true;
            }
        }

        /// <summary>
        /// なりすまし画像イメージプロパティ
        /// </summary>
        private Bitmap _FakeImage
        {
            get
            {
                this._isSetFakeImage = false;

                lock (this._fakeImage)
                {
                    return new Bitmap(this._fakeImage, this._width, this._height);
                }
            }
            set
            {
                // Cloneはこのプロパティに設定する前に行う必要がある。
                lock (this._fakeImage)
                {
                    var old = this._fakeImage;
                    this._fakeImage = value;
                    if (old != null)
                    {
                        old.Dispose();
                    }
                }
                this._isSetFakeImage = true;
            }
        }

        private readonly object SyncRoot = new object();

        #endregion

        #region Delegate
        /// <summary>
        /// メインフォームHideデリゲート
        /// </summary>
        private delegate void MainformHideDelegate();

        #endregion

        #region Constructors

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="args"></param>
        public FaceClientFormMain(Arguments args)
        {
            InitializeComponent();

            this.staffId.Text = string.Empty;
            this.staffName.Text = string.Empty;
            this._isFlipHorizon = eDoktor.Common.Configuration.AppSetting("IsFlipHorizon", true);
            this._isTransitionRegist = eDoktor.Common.Configuration.AppSetting("IsTransitionRegist", false);
            this._viewConsentScreen = eDoktor.Common.Configuration.AppSetting("ViewConsentScreen", false);
            this._displayMessageImageFilePath = eDoktor.Common.Configuration.AppSetting("DisplayMessageImageFilePath");
            this._returnValueWhenFaceRegisterd = eDoktor.Common.Configuration.AppSetting("ReturnValueWhenFaceRegisterd", (int)QueryUserIdResult.Unregistered);
            this._assembly = Assembly.GetExecutingAssembly();
            SetImageData(this._assembly, CST_RESOURCE_PROPERTY_NAME);

#if DEBUG_MODE
            this.staffId.Text = eDoktor.Common.Configuration.AppSetting("StaffId");
#else
#endif
            this._args = args;

            this._fullFormSize = this.Size;

            base.Text = this._args.Title; // テストツールだと、タイトルの指定がないため、Arguments.csの初期値が設定される。

            // イメージの初期化
            this._currentImage = new Bitmap(this.pictureBox1.Width, this.pictureBox1.Height);
            this._fakeImage = new Bitmap(this.pictureBox1.Width, this.pictureBox1.Height);
            this._faceDetectionStateTimer = new System.Windows.Forms.Timer();
            this._faceDetectionStateTimer.Tick += new EventHandler(DispFaceDetectionState);
            this._faceDetectionStateTimer.Interval = CST_FACE_DETECTION_STATE_DISPLAY_INTERVAL;
            this._faceDetectionStateTimer.Enabled = false;

            // ▼ ADD 認証タイムアウト時のアプリ終了対応 2024/01/26 Whizz Y.Tani
            this._timeoutAppCloseTimer = new System.Windows.Forms.Timer();
            this._timeoutAppCloseTimer.Tick += TimeoutAppCloseTimer_Tick;
            this._timeoutAppCloseTimer.Interval = eDoktor.Common.Configuration.AppSetting("TimeOutAppCloseTimer", CST_FACE_DETECTION_TIMEOUT_APP_CLOSE_TIMER_INTERVAL);
            // ▲ ADD 認証タイムアウト時のアプリ終了対応 2024/01/26 Whizz Y.Tani
        }

#endregion

#region Private Events

        /// <summary>
        /// フォームロード
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FaceClientFormMain_Load(object sender, EventArgs e)
        {
            try
            {
                eDoktor.Common.Trace.OutputDebugTrace("FaceClientFormMain_Load() call");

                this._faceRecognitionPossible = false;

                string useDeviceMonikerString = string.Empty;
                // サーバーに接続する
                if (this.ServerConnect() == true)
                {
                    // 設定値要求を行う
                    if (this.GetSettingsInfo() == true)
                    {
                        // 使用できるカメラをチェックする
                        useDeviceMonikerString = CameraInfo.GetUseDevice(_whiteList, _blackList);

                        // 使用できるカメラがない場合
                        if (string.IsNullOrEmpty(useDeviceMonikerString) == true)
                        {
                            // 使用できるカメラがありません。
                            var message = Messages.Message(MessageId.Message007);
                            eDoktor.Common.Trace.OutputErrorTrace(message);
                            FaceClientErr faceClientErr = new FaceClientErr();
                            faceClientErr.Message = message; ;
                            faceClientErr.ShowDialog(this);
                        }
                        else
                        {
                            this._faceRecognitionPossible = true;
                        }
                    }
                }

                if (this._faceRecognitionPossible == true)
                {
                    // カメラ起動
                    this._videoCaptureDevice = new VideoCaptureDevice(useDeviceMonikerString);
                    var capabilities = this._videoCaptureDevice?.VideoCapabilities?.FirstOrDefault();

                    if (capabilities == null)
                    {
                        // カメラ起動に失敗
                        var message = Messages.Message(MessageId.Message007);
                        eDoktor.Common.Trace.OutputErrorTrace(message);
                        FaceClientErr faceClientErr = new FaceClientErr();
                        faceClientErr.Message = message;
                        faceClientErr.ShowDialog(this);

                        this._faceRecognitionPossible = false;
                    }
                    else
                    {
                        eDoktor.Common.Trace.OutputTrace($"フレームサイズ 高さ={capabilities.FrameSize.Height} 幅={capabilities.FrameSize.Width} 平均フレームレイト={capabilities.AverageFrameRate} ");
                        this._originalFrameSize = new System.Drawing.Size(capabilities.FrameSize.Width, capabilities.FrameSize.Height);
                        this._videoImageDisplaySize = new System.Drawing.Size(capabilities.FrameSize.Width / 2, capabilities.FrameSize.Height);
                        this._cuttingPoint = GetRectangleCutPosition(this._originalFrameSize);
                        this._cutSize = GetRectangleDisplaySize(this._originalFrameSize);
                        int width = 0;
                        int height = 0;
                        // 余分な背景を表示しないための表示エリアのサイズ計算
                        GetImageAreaSize(this._originalFrameSize, out width, out height);
                        //**********************
                        //* 表示エリアの幅、高さ、位置を再設定
                        //**********************
                        pictureBox1.Width = width;
                        pictureBox1.Height = height;

                        // 画像が画面の真ん中付近になるように横の位置を調整
                        this.pictureBox1.Location = new System.Drawing.Point(this.Width / 2 - width / 2, this.pictureBox1.Location.Y);
                        this._height = this.pictureBox1.Height;
                        this._width = this.pictureBox1.Width;
                        this._videoCaptureDevice.VideoResolution = capabilities;
                        this._videoCaptureDevice.NewFrame += videoCaptureDevice_NewFrame;
                        this._videoCaptureDevice.Start();

                        //顔ガイド使用
                        if (this._settingsInfo.is_display_face_guide)
                        {
                            pictureBox2.Visible = true;
                            pictureBox2.Width = width;
                            pictureBox2.Height = height;

                            _faceGuid = eDoktor.Taikoban.FaceClient.Properties.Resources.Face_Guide4;
                            _faceGuid.MakeTransparent();
                            pictureBox2.Image = _faceGuid;
                            pictureBox2.BackColor = Color.Transparent;
                            pictureBox2.Parent = pictureBox1;
                            pictureBox2.Location = new System.Drawing.Point(0, 0);

                        }

                        //*******************
                        //* 顔検出開始
                        //*******************
                        picFace1.Visible = false;
                        picFace2.Visible = false;
                        this._faceDetectionCounter = 0;
                        eDoktor.Common.Trace.OutputTrace($"顔ガイド使用→ {this._settingsInfo.is_display_face_guide}");

                        //なりすまし判定エラーポップアップ
                        _messageDialog = new FaceDetection.MessageDialog();
                        _messageDialog.error_message_text.Text = this._settingsInfo.liveness_check_error_message;
                        _messageDialog.Owner = this;
                    }
                }

#if DEBUG_MODE
            // 顔認証処理
            backgroundWorker2.RunWorkerAsync();
#else
                //*****************************
                //* COMからWindowsメッセージ経由で呼ばれる時
                //*****************************
                this.ShowInTaskbar = false;
                this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;

                //*****************
                //* ここで親ウインドウの設定を行うと親も子も画面操作ができる。
                //*****************
                this.SetParentWindow();
                this.SetFormSize(0);
                this.SetFormPosition();
                this.Visible = false;
                var vaslResult = AuthScreenLoadResult.Success;
                SendScreenLoadResult(vaslResult);
#endif
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// フォームクローズ時処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FaceClientFormMain_FormClosing(object sender, FormClosingEventArgs e)
        {
            eDoktor.Common.Trace.OutputDebugTrace("FaceClientFormMain_FormClosing START");

            this.backgroundWorker2.CancelAsync();

            if (this._videoCaptureDevice != null && this._videoCaptureDevice.IsRunning)
            {
                this._videoCaptureDevice.SignalToStop();
                this._videoCaptureDevice.WaitForStop();
                this._videoCaptureDevice = null;
            }
            if (this._faceDetectionStateTimer != null)
            {
                this._faceDetectionStateTimer.Dispose();
            }
            if (this._livenessCheckDetection != null)
            {
                this._livenessCheckDetection.End();
            }

        }

        /// <summary>
        /// 新フレーム取得
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="eventArgs">イベント引数</param>
        void videoCaptureDevice_NewFrame(object sender, NewFrameEventArgs eventArgs)
        {
            Rectangle rectangle = new Rectangle();
            rectangle.X = this._cuttingPoint.X;
            rectangle.Y = this._cuttingPoint.Y;
            rectangle.Width = this._cutSize.Width;
            rectangle.Height = this._cutSize.Height;
            Bitmap targetBitmap = (Bitmap)eventArgs.Frame.Clone(rectangle, eventArgs.Frame.PixelFormat);
            this._CurrentImage = targetBitmap;

            var old = this.pictureBox1.Image;
            Bitmap tmpBitmap = new Bitmap(targetBitmap, this._width, this._height);

            //なりすまし判定を使わない設定値の場合、フレームのエンキュー・デキューを行わない
            if (_settingsInfo.is_livenessCheck)
            {
                _faceDetectionCounter++;
                //if (this._livenessCheckDetection != null && this._livenessCheckDetection._isFake && _messageDialog.Visible)
                if (this._livenessCheckDetection != null && this._livenessCheckDetection._isFake)
                {
                    if (_isFirstFakeImage)
                    {
                        _isFirstFakeImage = false;
                        this._FakeImage = (Bitmap)eventArgs.Frame.Clone(rectangle, eventArgs.Frame.PixelFormat);
                    }
                    while (true)
                    {
                        if (this._livenessCheckDetection.ImageQueue.Count <= 0)
                        {
                            this._livenessCheckDetection._isFake = false;

                            break;
                        }

                        if (this._livenessCheckDetection.ImageQueue.TryDequeue(out _dequeueBitmap) != true)
                        {
                            break;
                        }
                    }
                }
                else if (_faceDetectionCounter >= CST_FACE_DETECTION_EXECUTION_COUNTER_MAX)
                {
                    if(this._livenessCheckDetection != null)
                    {
                        //this._livenessCheckDetection.ImageQueue.Enqueue((Bitmap)eventArgs.Frame.Clone());
                        Bitmap[] queueBitmap = new Bitmap[2];
                        queueBitmap[0] = (Bitmap)targetBitmap.Clone();
                        queueBitmap[1] = (Bitmap)eventArgs.Frame.Clone();
                        this._livenessCheckDetection.ImageQueue.Enqueue(queueBitmap);
                    }
                    _faceDetectionCounter = 0;
                }
            }

            if (this._isFlipHorizon == true)
            {
                tmpBitmap.RotateFlip(RotateFlipType.RotateNoneFlipX);
            }

            this.pictureBox1.Image = new Bitmap(tmpBitmap, this._width, this._height);
            tmpBitmap.Dispose();

            if (old != null)
            {
                old.Dispose();
            }
        }

        /// <summary>
        /// BackgroundWorker 顔認証処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void backgroundWorker2_DoWork(object sender, DoWorkEventArgs e)
        {
            // 認証開始待機
            System.Threading.Thread.Sleep(this._settingsInfo.auth_start_wait_time < 0 ? 0 : this._settingsInfo.auth_start_wait_time);
            eDoktor.Common.Trace.OutputDebugTrace("バックグラウンド顔認証処理開始");

            if (this._settingsInfo.auth_mode == EnumAuthMode.Server)
            {
                eDoktor.Common.Trace.OutputDebugTrace("サーバ認証モード");
            }
            else
            {
                eDoktor.Common.Trace.OutputDebugTrace("クライアント認証モード");
            }

            if (this._userAuthMode == EnumUserAuthMode.UserSearchAuthMode)
            {
                eDoktor.Common.Trace.OutputDebugTrace("ユーザー検索認証モード");
            }
            else
            {
                eDoktor.Common.Trace.OutputDebugTrace("ユーザー指定認証モード");
            }

            var bgWorker = (BackgroundWorker)sender;
            var isEnd = false;

            while (!bgWorker.CancellationPending && isEnd == false)
            {
                var result = new LivenessCheckResult(1, false, false, false, false);

                bool isLivenessCheck = _settingsInfo.is_livenessCheck && _livenessCheckDetection != null;

                //タイムアウトなら処理終了
                if (isLivenessCheck)
                {
                    // 結果を取得
                    result = _livenessCheckDetection.LivenessCheckResult;

                    if (result.IsTimeout)
                    {
                        eDoktor.Common.Trace.OutputDebugTrace("_livenessCheckDetection.LivenessCheckResult.IsTimeout={0}", result.IsTimeout);

                        break;
                    }

                    //なりすましログ登録
                    if (_isFirstLivenessCheckLog && _livenessCheckDetection._isThumbnail && this._isSetFakeImage)
                    {
                        _isFirstLivenessCheckLog = false;
                        RequestRegisterLivenessLog(this._FakeImage);
                    }
                }

                //認証処理を行うかチェック
                if (!isLivenessCheck || isAuthByLivenessCheck(result))
                {
                    lblErrorMessage.ForeColor = Color.White;
                    lblErrorMessage.Text = string.Empty;

                    if (this._isSetImage == false)
                    {
                        // 顔認証要求送信中は処理を行わない
                        continue;
                    }

#if FACE_RECOGNITION
                    // カメラからの映像を取得
                    if (this._settingsInfo.auth_mode == EnumAuthMode.Server)
                    {
                        // サーバ認証モード
                        isEnd = this.ServerModeAuthProc(this._CurrentImage);
                    }
                    else
                    {
                        // クライアント認証モード
                        isEnd = this.ClientModeAuthProc(this._CurrentImage);
                    }

                    //判定処理を終了する
                    if (isEnd && this._livenessCheckDetection != null)
                    {
                        this._livenessCheckDetection.End();
                        //エラーメッセージダイアログを消す
                        this._faceDetectionStateTimer.Enabled = false;
                        this._messageDialog.Close();
                    }
                }

#else
 // 確認のため顔認証を止める
#endif
            }

            eDoktor.Common.Trace.OutputDebugTrace("bgWorker.CancellationPending={0}, isEnd={1}", bgWorker.CancellationPending, isEnd);

#if DEBUG_MODE
            this.Close();
#else
#endif
        }

        /// <summary>
        /// 認証処理を行うかどうかチェックする
        /// </summary>
        /// <returns>true:認証処理を行う false:認証処理を行わない</returns>
        private bool isAuthByLivenessCheck(LivenessCheckResult result)
        {
            //なりすまし判定を使用しない設定値ならそのまま認証
            if (!_settingsInfo.is_livenessCheck)
            {
                return true;
            }

            //なりすまし処理を開始していなければ認証を行う
            if (_livenessCheckDetection == null || !_livenessCheckDetection.IsExecutable())
            {
                return true;
            }

            // 顔の数が1以外の場合は認証しない
            if (result.FaceCount != 1)
            {
                if (lblErrorMessage.Text != string.Empty)
                {
                    lblErrorMessage.ForeColor = Color.White;
                    lblErrorMessage.Text = string.Empty;

                    eDoktor.Common.Trace.OutputTrace("エラーメッセージを消しました");
                }

                return false;
            }

            //正常な顔でない場合は認証しない
            //if (_livenessCheckDetection != null && _livenessCheckDetection._isInvalidFace)
            if (result.IsInvalidFace)
            {
                var message = eDoktor.Common.Configuration.AppSetting("MessageLivenessInvalidFace");
                if (lblErrorMessage.Text != message)
                {
                    lblErrorMessage.ForeColor = Color.Orange;
                    lblErrorMessage.Text = message;

                    eDoktor.Common.Trace.OutputWarningTrace("顔不正メッセージを表示しました");
                }

                return false;
            }

            //フェイク画像と判定されたときは認証しない
            //if(_livenessCheckDetection != null && (_livenessCheckDetection._isFake || _livenessCheckDetection._isCurrentFake))
            if (result.IsFake || result.IsCurrentFake)
            {
                var message = eDoktor.Common.Configuration.AppSetting("MessageLivenessCheckErrored");
                if (lblErrorMessage.Text != message)
                {
                    lblErrorMessage.ForeColor = Color.Red;
                    lblErrorMessage.Text = message;

                    eDoktor.Common.Trace.OutputWarningTrace("なりすまし判定メッセージを表示しました");
                }

                return false;
            }

            // 結果をロギング
            eDoktor.Common.Trace.OutputDebugTrace("LivenessCheckResult:FaceCount={0},IsInvalidFace={1},IsCurrentFake={2}", result.FaceCount, result.IsInvalidFace, result.IsCurrentFake);

            return true;
        }

        /// <summary>
        /// 顔検出状態を表示する。
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void DispFaceDetectionState(object sender, EventArgs e)
        {
            //なりすまし判定を使わない設定値の場合、なりすまし判定による切り替え・タイムアウト処理を行わない
            if (_settingsInfo.is_livenessCheck)
            {
                if (_livenessCheckDetection != null && _livenessCheckDetection.IsExecuting())
                {
                    // 結果を取得する
                    var result = _livenessCheckDetection.LivenessCheckResult;

                    if (_timeoutFlg && result.IsTimeout && this._messageDialog != null)
                    {
                        eDoktor.Common.Trace.OutputTrace("設定された時間操作がなかったため、タイムアウトしました。タイムアウト時間（ms） : {0}", this._settingsInfo.liveness_check_timeout);

                        // ▼ ADD メモリリーク対応 2024/01/15 Whizz Y.Tani
                        this._videoCaptureDevice.Stop();
                        // ▲ ADD メモリリーク対応 2024/01/15 Whizz Y.Tani

                        // ▼ ADD 認証タイムアウト時のアプリ終了対応 2024/01/26 Whizz Y.Tani
                        // タイムアウトタイマースタート
                        this._timeoutAppCloseTimer.Enabled = true;
                        // ▲ ADD 認証タイムアウト時のアプリ終了対応 2024/01/26 Whizz Y.Tani

                        //タイムアウトでクローズするのは一度だけ
                        _timeoutFlg = false;
                        this._faceDetectionStateTimer.Enabled = false;
                        this._livenessCheckDetection.End();
                        _messageDialog.Owner = null;
                        this._messageDialog.Close();
                        //認証のタイムアウトは認証リトライ終了の方法を使う
                        HideMainWindow();
                        FaceClientErr faceClientErr = new FaceClientErr();
                        faceClientErr.Message = this._settingsInfo.liveness_check_timeout_error_message;
                        // ▼ MOD 認証タイムアウト時のアプリ終了対応 2024/01/26 Whizz Y.Tani
                        var res = faceClientErr.ShowDialog(this);

                        // アプリ終了タイムアウト満了時はタイマースレッド側で終了通知を行うため、抜ける。
                        if (res == DialogResult.Cancel)
                        {
                            return;
                        }
                        // ▲ MOD 認証タイムアウト時のアプリ終了対応 2024/01/26 Whizz Y.Tani

                        // アプリが終了したことを通知する。
                        // ▼ MOD 2024/01/27 Y.oka
                        //SendAuthResult(AuthResult.OtherError);
                        SendAuthResult(AuthResult.Timeout);
                        // ▲ MOD 2024/01/27 Y.oka
                    }
                    else
                    {
                        if (result.FaceCount <= 0)
                        {
                            picFace1.Visible = false;
                            picFace2.Visible = false;

                            lblStatusMessage.ForeColor = Color.White;
                            lblStatusMessage.Text = string.Empty;
                            lblErrorMessage.ForeColor = Color.White;
                            lblErrorMessage.Text = string.Empty;
                        }
                        else if (result.FaceCount == 1)
                        {
                            picFace1.Visible = true;
                            picFace2.Visible = false;

                            var message = eDoktor.Common.Configuration.AppSetting("MessageFaceDetected");

                            if (lblStatusMessage.Text != message)
                            {
                                lblStatusMessage.ForeColor = Color.White;
                                lblStatusMessage.Text = message;

                                eDoktor.Common.Trace.OutputTrace("顔検出メッセージを表示しました");
                            }
                        }
                        else
                        {
                            picFace1.Visible = true;
                            picFace2.Visible = true;

                            var message = eDoktor.Common.Configuration.AppSetting("MessageMultiFacesDetected");

                            if (lblStatusMessage.Text != message)
                            {
                                lblStatusMessage.ForeColor = Color.Orange;
                                lblStatusMessage.Text = message;

                                eDoktor.Common.Trace.OutputWarningTrace("顔複数検出メッセージを表示しました");
                            }
                        }
                    }
                }
            }

            if (this._livenessCheckDetection == null || this._livenessCheckDetection.IsExecuting() != true)
            {
                return;
            }
        }

        // ▼ ADD 認証タイムアウト時のアプリ終了対応 2024/01/26 Whizz Y.Tani
        /// <summary>
        /// 顔認証タイムアウト後にアプリを終了するイベント
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void TimeoutAppCloseTimer_Tick(object sender, EventArgs e)
        {
            // タイマー停止
            this._timeoutAppCloseTimer.Enabled = false;

            // タイムアウトでアプリが終了したことを通知する。
            SendAuthResult(AuthResult.Timeout);
        }
        // ▲ ADD 認証タイムアウト時のアプリ終了対応 2024/01/26 Whizz Y.Tani

        /// <summary>
        /// キャンセルボタンクリック処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picCancel_Click(object sender, EventArgs e)
        {
            this.SendAuthResult(AuthResult.Cancel);
        }

        /// <summary>
        /// マウスカーソルがキャンセルボタン領域に入った時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picCancel_MouseEnter(object sender, EventArgs e)
        {
            try
            {
                ChangeButtonImage(picCancel, ButtonKind.Cancel, ImageData.ImageKind.MouseEnter);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// マウスカーソルがキャンセルボタン領域から抜けた時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picCancel_MouseLeave(object sender, EventArgs e)
        {
            try
            {
                ChangeButtonImage(picCancel, ButtonKind.Cancel, ImageData.ImageKind.MouseLeave);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// キャンセルボタン押下時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picCancel_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                ChangeButtonImage(picCancel, ButtonKind.Cancel, ImageData.ImageKind.MouseDown);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// キャンセルボタン戻し時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picCancel_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                ChangeButtonImage(picCancel, ButtonKind.Cancel, ImageData.ImageKind.MouseUp);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

#endregion

#region Private Method

        /// <summary>
        /// server接続処理
        /// </summary>
        /// <returns>処理結果</returns>
        private bool ServerConnect()
        {
            if (!this._requestSend.ServerConnect())
            {
                // 接続に失敗したので、エラー画面を表示する。
                FaceClientErr faceClientConnectErr = new FaceClientErr();
                faceClientConnectErr.Message = "サーバーに接続できませんでした。";
                DialogResult dialogResult = faceClientConnectErr.ShowDialog(this);

                // 閉じるボタン押下時は、メニュー画面も閉じるのでフラグを立てる
                if (dialogResult == DialogResult.Cancel)
                {
                    // 接続エラーフラグを立てる
                    // Environment.Exit(0)とするとプロセスがこの時点でKillされるのでコメントにする。
                    //Environment.Exit(0);
                    return false;
                }
                else
                {
                    //　再接続ボタン押下時は再帰的に再接続処理をコールする
                    return this.ServerConnect();
                }
            }

            return true;
        }

        /// <summary>
        /// 設定値を取得する
        /// </summary>
        /// <param name="client"></param>
        /// <returns>処理結果</returns>
        private bool GetSettingsInfo()
        {
            var res = this._requestSend.RequestGetSettingInfo(out FaceInterprocess.FaceSettingGetResData resData);

            if (res)
            {
                this._whiteList = resData.DeviceWhiteList;
                this._blackList = resData.DeviceBlackList;

                this._settingsInfo = resData.SettingInfo;
            }

            return res;
        }


        /// <summary>
        /// サーバモード認証処理
        /// </summary>
        /// <param name="frameBitmap"></param>
        /// <returns>画面終了フラグ</returns>
        private bool ServerModeAuthProc(Bitmap frameBitmap)
        {
            try
            {
#if SAVE_THE_BITMAP_USED

                frameBitmap.Save("FaceRecognitionBitmap.bmp", System.Drawing.Imaging.ImageFormat.Bmp);
#endif
                if (this._userAuthMode == EnumUserAuthMode.UserSearchAuthMode)
                {
                    // ユーザ検索認証モード
                    eDoktor.Common.Trace.OutputDebugTrace($"{DateTime.Now.ToShortTimeString()}:Call UserSearchFaceAuthRequest");

                    // ユーザー検索顔認証要求を送信する
                    var ret = this._requestSend.RequestUserSearchFaceAuth(frameBitmap, out LoginUserInfo loginUserInfo);

                    if (ret)
                    {
                        lblErrorMessage.ForeColor = Color.White;
                        lblErrorMessage.Text = string.Empty;

                        if (_settingsInfo.is_display_auth_user_daialog)
                        {
                            // 検索ユーザ確認ダイアログ表示有無(ユーザ検索認証時)で表示する設定の場合
                            using (FaceClientUserConfirm faceClientUserConfirm = new FaceClientUserConfirm(loginUserInfo))
                            {
                                // 認証ユーザ確認画面表示
                                var result = faceClientUserConfirm.ShowDialog(this);

                                if (result != DialogResult.Yes)
                                {
                                    eDoktor.Common.Trace.OutputDebugTrace("認証成功 ユーザ違い(AccountId:{0} UserId：{1} LogonId：{2} UserName：{3})"
                                        , loginUserInfo.account_id, loginUserInfo.user_id, loginUserInfo.logon_id, loginUserInfo.name);

                                    return false;
                                }
                            }
                        }

                        eDoktor.Common.Trace.OutputDebugTrace("認証成功");
#if DEBUG_MODE
#else
                        // アプリが終了したことを通知する。
                        this.SendAuthResult(AuthResult.Success, loginUserInfo.account_id);
#endif
                        return true;
                    }
                    else
                    {
                        lblErrorMessage.ForeColor = Color.Orange;
                        lblErrorMessage.Text = string.Format("{0}({1})", eDoktor.Common.Configuration.AppSetting("MessageFaceRecognitionFailed"), this._RetryCount);

                        eDoktor.Common.Trace.OutputWarningTrace("顔認証失敗メッセージを表示しました");
                    }
                }
                else
                {
                    // ユーザ指定認証モード
                    eDoktor.Common.Trace.OutputDebugTrace($"{DateTime.Now.ToShortTimeString()}:Call FaceAuthRequest");

                    // 顔認証要求を送信する
                    var ret = this._requestSend.RequestFaceAuth(this._accountId, frameBitmap);

                    if (ret)
                    {
                        lblErrorMessage.ForeColor = Color.White;
                        lblErrorMessage.Text = string.Empty;

                        eDoktor.Common.Trace.OutputDebugTrace("認証成功");
#if DEBUG_MODE
#else
                        // アプリが終了したことを通知する。
                        this.SendAuthResult(AuthResult.Success);
#endif

                        return true;
                    }
                    else
                    {
                        lblErrorMessage.ForeColor = Color.Orange;
                        lblErrorMessage.Text = string.Format("{0}({1})", eDoktor.Common.Configuration.AppSetting("MessageFaceRecognitionFailed"), this._RetryCount);

                        eDoktor.Common.Trace.OutputWarningTrace("顔認証失敗メッセージを表示しました");
                    }
                }

                return this.RetryProc();
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);

                return this.RetryProc();
            }
        }

        /// <summary>
        /// なりすましログ登録要求を送信する
        /// </summary>
        /// <param name="frameBitmap"></param>
        /// <returns>画面終了フラグ</returns>
        private void RequestRegisterLivenessLog(Bitmap frameBitmap)
        {
            try
            {
                _requestSend.RequestLivenessImageRegist(this._accountId, frameBitmap);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);

            }
        }

        /// <summary>
        /// クライアントモード認証処理
        /// </summary>
        /// <param name="frameBitmap"></param>
        /// <returns>画面終了フラグ</returns>
        private bool ClientModeAuthProc(Bitmap frameBitmap)
        {
            // TODO クライアントモードの処理
            // モードの設定が不正です
            //this.Hide();
            HideMainWindow();
            //MessageBox.Show(this, Messages.Message(MessageId.Message011));
            FaceClientErr faceClientErr = new FaceClientErr();
            faceClientErr.Message = Messages.Message(MessageId.Message011); ;
            faceClientErr.ShowDialog(this);

#if DEBUG_MODE
#else
            // アプリが終了したことを通知する。
            SendAuthResult(AuthResult.OtherError);
#endif
            return true;
        }

        /// <summary>
        /// リトライ時処理
        /// </summary>
        /// <returns>画面終了フラグ</returns>
        private bool RetryProc()
        {
            // 認証リトライ回数をカウントアップ
            this._RetryCount++;

            // 顔認証に失敗しました。
            eDoktor.Common.Trace.OutputErrorTrace(this._RetryCount.ToString() + "回" + Messages.Message(MessageId.Message017));

            if (this._settingsInfo.retry_count < this._RetryCount)
            {
                // 設定値のリトライ回数に達した場合は、認証失敗として終了処理を行う。
                eDoktor.Common.Trace.OutputDebugTrace("設定.リトライ回数: {0}", this._settingsInfo.retry_count);
                eDoktor.Common.Trace.OutputDebugTrace("リトライ回数に達したため、認証処理を終了します。");
                this._RetryCount = 0;

                // ▼ MOD メモリリーク対応 2024/01/29 Y.oka
                // 20231011　なりすまし判定止める処理追加
                //if (this._livenessCheckDetection != null)
                //{
                //    this._livenessCheckDetection.Stop();
                //}
                this._videoCaptureDevice.Stop();
                this._faceDetectionStateTimer.Enabled = false;
                // ▲ MOD メモリリーク対応 2024/01/29 Y.oka

                //this.Hide();
                HideMainWindow();

                // 顔認証に失敗しました。
                //MessageBox.Show(Messages.Message(MessageId.Message017));
                FaceClientErr faceClientErr = new FaceClientErr();
                faceClientErr.Message = Messages.Message(MessageId.Message017);
                faceClientErr.ShowDialog(this);

#if DEBUG_MODE
#else
                // アプリが終了したことを通知する。
                SendAuthResult(AuthResult.RetryOver);
#endif

                return true;
            }

            return false;
        }

        /// <summary>
        /// メインウインドを非表示にする。
        /// </summary>
        private void HideMainWindow()
        {
            if (this.InvokeRequired)
            {
                eDoktor.Common.Trace.OutputTrace("HideMainWindow()が別スレッドから呼ばれました");
                Invoke(new MainformHideDelegate(HideMainWindow));
            }
            else
            {
                this.Hide();
            }
        }

#endregion

#region Override ウインドウプロシジャー

#if DEBUG_MODE
#else
        /// <summary>
        /// ウインドウプロシジャー(受信側プログラム)
        /// </summary>
        /// <param name="m"></param>
        [SecurityPermission(SecurityAction.Demand, Flags = SecurityPermissionFlag.UnmanagedCode)]
        protected override void WndProc(ref Message m)
        {
            const int WM_SYSCOMMAND = 0x112;
            const long SC_CLOSE = 0xF060L;

            // この処理があるとフォームの閉じるボタンを押下しても閉じない。なので、FormBorderStyleプロパティをNoneにしてボタン自体非表示にしたほうがよい。
            if (m.Msg == WM_SYSCOMMAND &&
                (m.WParam.ToInt64() & 0xFFF0L) == SC_CLOSE)
            {
                return;
            }

            //            eDoktor.Common.Trace.OutputTrace(m.Msg.ToString());

            // 文字が送信されてきた場合
            if (m.Msg == WM_COPYDATA)
            {
                // COMからのメッセージ
                eDoktor.Common.Trace.OutputTrace("COPY_DATA メッセージ受信");
                var bioMessage = new BioAuthMessage.BioAuthMessage();
                var msgObject = bioMessage.ReceiveString(
                    (BioAuthMessage.BioAuthMessage.COPYDATASTRUCT)m.GetLParam(typeof(BioAuthMessage.BioAuthMessage.COPYDATASTRUCT))
                );
                if (msgObject.dataType == BioAuthMessage.BioAuthMessage.MessageCode.QueryUserId)
                {
                    // QueryUserId呼び出し受信
                    eDoktor.Common.Trace.OutputTrace("QueryUserIdを呼び出します。");
                    m.Result = QueryUserId(msgObject.data);
                }
                else if (msgObject.dataType == BioAuthMessage.BioAuthMessage.MessageCode.PerformBioAuth)
                {
                    // PerformBioAuth呼び出し受信
                    eDoktor.Common.Trace.OutputTrace("PerformBioAuthを呼び出します。");
                    m.Result = PerformBioAuth();
                }
                else if (msgObject.dataType == BioAuthMessage.BioAuthMessage.MessageCode.TerminateBioAuth)
                {
                    // TerminateBioAuth呼び出し受信
                    eDoktor.Common.Trace.OutputTrace("TerminateBioAuthを呼び出します。");
                    m.Result = TerminateBioAuth();
                }
                else if (msgObject.dataType == BioAuthMessage.BioAuthMessage.MessageCode.End)
                {
                    // End呼び出し受信
                    eDoktor.Common.Trace.OutputTrace("Endを呼び出します。");
                    m.Result = EndProgram();
                }
                else
                {
                    // その他（未定義なのでエラー）
                    eDoktor.Common.Trace.OutputTrace("未定義の要求種別の要求を受信しました。");
                    m.Result = new IntPtr(9999);
                }
                return;
            }

            base.WndProc(ref m);

        }
#endif

#endregion

#region ウインドウ プロパティ設定

        /// <summary>
        /// 親ウインドウ設定
        /// </summary>
        private void SetParentWindow()
        {
            // 引数で親windowハンドルが設定されていなければ抜ける
            if (this._args.ParentHandle == 0)
            {
                return;
            }

            // 設定済みなら抜ける
            if (this._parentSet)
            {
                return;
            }

            // 親windowを設定
            var r = NativeMethods.SetWindowLong(this.Handle, (int)NativeMethods.WindowLongFlags.GWLP_HWNDPARENT, this._args.ParentHandle);

            if (r == 0)
            {
                // エラー
                int nError = System.Runtime.InteropServices.Marshal.GetLastWin32Error();
                eDoktor.Common.Trace.OutputTrace(string.Format("親を設定 {0} LastError={1} ", this._args.ParentHandle, nError));
            }
            else
            {
                eDoktor.Common.Trace.OutputTrace(string.Format("親を設定 {0} ", this._args.ParentHandle));
            }

            // 親設定済みフラグON
            this._parentSet = true;
        }

        /// <summary>
        /// 画面のサイズを変更する。
        /// </summary>
        /// <param name="num">0: サイズを(0,0)にする。 1:フォームの元の値にする。</param>
        private void SetFormSize(int num)
        {
            if (num == 0)
            {
                // サイズを0に設定
                eDoktor.Common.Trace.OutputTrace("SetFormSize 0");
                this.Size = new System.Drawing.Size(0, 0);
            }
            else
            {
                // サイズを復元
                eDoktor.Common.Trace.OutputTrace("SetFormSize 1");
                this.Size = this._fullFormSize;
            }
        }

        /// <summary>
        /// 画面の表示位置を設定する。
        /// </summary>
        private void SetFormPosition()
        {
            this.Location = this._args.DispPoint;
        }

#endregion

#region 認証関連

        /// <summary>
        /// 指定ユーザーが登録されているかチェックする。
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <returns></returns>
        private IntPtr QueryUserId(string userId)
        {
            if (this._faceRecognitionPossible == false)
            {
                this.Close();

                return new IntPtr((int)QueryUserIdResult.UnexpectedError);
            }

            // ユーザーIDチェック
            eDoktor.Common.Trace.OutputTrace(string.Format("検索ユーザーID = {0}", userId));

            // UserIDが指定されている場合
            if (!string.IsNullOrWhiteSpace(userId))
            {
                this._userAuthMode = EnumUserAuthMode.UserSpecifyAuthMode;

                // 顔テンプレート登録確認要求を送信する
                var res = this._requestSend.RequestFaceTemplateRegistCheck(userId, out FaceInterprocess.FaceTemplateRegConfirmResData resData);

                if (!res)
                {
                    string msg = Messages.Message(MessageId.Message004);

                    // サーバーからの応答がありませんでした。
                    eDoktor.Common.Trace.OutputErrorTrace(msg);
                    //MessageBox.Show(this, msg);
                    FaceClientErr faceClientErr = new FaceClientErr();
                    faceClientErr.Message = msg;
                    faceClientErr.ShowDialog(this);
#if DEBUG_MODE
#else
                    // ▼ DELETE 終了応答修正 2023/09/24 eDoktor Y.Kihara
                    //// アプリが終了したことを通知する。
                    //SendAuthResult(AuthResult.Cancel);
                    // ▲ DELETE 終了応答修正 2023/09/24 eDoktor Y.Kihara
#endif
                    this.Close();

                    return new IntPtr((int)QueryUserIdResult.NoConnection);
                }

                // 処理結果が正常終了
                if (resData.ProcResult == EnumProcResult.Success)
                {
                    // 取得した職員IDと職員名をUIに反映する
                    staffId.Text = userId;
                    staffName.Text = resData.UserName;

                    this._accountId = resData.AccountId;

                    if (resData.RegistState == EnumRegistState.NoRegist)
                    {
                        // 応答結果が「登録なし」の場合
                        if (this._isTransitionRegist)
                        {
                            this.Hide();

                            if (this._viewConsentScreen)
                            {
                                // 同意画面を表示する
                                using (var faceInsertMessage = new FaceInsertMessage(this._displayMessageImageFilePath))
                                {
                                    eDoktor.Common.Trace.OutputDebugTrace("同意メッセージを表示");
                                    faceInsertMessage.ShowDialog(this);

                                    if (!faceInsertMessage.IsOk)
                                    {
                                        eDoktor.Common.Trace.OutputDebugTrace("→同意しない");

                                        this.Close();
                                        return new IntPtr((int)QueryUserIdResult.Unregistered);
                                    }
                                    else
                                    {
                                        // 登録画面を起動する設定の場合
                                        eDoktor.Common.Trace.OutputDebugTrace("→同意する");
                                        eDoktor.Common.Trace.OutputDebugTrace("登録画面に遷移します");
                                    }
                                }
                            }
                            else
                            {
                                eDoktor.Common.Trace.OutputDebugTrace("同意メッセージ非表示");
                            }

                            // 設定を取得
                            this.GetSettingsInfo();

                            // 登録画面を起動
                            using (var faceInsertPhotoGraphy = new FaceInsertPhotoGraphy(this._videoCaptureDevice, userId, resData.UserName, resData.AccountId, this._settingsInfo))
                            {
                                faceInsertPhotoGraphy.ShowDialog(this);

                                if (faceInsertPhotoGraphy.SuccessfulRegistration)
                                {
                                    // 登録成功
                                    if (this._returnValueWhenFaceRegisterd == 1)
                                    {
                                        // 顔認証に遷移
                                        this._livenessCheckDetection = new FaceDetection.LivenessCheckDetection(this._settingsInfo, _messageDialog);

                                        if (_settingsInfo.is_livenessCheck)
                                        {
                                            //なりすまし判定を行う設定の場合、なりすまし判定を開始する
                                            bool livenessCheckStart = _livenessCheckDetection.Start();

                                            if (!livenessCheckStart)
                                            {
                                                eDoktor.Common.Trace.OutputErrorTrace("なりすまし検出処理を開始できませんでした。");
                                                //認証クライアントを終了
                                                MessageBox.Show(Messages.Message(MessageId.Message032));
                                                // ▼ DELETE 終了応答修正 2023/09/24 eDoktor Y.Kihara
                                                //SendAuthResult(AuthResult.Cancel);
                                                // ▲ DELETE 終了応答修正 2023/09/24 eDoktor Y.Kihara
                                                this.Close();

                                                // ▼ ADD 終了応答修正 2023/09/24 eDoktor Y.Kihara
                                                return new IntPtr((int)QueryUserIdResult.UnexpectedError);
                                                // ▲ ADD 終了応答修正 2023/09/24 eDoktor Y.Kihara

                                            }
                                        }

                                        this._faceDetectionStateTimer.Enabled = true;

                                        return new IntPtr(this._returnValueWhenFaceRegisterd);
                                    }
                                    else
                                    {
                                        // 認証画面に遷移
                                        this.Close();
                                        return new IntPtr(this._returnValueWhenFaceRegisterd);
                                    }
                                }
                                else
                                {
                                    // 登録未完了
                                    this.Close();
                                    return new IntPtr((int)QueryUserIdResult.Unregistered);
                                }
                            }
                        }
                        else
                        {
                            // 処理終了
                            //MessageBox.Show(this, Messages.Message(MessageId.Message008, userId));
                            FaceClientErr faceClientErr = new FaceClientErr();
                            faceClientErr.Message = Messages.Message(MessageId.Message008, userId);
                            faceClientErr.ShowDialog(this);
                        }

#if DEBUG_MODE
#else
                        // ▼ DELETE 終了応答修正 2023/09/24 eDoktor Y.Kihara
                        //// アプリが終了したことを通知する。
                        //SendAuthResult(AuthResult.Cancel);
                        // ▲ DELETE 終了応答修正 2023/09/24 eDoktor Y.Kihara
#endif

                        this.Close();

                        // ▼ ADD 終了応答修正 2023/09/24 eDoktor Y.Kihara
                        return new IntPtr((int)QueryUserIdResult.Unregistered);
                        // ▲ ADD 終了応答修正 2023/09/24 eDoktor Y.Kihara
                    }

                    this._livenessCheckDetection = new FaceDetection.LivenessCheckDetection(this._settingsInfo, _messageDialog);

                    if (_settingsInfo.is_livenessCheck)
                    {
                        //なりすまし判定を行う設定の場合、なりすまし判定を開始する
                        bool livenessCheckStart = _livenessCheckDetection.Start();

                        if (!livenessCheckStart)
                        {
                            eDoktor.Common.Trace.OutputErrorTrace("なりすまし検出処理を開始できませんでした。");
                            //認証クライアントを終了
                            MessageBox.Show(Messages.Message(MessageId.Message032));
                            // ▼ DELETE 終了応答修正 2023/09/24 eDoktor Y.Kihara
                            //SendAuthResult(AuthResult.Cancel);
                            // ▲ DELETE 終了応答修正 2023/09/24 eDoktor Y.Kihara
                            this.Close();

                            // ▼ ADD 終了応答修正 2023/09/24 eDoktor Y.Kihara
                            return new IntPtr((int)QueryUserIdResult.UnexpectedError);
                            // ▲ ADD 終了応答修正 2023/09/24 eDoktor Y.Kihara

                        }
                    }

                    this._faceDetectionStateTimer.Enabled = true;
                }
                else
                {
                    // 処理結果が異常終了
                    var errMsg = ClientCommonProc.ErrorIdToErrorMessage(resData.ErrorId, userId);

                    if (!string.IsNullOrEmpty(errMsg))
                    {
                        //MessageBox.Show(this, errMsg);
                        FaceClientErr faceClientErr = new FaceClientErr();
                        faceClientErr.Message = errMsg;
                        faceClientErr.ShowDialog(this);
                    }

#if DEBUG_MODE
#else
                    // ▼ DELETE 終了応答修正 2023/09/24 eDoktor Y.Kihara
                    //// アプリが終了したことを通知する。
                    //SendAuthResult(AuthResult.Cancel);
                    // ▲ DELETE 終了応答修正 2023/09/24 eDoktor Y.Kihara
#endif
                    this.Close();

                    // ▼ ADD 終了応答修正 2023/09/24 eDoktor Y.Kihara
                    return new IntPtr((int)QueryUserIdResult.UnexpectedError);
                    // ▲ ADD 終了応答修正 2023/09/24 eDoktor Y.Kihara
                }
            }
            else
            {
                // ユーザー検索認証モード
                this._userAuthMode = EnumUserAuthMode.UserSearchAuthMode;

                // 職員IDと職員名のラベルは非表示とする
                label1.Visible = false;
                label2.Visible = false;
            }

            return new IntPtr((int)QueryUserIdResult.Registered);
        }

        /// <summary>
        /// 生体認証実施命令処理
        /// </summary>
        /// <returns>実施結果</returns>
        private IntPtr PerformBioAuth()
        {
            eDoktor.Common.Trace.OutputTrace("生体認証実施命令処理 開始");

            try
            {
                SetFormSize(1);
                this.Visible = true;
                Application.DoEvents();

                // 画面アクティベイト
                this.Activate();
                // 顔認証処理
                backgroundWorker2.RunWorkerAsync();

                return new IntPtr((int)PerformBioAuthResult.Success);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
                return new IntPtr((int)PerformBioAuthResult.UnexpectedError);
            }
            // 画面表示
        }

        /// <summary>
        /// 生体認証中止命令処理
        /// </summary>
        /// <returns>実施結果</returns>
        private IntPtr TerminateBioAuth()
        {
            try
            {
                eDoktor.Common.Trace.OutputTrace("生体認証中止命令処理 開始");

                //// ユーザーIDクリア
                //this._logonId = null;
                //// 結果送信フラグをON(キャンセルで送る)
                //this._resultNotification = true;
                //// タイムアウトタイマー破棄
                //DisposeAuthOperationTimeoutTimer();
                //// 認証ボタン不可
                //btnSubmit.Enabled = false;
                //// メッセージクリア
                //DisplayGuidanceMessage(string.Empty, false);
                //// 非表示
                //this.Hide();
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
            return new IntPtr((int)TerminateBioAuthResult.Success);
        }

        /// <summary>
        /// プログラム終了命令処理
        /// </summary>
        /// <returns>実施結果(常に0)</returns>
        private IntPtr EndProgram()
        {
            eDoktor.Common.Trace.OutputTrace("プログラム終了命令処理 開始");

            // 閉じる
            this.Close();

            return new IntPtr((int)EndResult.Success);
        }


        /// <summary>
        /// 画面ロード完了をインターフェース画面に通知する。
        /// </summary>
        /// <param name="result">結果</param>
        private void SendScreenLoadResult(AuthScreenLoadResult result)
        {
            try
            {
                if (this._args.InterfaceHandle != 0)
                {
                    var msg = string.Format("画面ロード完了を上位に通知します。結果={0}", result);
                    eDoktor.Common.Trace.OutputTrace(msg);
                    var retCode = (int)result;
                    NativeMethods.SendMessage(new System.IntPtr(this._args.InterfaceHandle), WM_APP_AUTH_FORM_LOADED, retCode, 0);
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// Auth認証結果をインターフェース画面に通知する。
        /// </summary>
        /// <param name="result">認証結果</param>
        /// <param name="accountId">アカウントID</param>
        private void SendAuthResult(AuthResult result, int accountId)
        {
            try
            {
                eDoktor.Common.Trace.OutputDebugTrace("handle={0}", System.Diagnostics.Process.GetCurrentProcess().MainWindowHandle);
                eDoktor.Common.Trace.OutputTrace(string.Format("認証結果を上位に通知します。結果={0} アカウントID={1}", result, accountId));
                NativeMethods.SendMessage(new IntPtr(this._args.InterfaceHandle), WM_APP_MSG_2, (int)result, accountId);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }


        /// <summary>
        /// Auth認証結果をインターフェース画面に通知する。
        /// </summary>
        /// <param name="result">結果</param>
        private void SendAuthResult(AuthResult result)
        {
            try
            {
                eDoktor.Common.Trace.OutputDebugTrace("handle={0}", System.Diagnostics.Process.GetCurrentProcess().MainWindowHandle);
                eDoktor.Common.Trace.OutputTrace(string.Format("認証結果を上位に通知します。結果={0}", result));
                NativeMethods.SendMessage(new IntPtr(this._args.InterfaceHandle), WM_APP_MSG_2, (int)result, 0);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

#endregion

#region 顔画像表示用の計算など
        /// <summary>
        /// 余分な背景を表示しないための顔画像表示用エリアのサイズを取得する。
        /// </summary>
        /// <param name="videoImageSize">Video画像のイメージサイズ</param>
        /// <param name="width">表示用イメージエリアの幅</param>
        /// <param name="height">表示用イメージエリアの高さ</param>
        private void GetImageAreaSize(System.Drawing.Size videoImageSize, out int width, out int height)
        {
            if (videoImageSize.Width == 640 && videoImageSize.Height == 480)
            {
                // VGAの時
                width = CST_MAX_FACE_AREA_WIDTH / 2;
                height = (int)((double)width * 1.2);
            }
            else if (videoImageSize.Width == 1920 && videoImageSize.Height == 1080)
            {
                // フルハイビジョンの時
                width = CST_MAX_FACE_AREA_WIDTH / 2;
                height = (int)((double)width * (double)9 / (double)8); ;
            }
            else
            {
                // その他の時
                int tmpWidth = CST_MAX_FACE_AREA_WIDTH / 2;
                int tmpHeight = CST_MAX_FACE_AREA_HEIGHT;
                System.Drawing.Size tmpSize = new System.Drawing.Size(videoImageSize.Width / 2, videoImageSize.Height);
                System.Drawing.Point dummyLocation = new System.Drawing.Point(0,0);
                ClientCommonProc.PictureBoxResize(tmpSize, ref tmpWidth, ref tmpHeight, ref dummyLocation);
                width = tmpWidth;
                height = tmpHeight;
            }
        }

        /// <summary>
        /// Video画像を切り取る位置を取得する。
        /// </summary>
        /// <param name="videoImageSize">Videoイメージサイズ</param>
        /// <returns>切り取る位置</returns>
        private System.Drawing.Point GetRectangleCutPosition(System.Drawing.Size videoImageSize)
        {
            System.Drawing.Point cuttingPoint;
            if (videoImageSize.Width == 640 && videoImageSize.Height == 480)
            {
                // VGAの時
                int x = 640 / 4;
                int y = videoImageSize.Height / 5;
                cuttingPoint = new System.Drawing.Point(x, y);
            }
            else if (videoImageSize.Width == 1920 && videoImageSize.Height == 1080)
            {
                // フルハイビジョンの時
                int x = 1920 / 4;
                int y = 0;
                cuttingPoint = new System.Drawing.Point(x, y);
            }
            else
            {
                // その他の時
                int x = videoImageSize.Width / 4;
                int y = 0;
                cuttingPoint = new System.Drawing.Point(x, y);
            }
            return cuttingPoint;
        }

        /// <summary>
        /// Videoイメージの中で表示に使用する矩形サイズを取得する。
        /// </summary>
        /// <param name="videoImageSize">Videoイメージ</param>
        /// <returns>表示に使用する矩形サイズ</returns>
        private System.Drawing.Size GetRectangleDisplaySize(System.Drawing.Size videoImageSize)
        {
            System.Drawing.Point cuttingPoint = GetRectangleCutPosition(videoImageSize);
            int width = cuttingPoint.X * 2;
            int height = videoImageSize.Height - cuttingPoint.Y;
            return new System.Drawing.Size(width, height);
        }

#endregion

#region 表示用イメージデータの設定など
        /// <summary>
        /// pictureBoxボタン用のイメージデータの設定を行う。
        /// </summary>
        /// <param name="assembly">リソースの属するアセンブリー</param>
        /// <param name="resourceName">リソース名</param>
        private void SetImageData(Assembly assembly, string resourceName)
        {
            this._dicImageData = new Dictionary<ButtonKind, ImageData>();
            var imageData1 = new ImageData(this._assembly, CST_RESOURCE_PROPERTY_NAME);
            imageData1.AddImageData(ImageData.ImageKind.Normal, "Cancel_button_off");
            imageData1.AddImageData(ImageData.ImageKind.MouseEnter, "Cancel_button_hover");
            imageData1.AddImageData(ImageData.ImageKind.MouseLeave, "Cancel_button_off");
            imageData1.AddImageData(ImageData.ImageKind.MouseDown, "Cancel_button_on");
            imageData1.AddImageData(ImageData.ImageKind.MouseUp, "Cancel_button_off");
            this._dicImageData.Add(ButtonKind.Cancel, imageData1);
        }

        /// <summary>
        /// PictureBoxボタンのイメージを指定された種類に変更する。
        /// </summary>
        /// <param name="picBox">変更対象PictureBox</param>
        /// <param name="buttonKind">変更対象ボタンタイプ</param>
        /// <param name="imageKind">イメージの種類</param>
        private void ChangeButtonImage(PictureBox picBox, ButtonKind buttonKind, ImageData.ImageKind imageKind)
        {
            var oldImage = picBox.Image;
            picBox.Image = this._dicImageData[buttonKind].GetImage(imageKind);
            if (oldImage != null)
            {
                oldImage.Dispose();
            }
        }

        #endregion

        #region Private Enums

        /// <summary>
        /// PerformBioAuth命令に対する結果
        /// </summary>
        private enum PerformBioAuthResult
        {
            /// <summary>成功</summary>
            Success = 1,
            /// <summary>ユーザーID未指定</summary>
            NoUserId = 3,
            /// <summary>エラー</summary>
            UnexpectedError = 99,
        }

        /// <summary>
        /// TerminateBioAuth命令に対する結果
        /// </summary>
        private enum TerminateBioAuthResult
        {
            /// <summary>成功</summary>
            Success = 1,
            /// <summary>開始されていない</summary>
            NotStarted = 2,
            /// <summary>エラー</summary>
            UnexpectedError = 99,
        }

        /// <summary>
        /// QueryUserId命令に対する結果
        /// </summary>
        private enum QueryUserIdResult : int
        {
            /// <summary>登録済み</summary>
            Registered = 1,
            /// <summary>未登録</summary>
            Unregistered = 2,
            /// <summary>サーバーと通信できず</summary>
            NoConnection = 3,
            /// <summary>すでに実行済み</summary>
            AlreadyCheck = 4,
            /// <summary>開始されていない</summary>
            NotStarted = 5,
            /// <summary>エラー</summary>
            UnexpectedError = 99,
        }

        /// <summary>
        /// End命令に対する結果
        /// </summary>
        private enum EndResult
        {
            /// <summary>成功</summary>
            Success = 1,
            /// <summary>開始されていない</summary>
            NotStarted = 2,
            /// <summary>エラー</summary>
            UnexpectedError = 99,
        }

        /// <summary>
        /// パスワード認証結果
        /// </summary>
        private enum AuthResult
        {
            /// <summary>認証成功</summary>
            Success = 2,
            /// <summary>認証中止</summary>
            Cancel = 3,
            /// <summary>認証失敗(リトライオーバー)</summary>
            RetryOver = 4,
            /// <summary>認証失敗(タイムアウト)</summary>
            Timeout = 5,
            /// <summary>サーバー接続エラー(リトライオーバー)</summary>
            ServerError = 6,
            /// <summary>その他エラー(リトライオーバー)</summary>
            OtherError = 7,
        }

        /// <summary>
        /// Auth認証画面ロード結果
        /// </summary>
        private enum AuthScreenLoadResult
        {
            /// <summary>Auth認証画面ロード完了</summary>
            Success = 1,
        }

        /// <summary>
        /// イメージデータ用のボタンタイプ
        /// </summary>
        private enum ButtonKind
        {
            /// <summary>
            /// キャンセルボタン
            /// </summary>
            Cancel = 0,
        }
        #endregion
    }
}
