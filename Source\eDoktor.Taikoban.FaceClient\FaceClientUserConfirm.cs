﻿using System;
using System.Collections.Generic;
using System.Resources;
using System.Reflection;
using System.Security.Permissions;
using System.Windows.Forms;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktorTaikoban.FaceClientCommon;
using eDoktor.Taikoban.FaceImageCntl;

namespace eDoktor.Taikoban.FaceClient
{
    /// <summary>
    /// 認証ユーザ確認画面
    /// </summary>
    public partial class FaceClientUserConfirm : Form
    {
        #region enum
        /// <summary>
        /// イメージデータ用のボタンタイプ
        /// </summary>
        private enum ButtonKind
        {
            /// <summary>はいボタン</summary>
            Yes = 0,
            /// <summary>いいえボタン</summary>
            No = 1,
        }

        #endregion

        #region Private Const
        private const string CST_RESOURCE_PROPERTY_NAME = "eDoktor.Taikoban.FaceClient.Properties.Resources";
        #endregion

        #region Private Fields
        /// <summary>
        /// 実行アセンブリー
        /// </summary>
        private Assembly _assembly;

        /// <summary>
        /// 要求送信クラス
        /// </summary>
        RequestSendClass _requestSend = new RequestSendClass();

        /// <summary>
        /// イメージデータのディクショナリー
        /// </summary>
        private Dictionary<ButtonKind, ImageData> _dicImageData;

        /// <summary>
        /// 現在選択されているボタン
        /// </summary>
        private ButtonKind _selectedButton;

        /// <summary>
        /// キー入力ビジーフラグ
        /// </summary>
        private bool _keyBusy = false;
        #endregion

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="staffId">職員ID</param>
        /// <param name="staffName">職員名</param>
        public FaceClientUserConfirm(LoginUserInfo loginUserInfo)
        {
            InitializeComponent();
            this._assembly = Assembly.GetExecutingAssembly();
            SetImageData(this._assembly, CST_RESOURCE_PROPERTY_NAME);
            this._selectedButton = ButtonKind.Yes;

            this.DialogResult = DialogResult.No;
            this.lblConfirmMessage.Text = Messages.Message(MessageId.Message030);
            this.label_staffid.Text = loginUserInfo.logon_id;
            this.label_staffName.Text = loginUserInfo.name;
        }

        [SecurityPermission(SecurityAction.Demand, Flags = SecurityPermissionFlag.UnmanagedCode)]
        protected override void WndProc(ref Message m)
        {
            const int WM_NCLBUTTONDBLCLK = 0xA3;

            if (m.Msg == WM_NCLBUTTONDBLCLK)
            {
                //非クライアント領域がダブルクリックされた時
                m.Result = IntPtr.Zero;
                return;
            }

            base.WndProc(ref m);
        }

        /// <summary>
        /// フォームロード
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FaceClientUserConfirm_Load(object sender, EventArgs e)
        {
            if (this._selectedButton == ButtonKind.Yes)
            {
                picYes.Image = this._dicImageData[ButtonKind.Yes].GetImage(ImageData.ImageKind.Choosing);
                picNo.Image = this._dicImageData[ButtonKind.No].GetImage(ImageData.ImageKind.Normal);
            }
            else
            {
                picYes.Image = this._dicImageData[ButtonKind.Yes].GetImage(ImageData.ImageKind.Normal);
                picNo.Image = this._dicImageData[ButtonKind.No].GetImage(ImageData.ImageKind.Choosing);
            }
        }

        /// <summary>
        /// 「はい」ボタン押下時
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picYes_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Yes;
            this.Close();
        }

        /// <summary>
        /// 「はい」ボタンマウスEnterイベント処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picYes_MouseEnter(object sender, EventArgs e)
        {
            try
            {
                ChangeButtonImage(picYes, ButtonKind.Yes, ImageData.ImageKind.MouseEnter);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 「はい」ボタンマウスLeaveイベント処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picYes_MouseLeave(object sender, EventArgs e)
        {

            try
            {
                var oldImage = picYes.Image;
                if (this._selectedButton == ButtonKind.Yes)
                {
                    ChangeButtonImage(picYes, ButtonKind.Yes, ImageData.ImageKind.Choosing);
                }
                else
                {
                    ChangeButtonImage(picYes, ButtonKind.Yes, ImageData.ImageKind.MouseLeave);
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 「はい」ボタンマウスUpイベント処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picYes_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                ChangeButtonImage(picYes, ButtonKind.Yes, ImageData.ImageKind.MouseUp);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 「はい」ボタンマウスDownイベント処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picYes_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                ChangeButtonImage(picYes, ButtonKind.Yes, ImageData.ImageKind.MouseDown);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 「いいえ」ボタン押下時
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picNo_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 「いいえ」ボタンマウスEnterイベント処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picNo_MouseEnter(object sender, EventArgs e)
        {
            try
            {
                ChangeButtonImage(picNo, ButtonKind.No, ImageData.ImageKind.MouseEnter);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 「いいえ」ボタンマウスLeaveイベント処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picNo_MouseLeave(object sender, EventArgs e)
        {
            try
            {
                if (this._selectedButton == ButtonKind.No)
                {
                    ChangeButtonImage(picNo, ButtonKind.No, ImageData.ImageKind.Choosing);
                }
                else
                {
                    ChangeButtonImage(picNo, ButtonKind.No, ImageData.ImageKind.MouseLeave);
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 「いいえ」ボタンマウスUpイベント処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picNo_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                ChangeButtonImage(picNo, ButtonKind.No, ImageData.ImageKind.MouseUp);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 「いいえ」ボタンマウスDownイベント処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picNo_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                ChangeButtonImage(picNo, ButtonKind.No, ImageData.ImageKind.MouseDown);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 画面のキー受付(キーダウン)処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void FaceClientUserConfirm_KeyDown(object sender, KeyEventArgs e)
        {
            if (this._keyBusy == true)
            {
                return;
            }
            if (e.KeyCode == Keys.Tab)
            {
                if (this._selectedButton == ButtonKind.Yes)
                {
                    this._selectedButton = ButtonKind.No;
                    // 「はい」ボタンのイメージ処理
                    ChangeButtonImage(picYes, ButtonKind.Yes, ImageData.ImageKind.Normal);
                    // 「いいえ」ボタンのイメージ処理
                    ChangeButtonImage(picNo, ButtonKind.No, ImageData.ImageKind.Choosing);
                }
                else
                {
                    this._selectedButton = ButtonKind.Yes;
                    // 「はい」ボタンのイメージ処理
                    ChangeButtonImage(picYes, ButtonKind.Yes, ImageData.ImageKind.Choosing);
                    // 「いいえ」ボタンのイメージ処理
                    ChangeButtonImage(picNo, ButtonKind.No, ImageData.ImageKind.Normal);
                }
            }
            else if (e.KeyCode == Keys.Enter)
            {
                if (this._selectedButton == ButtonKind.Yes)
                {
                    // 「いいえ」ボタンの表示を標準に戻す(ホバーのイメージがあるかもしれないので)
                    ChangeButtonImage(picNo, ButtonKind.No, ImageData.ImageKind.Normal);
                    // 「はい」ボタンの表示をマウスDownにする
                    ChangeButtonImage(picYes, ButtonKind.Yes, ImageData.ImageKind.MouseDown);
                    this._keyBusy = true;
                    Application.DoEvents();

                    System.Threading.Thread.Sleep(100);
                    this.DialogResult = DialogResult.Yes;
                    this.Close();
                }
                else
                {
                    // 「はい」ボタンの表示を標準に戻す(ホバーのイメージがあるかもしれないので)
                    ChangeButtonImage(picYes, ButtonKind.Yes, ImageData.ImageKind.Normal);
                    // 「はい」ボタンの表示をマウスDownにする
                    ChangeButtonImage(picNo, ButtonKind.No, ImageData.ImageKind.MouseDown);
                    this._keyBusy = true;
                    Application.DoEvents();

                    System.Threading.Thread.Sleep(100);
                    this.Close();
                }
            }
        }

        #region 表示用イメージデータの設定など
        /// <summary>
        /// pictureBoxボタン用のイメージデータの設定を行う。
        /// </summary>
        /// <param name="assembly">リソースの属するアセンブリー</param>
        /// <param name="resourceName">リソース名</param>
        private void SetImageData(Assembly assembly, string resourceName)
        {
            this._dicImageData = new Dictionary<ButtonKind, ImageData>();
            var imageData1 = new ImageData(this._assembly, CST_RESOURCE_PROPERTY_NAME);
            imageData1.AddImageData(ImageData.ImageKind.Normal, "Yes_button_off");
            imageData1.AddImageData(ImageData.ImageKind.MouseEnter, "Yes_button_hover");
            imageData1.AddImageData(ImageData.ImageKind.MouseLeave, "Yes_button_off");
            imageData1.AddImageData(ImageData.ImageKind.MouseDown, "Yes_button_hover");
            imageData1.AddImageData(ImageData.ImageKind.MouseUp, "Yes_button_off");
            imageData1.AddImageData(ImageData.ImageKind.Choosing, "Yes_button_select");
            this._dicImageData.Add(ButtonKind.Yes, imageData1);

            var imageData2 = new ImageData(this._assembly, CST_RESOURCE_PROPERTY_NAME);
            imageData2.AddImageData(ImageData.ImageKind.Normal, "No_button_off");
            imageData2.AddImageData(ImageData.ImageKind.MouseEnter, "No_button_hover");
            imageData2.AddImageData(ImageData.ImageKind.MouseLeave, "No_button_off");
            imageData2.AddImageData(ImageData.ImageKind.MouseDown, "No_button_hover");
            imageData2.AddImageData(ImageData.ImageKind.MouseUp, "No_button_off");
            imageData2.AddImageData(ImageData.ImageKind.Choosing, "No_button_select");
            this._dicImageData.Add(ButtonKind.No, imageData2);
        }

        /// <summary>
        /// PictureBoxボタンのイメージを指定された種類に変更する。
        /// </summary>
        /// <param name="picBox">変更対象PictureBox</param>
        /// <param name="buttonKind">変更対象ボタンタイプ</param>
        /// <param name="imageKind">イメージの種類</param>
        private void ChangeButtonImage(PictureBox picBox, ButtonKind buttonKind, ImageData.ImageKind imageKind)
        {
            var oldImage = picBox.Image;
            picBox.Image = this._dicImageData[buttonKind].GetImage(imageKind);
            if (oldImage != null)
            {
                oldImage.Dispose();
            }
        }
        #endregion

    }
}
