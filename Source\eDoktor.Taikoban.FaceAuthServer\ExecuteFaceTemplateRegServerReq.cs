﻿#define LICENCE_ENABLED

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSDK;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using eDoktor.Taikoban.FaceAuthTemplateUpdateJudge;
using eDoktor.Taikoban.FaceInterprocess;
using GFRL.FaceRecognition;
using GFRL.FaceSearch;

namespace eDoktor.Taikoban.FaceAuthServer
{
    /// <summary>
    /// 顔テンプレート登録要求(サーバ認証モード)
    /// </summary>
    public class ExecuteFaceTemplateRegServerReq
    {
        /// <summary>
        /// データベース
        /// </summary>
        DB _db;

        /// <summary>
        /// 設定情報
        /// </summary>
        SettingsInfo _settingInfo;

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="database">データベース</param>
        /// <param name="settingInfo">設定情報</param>
        public ExecuteFaceTemplateRegServerReq(Database database, SettingsInfo settingInfo)
        {
            this._db = new DB(database);
            this._settingInfo = settingInfo;
        }

        /// <summary>
        /// 顔テンプレート登録要求(サーバ認証モード)コマンドに対する受信処理です
        /// </summary>
        /// <param name="client">コマンド送信もとのクライアント情報</param>
        /// <param name="packet">受信コマンドの内容</param>
        public XElement Execute(FaceInterprocess.Packet packet)
        {
            try
            {
                if (this._settingInfo.auth_mode == EnumAuthMode.Client)
                {
                    // ① 設定．認証モードがクライアントモードの場合、「モード違い」で「異常終了」とする。
                    Trace.OutputErrorTrace(Messages.Message(MessageId.Message011));

                    return this.SetFailElem(EnumErrorId.DifferentMode);
                }

                var isCorrectData = this.GetRequestData(packet, out FaceTemplateRegServerReqData requestData);

                if (isCorrectData == false)
                {
                    Trace.OutputErrorTrace("ExecuteCmdQueryFaceSettingGetReq：受信データが不正です。フォーマットエラーを返します。");

                    return this.SetFailElem(EnumErrorId.InputDataErr);
                }

#if LICENCE_ENABLED
                var createTmplateResult = this.CreateTemplate(requestData, requestData.AccountId, out FaceFeatureProperty faceFeature);

                if (createTmplateResult != EnumErrorId.NoErr)
                {
                    return this.SetFailElem(createTmplateResult);
                }

#endif // LICENCE_ENABLED

                var isGetTemplate = this._db.GetTemplateData(requestData.AccountId, out List<TemplateInfo> templateDataInfoList);

                if (isGetTemplate == false)
                {
                    return this.SetFailElem(EnumErrorId.DbErr);
                }

                // ④ DBから対象職員IDの顔テンプレートデータリストの保持数を取得する。
                // ⑤ 保持数が設定．顔テンプレート登録上限に達しているの場合、「保持数オーバー」で「異常終了」とする。
                if (templateDataInfoList.Count >= this._settingInfo.face_template_regist_max_count)
                {
                    // 登録あり（上限）
                    Trace.OutputDebugTrace(Messages.Message(MessageId.Message009));

                    return this.SetFailElem(EnumErrorId.HoldOver);
                }

                // 職員IDと取得した顔テンプレートでサーバに顔テンプレート登録要求を行う。
                return this.InsertTemplate(faceFeature, templateDataInfoList, requestData.AccountId);
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return this.SetFailElem(EnumErrorId.Exception);
            }
        }

        /// <summary>
        /// リクエストデータ取得
        /// </summary>
        /// <param name="packet">受信コマンドの内容</param>
        /// <param name="requestData">リクエストデータ</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        private bool GetRequestData(eDoktor.Taikoban.FaceInterprocess.Packet packet, out FaceTemplateRegServerReqData requestData)
        {
            requestData = new FaceTemplateRegServerReqData();

            try
            {
                requestData.ToObject(packet.Data);

                return true;
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return false;
            }
        }

        /// <summary>
        /// 成功時応答データ作成
        /// </summary>
        /// <returns></returns>
        private XElement SetSuccessElem()
        {
            var elementData = new ElementDataCreate();
            elementData.CreateElem(nameof(FaceTemplateRegServerResData.ProcResult), (int)EnumProcResult.Success);
            elementData.CreateElem(nameof(FaceTemplateRegServerResData.ErrorId), (int)EnumErrorId.NoErr);

            Trace.OutputDebugTrace(elementData.StrElements);

            return elementData.Elements;
        }

        /// <summary>
        /// 失敗時応答データ作成
        /// </summary>
        /// <param name="errorId">エラーID</param>
        /// <returns></returns>
        private XElement SetFailElem(EnumErrorId errorId)
        {
            var elementData = new ElementDataCreate();
            elementData.CreateElem(nameof(FaceTemplateRegServerResData.ProcResult), (int)EnumProcResult.Fail);
            elementData.CreateElem(nameof(FaceTemplateRegServerResData.ErrorId), (int)errorId);

            Trace.OutputDebugTrace(elementData.StrElements);

            return elementData.Elements;
        }

        /// <summary>
        /// テンプレート作成
        /// </summary>
        /// <param name="requestData"></param>
        /// <param name="accountId">アカウントID</param>
        /// <param name="faceFeature">登録顔情報</param>
        /// <returns></returns>
        private EnumErrorId CreateTemplate(FaceTemplateRegServerReqData requestData, int accountId, out FaceFeatureProperty faceFeature)
        {
            faceFeature = null;

            // ② 顔認証SDKの顔サーチ処理、サムネイル取得処理
            var sdkFace = new SDKFaceAuth(this._settingInfo);

            // 画像データから顔検出を行い、検出結果から、サムネイル画像を取得する
            var faceSearchResult = sdkFace.GetThumbnail(requestData.Image);

            if (faceSearchResult == null)
            {
                // 顔サーチ結果がNGの場合、「顔サーチNG」で「異常終了」とする。
                Trace.OutputErrorTrace(Messages.Message(MessageId.Message012));
                return EnumErrorId.FaceSearchNG;
            }

            // ③ 取得したサムネイルで顔認証SDKでの顔テンプレート作成処理を行う。
            faceFeature = sdkFace.TemplateCreate(faceSearchResult.FaceThumbnail);

            if (faceFeature == null)
            {
                // 顔テンプレートが取得できなかった場合、「顔テンプレート作成失敗」で「異常終了」とする。
                Trace.OutputErrorTrace(Messages.Message(MessageId.Message015));
                return EnumErrorId.FaceTemplateCreateFail;
            }

            // テンプレート作成に成功した場合、テンプレート閾値とマスク装着度が登録する閾値を満たしているかチェックする。
            var templateUpdateJudge = new TemplateUpdateJudge();
            bool checkResult = templateUpdateJudge.TemplateThresholdCheck(faceFeature.TemplateQuality, faceFeature.MaskScore, this._settingInfo);

            // 登録対象
            if (!checkResult)
            {
                // 閾値に満たしていない場合、「入力データ不正」で「異常終了」とする。
                Trace.OutputDebugTrace($"登録に失敗しましたしました。アカウントID:{accountId}");
                return EnumErrorId.NoUpdate;
            }

            return EnumErrorId.NoErr;
        }

        /// <summary>
        /// テンプレート登録処理
        /// </summary>
        /// <param name="faceFeature">登録顔情報</param>
        /// <param name="templateDataInfoList">対象ユーザの顔テンプレートリスト</param>
        /// <param name="accountId">アカウントID</param>
        /// <returns></returns>
        private XElement InsertTemplate(FaceFeatureProperty faceFeature, List<TemplateInfo> templateDataInfoList, int accountId)
        {
            // ▼ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
            //ServerCommonProc.GetInsertTemplteId(this._settingInfo.face_template_regist_max_count, templateDataInfoList, out int templateId);
            ServerCommonProc.GetInsertTemplteId(this._settingInfo.face_template_regist_max_count, templateDataInfoList, faceFeature.MaskScore, out int templateId);
            // ▲ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara

            Trace.OutputDebugTrace("テンプレート登録");
            Trace.OutputDebugTrace("insert templateId : {0}", templateId);
            var insertResult = false;

#if LICENCE_ENABLED
            // ⑥ 顔テンプレート、テンプレート評価値と職員IDを紐付けてDBに登録する。
            // ▼ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
            //insertResult = this._db.InsertTemplateData(accountId, templateId, faceFeature.TemplateData, faceFeature.TemplateQuality);
            insertResult = this._db.InsertTemplateData(accountId, templateId, faceFeature.TemplateData, faceFeature.TemplateQuality, faceFeature.MaskScore);
            // ▲ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
#else // LICENCE_ENABLED
                insertResult = this._db.InsertTemplateData(loginUserInfo.account_id, templateId, requestData.Image, 0);
#endif // LICENCE_ENABLED

            if (insertResult)
            {
                // 登録成功
                Trace.OutputDebugTrace($"登録が完了しました。アカウントID:{accountId},テンプレートID:{templateId}");
                return this.SetSuccessElem();
            }
            else
            {
                // 登録失敗
                Trace.OutputDebugTrace($"登録に失敗しました。アカウントID:{accountId},テンプレートID:{templateId}");
                return this.SetFailElem(EnumErrorId.DbErr);
            }
        }
    }
}
