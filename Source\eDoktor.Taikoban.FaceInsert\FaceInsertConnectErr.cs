﻿using System;
using System.Windows.Forms;

namespace eDoktor.Taikoban.FaceInsert
{
    /// <summary>
    /// サーバー接続失敗画面
    /// </summary>
    public partial class FaceInsertConnectErr : Form
    {
        /// <summary>
        /// ボタンアクションクラス
        /// </summary>
        BtnImageCahnge _btnImageCahnge = new BtnImageCahnge();

        /// <summary>
        /// コンストラクタ
        /// </summary>
        public FaceInsertConnectErr()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 再接続ボタン押下処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnConnect_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 閉じるボタン押下処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnCloses_Click(object sender, EventArgs e)
        {
            this.Close();
        }


        /// <summary>
        /// マウスカーソルがボタン領域に入った時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseEnter(object sender, EventArgs e)
        {
            _btnImageCahnge.btn_MouseEnter(sender, e);
        }

        /// <summary>
        /// マウスカーソルがボタン領域から抜けた時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseLeave(object sender, EventArgs e)
        {
            _btnImageCahnge.btn_MouseLeave(sender, e);
        }

        /// <summary>
        /// ボタン押下時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            _btnImageCahnge.btn_MouseDown(sender, e);
        }
    }
}
