﻿//#define DEBUG_MODE // デバッグしたい場合はこちらを有効にすること

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

using eDoktor.Common;

namespace eDoktor.Taikoban.FaceClient
{
    static class Program
    {
#if DEBUG_MODE
        /// <summary>
        /// アプリケーションのメイン エントリ ポイントです。
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                string errmsg = string.Empty;
                EnvironmentSetting.Set();
                eDoktor.Common.Trace.OutputDebugTrace("顔認証画面開始");

                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.Run(new FaceClientFormMain(new Arguments()));

            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
            finally
            {
                eDoktor.Common.Trace.OutputTrace("顔認証画面終了");
            }
        }
#else
        /// <summary>
        /// アプリケーションのメイン エントリ ポイントです。
        /// </summary>
        [STAThread]
        static void Main(string[] args)
        {
            try
            {
                string errmsg = string.Empty;
                EnvironmentSetting.Set();
                eDoktor.Common.Trace.OutputDebugTrace("顔認証画面開始");

                Trace.OutputDebugTrace("args.length : {0}", args.Length);
                
                Arguments arguments = new Arguments();
                if (arguments.AnalyzeArguments(args) != true)
                {
                    MessageBox.Show("コマンドライン引数が不正です。");
                    return;
                }

                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.Run(new FaceClientFormMain(arguments));

            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
            finally
            {
                eDoktor.Common.Trace.OutputTrace("顔認証画面終了");
            }
        }
#endif
    }
}
