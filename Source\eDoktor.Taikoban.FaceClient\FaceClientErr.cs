﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Resources;
using System.Reflection;
using System.Security.Permissions;
using System.Windows.Forms;
using eDoktor.Taikoban.FaceImageCntl;

namespace eDoktor.Taikoban.FaceClient
{
    /// <summary>
    /// 接続エラー画面
    /// </summary>
    public partial class FaceClientErr : Form
    {
        #region enum
        /// <summary>
        /// イメージデータ用のボタンタイプ
        /// </summary>
        private enum ButtonKind
        {
            /// <summary>
            /// 閉じるボタン
            /// </summary>
            Close = 0,
        }

        #endregion

        #region Private Const
        private const string CST_RESOURCE_PROPERTY_NAME = "eDoktor.Taikoban.FaceClient.Properties.Resources";
        /// <summary>
        /// メッセージエリアの1行当たりの文字数(半角で計算) フォントはMS UI Gothic 15ポイントでBoldを想定しています。
        /// </summary>
        private const int CST_ONE_LINE_MESSAGE_LENGTH = 38; 
        #endregion

        #region Property
        /// <summary>
        /// 表示メッセージ
        /// </summary>
        public string Message { get; set; }
        #endregion

        #region Private Fields
        /// <summary>
        /// 実行アセンブリー
        /// </summary>
        private Assembly _assembly;

        /// <summary>
        /// イメージデータのディクショナリー
        /// </summary>
        private Dictionary<ButtonKind, ImageData> _dicImageData;

        #endregion

        /// <summary>
        /// コンストラクタ
        /// </summary>
        public FaceClientErr()
        {
            InitializeComponent();
            this._assembly = Assembly.GetExecutingAssembly();
            SetImageData(this._assembly, CST_RESOURCE_PROPERTY_NAME);
        }

        [SecurityPermission(SecurityAction.Demand,  Flags = SecurityPermissionFlag.UnmanagedCode)]
        protected override void WndProc(ref Message m)
        {
            const int WM_NCLBUTTONDBLCLK = 0xA3;

            if (m.Msg == WM_NCLBUTTONDBLCLK)
            {
                //非クライアント領域がダブルクリックされた時
                m.Result = IntPtr.Zero;
                return;
            }

            base.WndProc(ref m);
        }

        /// <summary>
        /// Formロード処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void FaceClientErr_Load(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(Message) == true)
            {
                label1.Text = string.Empty;
                label2.Text = string.Empty;
            }
            else
            {
                try
                {
                    Encoding sjisEnc = Encoding.GetEncoding("Shift_JIS");
                    int byteCount = sjisEnc.GetByteCount(Message);
                    if (byteCount <= CST_ONE_LINE_MESSAGE_LENGTH)
                    {
                        label1.Text = Message;
                        label2.Text = string.Empty;
                    }
                    else
                    {
                        int line1Len = 0;
                        int line2Pos = 0;
                        for (int i = 0; i < Message.Length; i++)
                        {
                            int tmpLen = sjisEnc.GetByteCount(Message.Substring(i, 1));
                            if ((line1Len + tmpLen) < CST_ONE_LINE_MESSAGE_LENGTH)
                            {
                                line1Len += tmpLen;
                            }
                            else
                            {
                                line2Pos = i;
                                break;
                            }
                        }
                        if (line2Pos == 0)
                        {
                            label1.Text = Message;
                            label2.Text = string.Empty;
                        }
                        else
                        {
                            label1.Text = Message.Substring(0, line2Pos);
                            label2.Text = Message.Substring(line2Pos);
                        }
                    }
                }
                catch (Exception ex)
                {
                    eDoktor.Common.Trace.OutputExceptionTrace(ex);
                    label1.Text = Message;
                }
            }
        }

        /// <summary>
        /// 閉じるボタン押下処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picClose_Click(object sender, EventArgs e)
        {
            // ▼ ADD 認証タイムアウト時のアプリ終了対応 2024/01/26 Whizz Y.Tani
            this.DialogResult = DialogResult.OK;
            // ▲ ADD 認証タイムアウト時のアプリ終了対応 2024/01/26 Whizz Y.Tani
            this.Close();
        }

        /// <summary>
        /// マウスカーソルが閉じるボタン領域に入った時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picClose_MouseEnter(object sender, EventArgs e)
        {
            try
            {
                ChangeButtonImage(picClose, ButtonKind.Close, ImageData.ImageKind.MouseEnter);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }

        }

        /// <summary>
        /// マウスカーソルが閉じるボタン領域から抜けた時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picClose_MouseLeave(object sender, EventArgs e)
        {
            try
            {
                ChangeButtonImage(picClose, ButtonKind.Close, ImageData.ImageKind.MouseLeave);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 閉じるボタン押下時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picClose_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                ChangeButtonImage(picClose, ButtonKind.Close, ImageData.ImageKind.MouseDown);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }
        /// <summary>
        /// キャンセルボタン戻し時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picClose_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                ChangeButtonImage(picClose, ButtonKind.Close, ImageData.ImageKind.MouseUp);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// キー入力時の処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FaceClientErr_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                if (e.KeyCode == Keys.Enter)
                {
                    // Enterキー入力で「OK」ボタンクリックと同じ処理を実行する
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        #region 表示用イメージデータの設定など
        /// <summary>
        /// pictureBoxボタン用のイメージデータの設定を行う。
        /// </summary>
        /// <param name="assembly">リソースの属するアセンブリー</param>
        /// <param name="resourceName">リソース名</param>
        private void SetImageData(Assembly assembly, string resourceName)
        {
            this._dicImageData = new Dictionary<ButtonKind, ImageData>();
            var imageData1 = new ImageData(this._assembly, CST_RESOURCE_PROPERTY_NAME);
            imageData1.AddImageData(ImageData.ImageKind.Normal, "Close_button_off");
            imageData1.AddImageData(ImageData.ImageKind.MouseEnter, "Close_button_hover");
            imageData1.AddImageData(ImageData.ImageKind.MouseLeave, "Close_button_off");
            imageData1.AddImageData(ImageData.ImageKind.MouseDown, "Close_button_on");
            imageData1.AddImageData(ImageData.ImageKind.MouseUp, "Close_button_off");
            this._dicImageData.Add(ButtonKind.Close, imageData1);
        }

        /// <summary>
        /// PictureBoxボタンのイメージを指定された種類に変更する。
        /// </summary>
        /// <param name="picBox">変更対象PictureBox</param>
        /// <param name="buttonKind">変更対象ボタンタイプ</param>
        /// <param name="imageKind">イメージの種類</param>
        private void ChangeButtonImage(PictureBox picBox, ButtonKind buttonKind, ImageData.ImageKind imageKind)
        {
            var oldImage = picBox.Image;
            picBox.Image = this._dicImageData[buttonKind].GetImage(imageKind);
            if (oldImage != null)
            {
                oldImage.Dispose();
            }
        }

		#endregion
	}
}
