﻿using eDoktor.Taikoban.FaceAuthCommon;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace eDoktor.Taikoban.FaceAuthSettingsInfo
{
    /// <summary>
    /// 設定情報クラス
    /// </summary>
    public class SettingsInfo
    {
        /// <summary>
        /// 認証モード
        /// </summary>
        public EnumAuthMode auth_mode { get; set; }

        /// <summary>
        /// リトライ回数
        /// </summary>
        public int retry_count { get; set; }

        /// <summary>
        /// 顔テンプレート登録上限
        /// </summary>
        private int faceMaxCount;

        /// <summary>
        /// 顔テンプレート登録上限
        /// </summary>
        public int face_template_regist_max_count
        {
            get
            {
                return faceMaxCount;
            }
            set
            {
                if (value <= 0)
                {
                    faceMaxCount = 1;
                }
                else if (value > 255)
                {
                    faceMaxCount = 255;
                }
                else
                {
                    faceMaxCount = value;
                }
            }
        }

        /// <summary>
        /// 顔サーチ待機時間(ms)
        /// </summary>
        public int face_search_wait_time { get; set; }

        /// <summary>
        /// 起動時待機時間(ms)
        /// </summary>
        public int auth_start_wait_time { get; set; }

        /// <summary>
        /// 接続タイムアウト(ms)
        /// </summary>
        public int time_out { get; set; }

        /// <summary>
        /// マスク装着度閾値
        /// </summary>
        public float mask_score_threshold { get; set; }

        /// <summary>
        /// テンプレート評価値(認証用)
        /// </summary>
        public float template_quality_threshold_auth { get; set; }

        /// <summary>
        /// テンプレート評価値(登録用)
        /// </summary>
        public float template_quality_threshold_insert { get; set; }

        /// <summary>
        /// 顔認証閾値
        /// </summary>
        public float face_auth_threshold { get; set; }

        /// <summary>
        /// 顔照合タイプ
        /// </summary>
        public GFRL.FaceRecognition.FaceRecogDataType face_recog_data_type { get; set; }

        /// <summary>
        /// 性別年齢推定有無
        /// </summary>
        public bool is_estimate_gender_age { get; set; }

        /// <summary>
        /// 顔サーチ時照合適合度閾値
        /// </summary>
        public float face_recog_quality_threshold { get; set; }

        /// <summary>
        /// 最大検出貌数
        /// </summary>
        public int face_search_count_max { get; set; }

        /// <summary>
        /// 最小目間画素数
        /// </summary>
        public int face_search_scale_min { get; set; }

        /// <summary>
        /// 最大目間画素数
        /// </summary>
        public int face_search_scale_max { get; set; }

        /// <summary>
        /// 顔サーチ入力画像幅
        /// </summary>
        public int input_image_width { get; set; }

        /// <summary>
        /// 顔サーチ入力画像高さ
        /// </summary>
        public int input_image_height { get; set; }

        /// <summary>
        /// ユーザ認証時、テンプレートを保持しておくか
        /// </summary>
        public bool is_hold_template_data { get; set; }

        /// <summary>
        /// 別スレッドでのテンプレート更新間隔
        /// </summary>
        public int template_get_wait_time { get; set; }

        /// <summary>
        /// 検索ユーザ確認ダイアログ表示有無(ユーザ検索認証時)
        /// </summary>
        public bool is_display_auth_user_daialog { get; set; }

        /// <summary>
        /// 検索ユーザ確認ダイアログ表示時の認証に使用した画像の表示有無
        /// </summary>
        public bool is_display_auth_image { get; set; }

        /// <summary>
        /// まばたき検知機能を使用するかどうか
        /// </summary>
        public bool is_use_eye_blink { get; set; }

        /// <summary>
        /// まばたき検知に顔検出を使用するかどうか
        /// </summary>
        public bool is_use_face_for_blinking { get; set; }

        /// <summary>
        /// まばたき検知開始のための顔の最大面積
        /// </summary>
        public int face_size_max { get; set; }

        /// <summary>
        /// まばたき検知開始のための顔の最小面積
        /// </summary>
        public int face_size_min { get; set; }

        /// <summary>
        /// まばたき検知開始のための目の最大面積
        /// </summary>
        public int eye_size_max { get; set; }

        /// <summary>
        /// まばたき検知開始のための目の最小面積
        /// </summary>
        public int eye_size_min { get; set; }

        /// <summary>
        /// まばたき検知開始のための待機時間
        /// </summary>
        public int blink_waiting_time { get; set; }

        /// <summary>
        /// まばたきをしたとみなす回数
        /// </summary>
        public int blink_count { get; set; }

        /// <summary>
        /// まばたきをしている時間（これより長い時間目が検出できなければまばたきとみなさない）
        /// </summary>
        public int blinking_time { get; set; }

        /// <summary>
        /// まばたき検知を行う範囲のX座標
        /// </summary>
        public int blink_area_x { get; set; }

        /// <summary>
        /// まばたき検知を行う範囲のY座標
        /// </summary>
        public int blink_area_y { get; set; }

        /// <summary>
        /// まばたき検知を行う範囲の横幅
        /// </summary>
        public int blink_area_width { get; set; }

        /// <summary>
        /// まばたき検知を行う範囲の縦幅
        /// </summary>
        public int blink_area_height { get; set; }

        /// <summary>
        /// まばたきメッセージを表示するかどうか
        /// </summary>
        public bool is_display_blink_message { get; set; }

        /// <summary>
        /// まばたきメッセージ
        /// </summary>
        public string blink_message { get; set; }

        /// <summary>
        /// 顔ガイド表示するかどうか
        /// </summary>
        public bool is_display_face_guide { get; set; }

        //--------------------------------------なりすまし判定設定値--------------------------------------

        /// <summary>
        /// トークンファイルパス
        /// </summary>
        public string token_file_path { get; set; }

        /// <summary>
        /// faceDetectModelファイルパス
        /// </summary>
        public string faceDetectModel_file_path { get; set; }

        /// <summary>
        /// faceDetectParamファイルパス
        /// </summary>
        public string faceDetectParam_file_path { get; set; }

        /// <summary>
        /// fakeModelHファイルパス
        /// </summary>
        public string fakeModelH_file_path { get; set; }

        /// <summary>
        /// fakeModelVファイルパス
        /// </summary>
        public string fakeModelV_file_path { get; set; }

        /// <summary>
        /// faceDirHModelファイルパス
        /// </summary>
        public string faceDirHModel_file_path { get; set; }

        /// <summary>
        /// faceDirVModelファイルパス
        /// </summary>
        public string faceDirVModel_file_path { get; set; }

        /// <summary>
        /// glassesModelファイルパス
        /// </summary>
        public string glassesModel_file_path { get; set; }

        /// <summary>
        /// maskModelファイルパス
        /// </summary>
        public string maskModel_file_path { get; set; }

        /// <summary>
        /// eyeDetectModelファイルパス
        /// </summary>
        public string eyeDetectModel_file_path { get; set; }

        /// <summary>
        /// eyeStatusModelファイルパス
        /// </summary>
        public string eyeStatusModel_file_path { get; set; }

        /// <summary>
        /// eyeDirModelファイルパス
        /// </summary>
        public string eyeDirModel_file_path { get; set; }

        /// <summary>
        /// 顔位置検出用：顔位置検出を行う最小顔横幅サイズ(px)を指定します。
        /// </summary>
        public int minFaceWidth_param { get; set; }

        /// <summary>
        /// 顔位置検出用：顔位置検出を行う最大顔横幅サイズ(px)を指定します。
        /// </summary>
        public int maxFaceWidth_param { get; set; }

        /// <summary>
        /// 顔位置検出用：顔接近度を判定するため、「顔位置検出サイズ面積 ÷ 入力画像サイズ面積」により算出される顔サイズ許容する下限・上限割合を指定します。0.05〜1.00値の範囲で指定します。
        /// </summary>
        public float faceAreaMinRatio_param { get; set; }

        /// <summary>
        /// 顔位置検出用：顔接近度を判定するため、「顔位置検出サイズ面積 ÷ 入力画像サイズ面積」により算出される顔サイズ許容する下限・上限割合を指定します。0.05〜1.00値の範囲で指定します。
        /// </summary>
        public float faceAreaMaxRatio_param { get; set; }

        /// <summary>
        /// 顔位置検出用：検出された顔位置が上下左右端に位置する場合に、以降処理を停止するかどうかを真偽値で指定します。
        /// </summary>
        public bool edgePosErrMode_param { get; set; }

        /// <summary>
        /// フェイク判定用：フェイク判定を行うかどうかを真偽値で指定します。
        /// </summary>
        public bool isFakeMode_param { get; set; }

        /// <summary>
        /// フェイク判定用：Biodata::isFakeLikelihood値から、フェイク顔と判定するための閾値となります。0.0〜1.0値の範囲で指定します。
        /// </summary>
        public float fakeJudgeTh_param { get; set; }

        /// <summary>
        /// 顔向き判定用：顔向き判定を行うかどうかを真偽値で指定します。
        /// </summary>
        public bool isFaceDirMode_param { get; set; }


        /// <summary>
        /// メガネ判定用：メガネ判定を行うかどうかを真偽値で指定します。
        /// </summary>
        public bool isGlassesMode_param { get; set; }


        /// <summary>
        /// マスク判定用：マスク判定を行うかどうかを真偽値で指定します。
        /// </summary>
        public bool isMaskMode_param { get; set; }

        /// <summary>
        /// マスク判定用：Biodata::isMaskLikelihood値から、マスク装着状態と判定するための閾値となります。0.0〜1.0の値の範囲で指定します。
        /// </summary>
        public float maskJudgeTh_param { get; set; }

        /// <summary>
        /// 目の位置検出用：目の位置検出・目の開閉判定を行うかどうかを真偽値で指定します。
        /// </summary>
        public bool isEyeMode_param { get; set; }

        /// <summary>
        /// 目の向き判定用：目の向き判定を行うかどうかを真偽値で指定します。
        /// </summary>
        public bool isEyeDirMode_param { get; set; }


        //----------なりすまし判定方法

        /// <summary>
        /// なりすまし判定方法：判定時間
        /// </summary>
        public int judge_time { get; set; }

        /// <summary>
        /// なりすまし判定方法：判定割合
        /// </summary>
        public int judge_ratio { get; set; }

        /// <summary>
        /// なりすまし判定エラーメッセージ
        /// </summary>
        public string liveness_check_error_message { get; set; }

        /// <summary>
        /// なりすまし判定タイムアウト
        /// </summary>
        public int liveness_check_timeout { get; set; }

        /// <summary>
        /// なりすまし判定タイムアウトエラーメッセージ
        /// </summary>
        public string liveness_check_timeout_error_message { get; set; }

        /// <summary>
        /// なりすまし判定フラグ
        /// </summary>
        public bool is_livenessCheck { get; set; }


        //--------------------------------------なりすまし判定設定値ここまで--------------------------------------
    }
}
