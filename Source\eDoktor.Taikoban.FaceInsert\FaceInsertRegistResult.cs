﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;

namespace eDoktor.Taikoban.FaceInsert
{
    /// <summary>
    /// 一括登録結果画面
    /// </summary>
    public partial class FaceInsertRegistResult : Form
    {
        #region Private Const

        /// <summary>
        /// カンマ
        /// </summary>
        private const string COMMA = ",";

        /// <summary>
        /// 文字コード
        /// </summary>
        private const string CHAR_CODE = "Shift_jis";

        /// <summary>
        /// ConfigKey CSV出力ファイルパス
        /// </summary>
        private const string CONFIG_KEY_CSV_OUTPUT_FILE_PATH = "CsvOutputFilePath";

        /// <summary>
        /// ファイル選択ダイアログの表示拡張子フィルタ
        /// </summary>
        private const string FILE_EXTENSION_FILTER = "CSV File(*.csv)|*.csv;";

        #endregion

        #region Private Fields

        /// <summary>
        /// データリスト
        /// </summary>
        private List<BulkRegistResult> _bulkRegistResultList = new List<BulkRegistResult>();

        /// <summary>
        /// ボタンアクションクラス
        /// </summary>
        BtnImageCahnge _btnImageCahnge = new BtnImageCahnge();

        #endregion

        #region Constructors

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="resultList">処理結果リスト</param>
        public FaceInsertRegistResult(List<BulkRegistResult> resultList)
        {
            InitializeComponent();

            if (resultList == null)
            {
                // 出力するデータがありません。

                Trace.OutputErrorTrace(Messages.Message(MessageId.Message028));
                return;
            }

            // 失敗⇒成功の順に並び替え
            this._bulkRegistResultList = resultList.OrderBy(x => x.IsResult).ToList();
        }

        #endregion

        #region EventHandler

        /// <summary>
        /// フォームロード
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FaceInsertRegistResult_Load(object sender, EventArgs e)
        {
            // グリッドに出力する
            dataGridView1.DataSource = this._bulkRegistResultList;
        }

        /// <summary>
        /// 閉じるボタン押下処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void button3_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// CSV出力ボタン(登録失敗)押下処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void button1_Click(object sender, EventArgs e)
        {

            var filePath = this.SelectCsvFilePath();

            if (string.IsNullOrEmpty(filePath))
            {
                return;
            }

            try
            {
                // CSVファイルに出力する
                using (StreamWriter writer = new StreamWriter(filePath, false, Encoding.GetEncoding(CHAR_CODE)))
                {
                    // ヘッダー部をCSVファイルに書き込む
                    writer.WriteLine(this.CreateHeaderRow());

                    // ユーザによる行追加が許可されている場合は、最後の新規入力用の

                    // 行
                    //for (int i=0;i<datagridv.)
                    foreach (DataGridViewRow row in dataGridView1.Rows)
                    {
                        if ((bool)(row.Cells[nameof(BulkRegistResult.IsResult)].Value))
                        {
                            continue;
                        }

                        // 行データからCSVに出力するテキストを作成
                        var csvRow = this.CreateCsvRowData(row);
                        // CSVファイルに書き込む
                        writer.WriteLine(csvRow);
                    }
                }
            }
            catch (IOException ex)
            {
                Trace.OutputExceptionTrace(ex);
                // ファイルが開かれています。
                MessageBox.Show(Messages.Message(MessageId.Message029));
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// CSV出力ボタン(全て)押下処理
        /// </summary>
        /// <param name=""></param>
        private void button2_Click(object sender, EventArgs e)
        {
            var filePath = this.SelectCsvFilePath();

            if (string.IsNullOrEmpty(filePath))
            {
                return;
            }

            try
            {
                // CSVファイルに出力する
                using (StreamWriter writer = new StreamWriter(filePath, false, Encoding.GetEncoding(CHAR_CODE)))
                {
                    // ヘッダー部をCSVファイルに書き込む
                    writer.WriteLine(this.CreateHeaderRow());

                    // 行
                    foreach (DataGridViewRow row in dataGridView1.Rows)
                    {
                        // 行データからCSVに出力するテキストを作成
                        var csvRow = this.CreateCsvRowData(row);
                        // CSVファイルに書き込む
                        writer.WriteLine(csvRow);
                    }
                }
            }
            catch (IOException ex)
            {
                Trace.OutputExceptionTrace(ex);
                // ファイルが開かれています。
                MessageBox.Show(Messages.Message(MessageId.Message029));
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// マウスカーソルがボタン領域に入った時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseEnter(object sender, EventArgs e)
        {
            _btnImageCahnge.btn_MouseEnter(sender, e);
        }

        /// <summary>
        /// マウスカーソルがボタン領域から抜けた時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseLeave(object sender, EventArgs e)
        {
            _btnImageCahnge.btn_MouseLeave(sender, e);
        }

        /// <summary>
        /// ボタン押下時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            _btnImageCahnge.btn_MouseDown(sender, e);
        }

        /// <summary>
        /// マウスカーソルが閉じるボタン領域に入った時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void BtnCloses_MouseEnter(object sender, EventArgs e)
        {
            try
            {
                Button btn = (Button)sender;
                btn.ForeColor = System.Drawing.Color.FromArgb(180, 209, 234);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }

        }

        /// <summary>
        /// マウスカーソルが閉じるボタン領域から抜けた時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void BtnCloses_MouseLeave(object sender, EventArgs e)
        {
            try
            {
                Button btn = (Button)sender;
                btn.ForeColor = System.Drawing.SystemColors.Window;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// ボタン押下時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void BtnCloses_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            try
            {
                Button btn = (Button)sender;
                btn.ForeColor = System.Drawing.Color.FromArgb(46, 177, 214);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// CSV出力先ファイルパスを選択
        /// </summary>
        /// <returns></returns>
        private string SelectCsvFilePath()
        {
            try
            {
                // SaveFileDialogクラスのインスタンスを作成
                SaveFileDialog sfd = new SaveFileDialog();

                // はじめのファイル名を指定する
                //はじめに表示されるフォルダを指定する
                sfd.InitialDirectory = eDoktor.Common.Configuration.AppSetting(CONFIG_KEY_CSV_OUTPUT_FILE_PATH);
                //[ファイルの種類]に表示される選択肢を指定する
                //指定しない（空の文字列）の時は、現在のディレクトリが表示される
                sfd.Filter = FILE_EXTENSION_FILTER;
                //[ファイルの種類]ではじめに選択されるものを指定する
                //2番目の「すべてのファイル」が選択されているようにする
                sfd.FilterIndex = 2;
                //タイトルを設定する
                sfd.Title = "保存先のファイルを選択してください";
                //ダイアログボックスを閉じる前に現在のディレクトリを復元するようにする
                sfd.RestoreDirectory = true;
                //既に存在するファイル名を指定したとき警告する
                //デフォルトでTrueなので指定する必要はない
                sfd.OverwritePrompt = true;
                //存在しないパスが指定されたとき警告を表示する
                //デフォルトでTrueなので指定する必要はない
                sfd.CheckPathExists = true;

                //ダイアログを表示する
                if (sfd.ShowDialog() == DialogResult.OK)
                {
                    //OKボタンがクリックされたとき、選択されたファイル名を表示する
                    Trace.OutputDebugTrace("CSV出力先 : {0}", sfd.FileName);
                    return sfd.FileName;
                }
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }

            return string.Empty;
        }

        /// <summary>
        /// CSVヘッダ行作成
        /// </summary>
        /// <returns></returns>
        private StringBuilder CreateHeaderRow()
        {
            var headerRow = new StringBuilder();

            //ヘッダー部を取得
            foreach (DataGridViewColumn col in dataGridView1.Columns)
            {
                if (col.Visible)
                {
                    headerRow.Append(col.HeaderText);
                    headerRow.Append(COMMA);
                }
            }

            headerRow.Remove(headerRow.Length - 1, 1);

            return headerRow;
        }

        /// <summary>
        /// 行データからCSVに出力するデータを作成
        /// </summary>
        /// <param name="row">行データ</param>
        /// <returns>CSVに出力するテキスト</returns>
        private StringBuilder CreateCsvRowData(DataGridViewRow row)
        {
            var csvRow = new StringBuilder();


            foreach (DataGridViewColumn col in dataGridView1.Columns)
            {
                if (col.Visible)
                {
                    csvRow.Append(row.Cells[col.Name].Value);
                    csvRow.Append(COMMA);
                }
            }
            return csvRow;
        }

        #endregion
    }
}
