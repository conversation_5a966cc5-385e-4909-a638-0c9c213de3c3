﻿using System;
using System.Collections.Generic;
using System.Linq;
using AForge.Video.DirectShow;
using eDoktor.Common;

namespace eDoktor.Taikoban.FaceAuthCommon
{
    /// <summary>
    /// 使用デバイス決定
    /// </summary>
    public class CameraInfo
    {
        /// <summary>
        /// 使用するカメラをチェックする
        /// </summary>
        /// <param name="whiteList">ホワイトリスト</param>
        /// <param name="blackList">ブラックリスト</param>
        /// <returns>MonikerString</returns>
        public static string GetUseDevice(List<string> whiteList, List<string> blackList)
        {
            var useDeviceMonikerString = string.Empty;

            try
            {
                // 端末で認識しているカメラデバイスの一覧を取得 ※複数ある場合、アルファベット順のリストになっている模様。
                var videoDevices = new FilterInfoCollection(FilterCategory.VideoInputDevice);

                if (videoDevices.Count > 0)
                {
                    eDoktor.Common.Trace.OutputDebugTrace("検出デバイスあり");
                    foreach (FilterInfo videoDevice in videoDevices)
                    {
                        eDoktor.Common.Trace.OutputDebugTrace("検出デバイス名：{0}", videoDevice.Name);
                    }

                    if (whiteList != null && whiteList.Count() > 0)
                    {
                        // ホワイトリストがある場合、ホワイトリストに載っているものを利用する。
                        Trace.OutputDebugTrace("ホワイトリストあり");

                        foreach (FilterInfo videoDevice in videoDevices)
                        {
                            if (whiteList.Exists(x => videoDevice.Name.StartsWith(x)))
                            {
                                // 認識しているカメラデバイスの一覧から前方一致で検索し、ホワイトリストに存在するデバイスを使用(該当する1件目を使用)
                                useDeviceMonikerString = videoDevice.MonikerString; ;
                                Trace.OutputDebugTrace("一致デバイスあり！:{0}", videoDevice.Name);
                                break;
                            }
                        }

                    }
                    else if (blackList != null && blackList.Count() > 0)
                    {
                        // ホワイトリストが空の場合、ブラックリストに載っていないものを利用する。
                        Trace.OutputDebugTrace("ブラックリストあり");

                        foreach (FilterInfo videoDevice in videoDevices)
                        {
                            if (!blackList.Exists(x => videoDevice.Name.StartsWith(x)))
                            {
                                // 認識しているカメラデバイスの一覧から前方一致で検索し、ホワイトリストに存在するデバイスを使用(該当する1件目を使用)
                                useDeviceMonikerString = videoDevice.MonikerString; ;
                                Trace.OutputDebugTrace("一致デバイスあり！:{0}", videoDevice.Name);
                                break;
                            }
                        }
                    }
                    else
                    {
                        // ホワイトリストもブラックリストもない場合は、1件目を使用
                        var videoDevice = videoDevices[0];
                        useDeviceMonikerString = videoDevice.MonikerString;
                        Trace.OutputDebugTrace("一致デバイスあり！:{0}", videoDevice.Name);
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }

            return useDeviceMonikerString;
        }
        /// <summary>
        /// 使用するカメラをチェックする
        /// </summary>
        /// <param name="whiteList">ホワイトリスト</param>
        /// <param name="blackList">ブラックリスト</param>
        /// <returns></returns>
        public static int UseCameraCheck(List<string> whiteList, List<string> blackList)
        {
            try
            {
                var useDeviceIndex = -1;

                // 端末で認識しているカメラデバイスの一覧を取得 ※複数ある場合、アルファベット順のリストになっている模様。
                var videoDevices = new FilterInfoCollection(FilterCategory.VideoInputDevice);

                if (videoDevices.Count > 0)
                {
                    if (whiteList != null && whiteList.Count() > 0)
                    {
                        // ホワイトリストがある場合、ホワイトリストに載っているものを利用する。
                        Trace.OutputDebugTrace("ホワイトリストあり");

                        for (int i = 0; i < videoDevices.Count; i++)
                        {
                            if (whiteList.Exists(x => videoDevices[i].Name.StartsWith(x)))
                            {
                                // 認識しているカメラデバイスの一覧から前方一致で検索し、ホワイトリストに存在するデバイスを使用(該当する1件目を使用)
                                useDeviceIndex = i;
                                Trace.OutputDebugTrace("一致デバイスあり！:{0}", videoDevices[useDeviceIndex].Name);
                                break;
                            }
                        }

                    }
                    else if (blackList != null && blackList.Count() > 0)
                    {
                        // ホワイトリストが空の場合、ブラックリストに載っていないものを利用する。
                        Trace.OutputDebugTrace("ブラックリストあり");

                        for (int i = 0; i < videoDevices.Count; i++)
                        {
                            if (!blackList.Exists(x => videoDevices[i].Name.StartsWith(x)))
                            {
                                // 認識しているカメラデバイスの一覧から前方一致で検索し、ブラックリストに存在しないデバイスを使用(該当する1件目を使用)
                                useDeviceIndex = i;
                                Trace.OutputDebugTrace("一致デバイスあり！:{0}", videoDevices[useDeviceIndex].Name);
                                break;
                            }
                        }
                    }
                    else
                    {
                        // ホワイトリストもブラックリストもない場合は、1件目を使用
                        useDeviceIndex = 0;
                        Trace.OutputDebugTrace("一致デバイスあり！:{0}", videoDevices[useDeviceIndex].Name);
                    }
                }

                Trace.OutputDebugTrace("useDeviceIndex(デバイスリストのインデックス) : {0}", useDeviceIndex);

                return useDeviceIndex;
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return -1;
            }
        }
    }
}
