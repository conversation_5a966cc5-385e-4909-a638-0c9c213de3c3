﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using OpenCvSharp;
using OpenCvSharp.Extensions;

namespace eDoktorTaikoban.FaceClientCommon
{
    /// <summary>
    /// カスケード分類機クラス
    /// </summary>
    public class SearchFaceByCascadeClassifier : IDisposable
    {
        /// <summary>
        /// カスケード分類子
        /// </summary>
        private readonly CascadeClassifier _cascadeClassifier;

        /// <summary>
        /// カスケード分類機XMLファイル
        /// </summary>
        private const string CASCADE_XML = "haarcascade_frontalface_default.xml";

        public SearchFaceByCascadeClassifier()
        {
            try
            {
                this._cascadeClassifier = new CascadeClassifier(CASCADE_XML);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// カスケード分類機で顔認識を行い、顔に四角を付ける
        /// </summary>
        /// <param name="frame">元イメージ</param>
        /// <param name="isFlipHorizon">反転フラグ</param>
        /// <returns>顔認識を付加したイメージ</returns>
        public Bitmap SearchFace(Bitmap frame,bool isFlipHorizon)
        {
            try
            {
                if (this._cascadeClassifier != null)
                {
                    //取得先のMat作成
                    using (var frameMat = BitmapConverter.ToMat(frame))
                    {
                        // 顔認識を行う
                        var rects = _cascadeClassifier.DetectMultiScale(frameMat, 1.1, 5, HaarDetectionTypes.ScaleImage, new OpenCvSharp.Size(30, 30));

                        if (rects.Length > 0)
                        {
                            // 顔を認識した箇所に赤枠を付ける
                            Cv2.Rectangle(frameMat, rects[0], Scalar.Red);
                        }

                        frame = frameMat.ToBitmap();
                    }
                }

                if(isFlipHorizon)
                {
                    frame.RotateFlip(RotateFlipType.RotateNoneFlipX);
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }

            return frame;
        }

        /// <summary>
        /// <summary>
        /// リソース開放
        /// </summary>
        public void Dispose()
        {
            if (this._cascadeClassifier != null)
            {
                this._cascadeClassifier.Dispose();
            }
        }
    }
}