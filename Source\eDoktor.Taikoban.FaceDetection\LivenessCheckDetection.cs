﻿//#define USE_CASCADE_ALT3    // カスケードファイルとして alt3を使用する(検出条件が厳しいみたいだ)
//                            // USE_CASCADE_ALT3を使用する時にはカスケードファイルを実行ファイルにコピーしてください。
using System;
using System.Collections.Concurrent;
using System.Drawing;
using System.Threading;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using System.IO;
using LivenessCheckSharp;
using System.Collections.Generic;

namespace eDoktor.Taikoban.FaceDetection
{
    #region 構造体
    /// <summary>
    /// なりすまし判定結果
    /// </summary>
    public struct LivenessCheckResult
    {
        #region Public Properties
        public int FaceCount { get; set; }
        public bool IsInvalidFace { get; set; }
        public bool IsFake { get; set; }
        public bool IsCurrentFake { get; set; }
        public bool IsTimeout { get; set; }
        #endregion

        #region Constructors
        public LivenessCheckResult(int faceCount, bool isInvalidFace, bool isFake, bool isCurrentFake, bool isTimeout)
        {
            FaceCount = faceCount;
            IsInvalidFace = isInvalidFace;
            IsFake = isFake;
            IsCurrentFake = isCurrentFake;
            IsTimeout = isTimeout;
        }
        #endregion
    }
    #endregion

    /// <summary>
    /// なりすまし判定処理クラス
    /// </summary>
    public class LivenessCheckDetection
    {

        #region プロパティー
        /// <summary>
        /// 顔検出用画像を格納するキュー
        /// </summary>
        public ConcurrentQueue<Bitmap[]> ImageQueue
        {
            private set { _imageQueue = value; }
            get { return _imageQueue; }
        }

        private object _livenessCheckResultLock = new object();
        /// <summary>
        /// なりすまし判定結果
        /// </summary>
        public LivenessCheckResult LivenessCheckResult
        {
            get
            {
                lock (_livenessCheckResultLock)
                {
                    return _livenessCheckResult;
                }
            }
            private set
            {
                lock (_livenessCheckResultLock)
                {
                    _livenessCheckResult = value;
                }
            }
        }

        /// <summary>
        /// 顔検出間隔(ms) 
        /// </summary>
        public int FaceCheckInterval
        {
            set
            {
                if (value < CST_DEFAULT_EXECUTION_INTERVAL)
                {
                    _faceCheckInterval = CST_DEFAULT_EXECUTION_INTERVAL;
                }
                else
                {
                    _faceCheckInterval = value;
                }
            }
            get
            {
                return _faceCheckInterval;
            }
        }

        /// <summary>
        /// 生体判定結果
        /// </summary>
        public BioDataStruct _bioDataStruct;

        /// <summary>
        /// 指定時間内の複数の画像がフェイク画像かどうか（フェイク画像：true）
        /// </summary>
        public bool _isFake = false;

        /// <summary>
        /// タイムアウトしているかどうか
        /// </summary>
        public bool _isTimeout = false;

        /// <summary>
        /// タイムアウト計測用タイマー
        /// </summary>
        public System.Threading.Timer _timeoutTimer = null;

        /// <summary>
        /// 現在判定した画像がフェイク画像かどうか
        /// </summary>
        public bool _isCurrentFake = false;

        /// <summary>
        /// 顔の数
        /// </summary>
        public int _faceCount = 0;

        /// <summary>
        /// なりすまし判定サムネイルを登録するかどうか
        /// </summary>
        public bool _isThumbnail = false;

        /// <summary>
        /// 画像に映る顔が不正かどうか（true:不正な顔 false:正常な顔）
        /// </summary>
        public bool _isInvalidFace = false;

        #endregion

        #region コンスタント

        /// <summary>
        /// 省略時顔検出実行間隔(単位はms)
        /// </summary>
        private const int CST_DEFAULT_EXECUTION_INTERVAL = 100;
        #endregion

        #region フィールド

        /// <summary>
        /// なりすまし判定結果
        /// </summary>
        private LivenessCheckResult _livenessCheckResult = new LivenessCheckResult(0, false, false, false, false);

        /// <summary>
        /// スレッド実行中フラグ(true:実行中 false:非実行)
        /// </summary>
        private bool _executing = false;
        /// <summary>
        /// なりすまし判定処理実行中フラグ(true:実行中 false:
        /// </summary>
        private bool _busy = false;
        private bool _executable = false;
        /// <summary>
        /// なりすまし判定に使用するビットマップを格納するコンカレントQueue
        /// </summary>
        private ConcurrentQueue<Bitmap[]> _imageQueue = null;
        private int _faceCheckInterval = 0;
        private System.Threading.Timer _faceDetectionTimer = null;
        /// <summary>
        /// 設定情報
        /// </summary>
        private SettingsInfo _settingsInfo = new SettingsInfo();

        /// <summary>
        /// なりすまし判定SDKラッパークラス
        /// </summary>
        private LivenessCheckSharpClass _livenessCheckSharpClass;

        /// <summary>
        /// 生体判定用ストップウォッチ
        /// </summary>
        private System.Diagnostics.Stopwatch _stopwatch = new System.Diagnostics.Stopwatch();

        /// <summary>
        /// 生体判定結果リスト
        /// </summary>
        private List<BioDataStruct> _bioDataList = new List<BioDataStruct>();

        /// <summary>
        /// 生体判定エラーメッセージダイアログ
        /// </summary>
        private MessageDialog _messageDialog;
        #endregion

        #region コンストラクタ
        public LivenessCheckDetection(SettingsInfo settingsInfo, MessageDialog messageDialog)
        {
            try
            {
                //トークンファイル読み込み
                string readText = string.Empty;
                using (StreamReader sr = new StreamReader(Path.GetFullPath(settingsInfo.token_file_path), System.Text.Encoding.UTF8))
                {
                    readText = sr.ReadLine();
                }

                //生体判定ラッパーライブラリ生成
                _livenessCheckSharpClass = new LivenessCheckSharpClass();
                if (!_livenessCheckSharpClass.checkTokenWrapper(readText))
                {
                    //エラーメッセージ・エラーログを出す処理
                    eDoktor.Common.Trace.OutputErrorTrace("checkTokenWrapperトークンファイルを確認してください");

                    //SDKのオブジェクトを解放
                    _livenessCheckSharpClass.releaseActivationChallengeWrapper();
                    _livenessCheckSharpClass.releaseLivenessCheckWrapper();
                    _executable = false;
                    return;
                }

                if (!_livenessCheckSharpClass.validTokenWrapper(readText))
                {
                    //エラーメッセージ・エラーログを出す処理
                    eDoktor.Common.Trace.OutputErrorTrace("validTokenWrapperトークンファイルを確認してください");

                    //SDKのオブジェクトを解放
                    _livenessCheckSharpClass.releaseActivationChallengeWrapper();
                    _livenessCheckSharpClass.releaseLivenessCheckWrapper();
                    eDoktor.Common.Trace.OutputErrorTrace("releaseLivenessCheckWrapperインスタンス解放");
                    _executable = false;
                    return;
                }

                // モデルファイルの読み込み
                ExternalFilePathStruct externalFilePathStruct = new ExternalFilePathStruct();
                externalFilePathStruct.faceDetectModel = Path.GetFullPath(settingsInfo.faceDetectModel_file_path);
                externalFilePathStruct.faceDetectParam = Path.GetFullPath(settingsInfo.faceDetectParam_file_path);
                externalFilePathStruct.fakeModelH = Path.GetFullPath(settingsInfo.fakeModelH_file_path);
                externalFilePathStruct.fakeModelV = Path.GetFullPath(settingsInfo.fakeModelV_file_path);
                externalFilePathStruct.faceDirHModel = Path.GetFullPath(settingsInfo.faceDirHModel_file_path);
                externalFilePathStruct.faceDirVModel = Path.GetFullPath(settingsInfo.faceDirVModel_file_path);
                externalFilePathStruct.glassesModel = Path.GetFullPath(settingsInfo.glassesModel_file_path);
                externalFilePathStruct.maskModel = Path.GetFullPath(settingsInfo.maskModel_file_path);
                externalFilePathStruct.eyeDetectModel = Path.GetFullPath(settingsInfo.eyeDetectModel_file_path);
                externalFilePathStruct.eyeStatusModel = Path.GetFullPath(settingsInfo.eyeStatusModel_file_path);
                externalFilePathStruct.eyeDirModel = Path.GetFullPath(settingsInfo.eyeDirModel_file_path);

                //なりすましSDKに読み込む設定値の読み込み
                ParamsStruct paramsStruct = new ParamsStruct();
                paramsStruct.minFaceWidth = settingsInfo.minFaceWidth_param;
                paramsStruct.maxFaceWidth = settingsInfo.maxFaceWidth_param;
                paramsStruct.faceAreaMinRatio = settingsInfo.faceAreaMinRatio_param;
                paramsStruct.faceAreaMaxRatio = settingsInfo.faceAreaMaxRatio_param;
                paramsStruct.edgePosErrMode = settingsInfo.edgePosErrMode_param;
                paramsStruct.isFakeMode = settingsInfo.isFakeMode_param;
                paramsStruct.fakeJudgeTh = settingsInfo.fakeJudgeTh_param;
                paramsStruct.isFaceDirMode = settingsInfo.isFaceDirMode_param;
                paramsStruct.isGlassesMode = settingsInfo.isGlassesMode_param;
                paramsStruct.isMaskMode = settingsInfo.isMaskMode_param;
                paramsStruct.maskJudgeTh = settingsInfo.maskJudgeTh_param;
                paramsStruct.isEyeMode = settingsInfo.isEyeMode_param;
                paramsStruct.isEyeDirMode = settingsInfo.isEyeDirMode_param;

                // モデルファイルと顔検出に必要な設定値をセット
                var result = _livenessCheckSharpClass.InitWrapper(externalFilePathStruct, paramsStruct);

                if (!result)
                {
                    //エラーメッセージ・エラーログを出す処理
                    eDoktor.Common.Trace.OutputErrorTrace("モデルファイル、パス、設定値を確認してください");

                    //SDKのオブジェクトを解放
                    _livenessCheckSharpClass.releaseActivationChallengeWrapper();
                    _livenessCheckSharpClass.releaseLivenessCheckWrapper();
                    _livenessCheckSharpClass.Dispose();
                    _executable = false;
                    return;
                }

                // 生体情報検知結果を格納する領域をセット
                result = _livenessCheckSharpClass.RegisterBioDataWrapper();

                if (!result)
                {
                    //エラーメッセージ・エラーログを出す処理
                    eDoktor.Common.Trace.OutputErrorTrace("モデルファイル、パス、設定値を確認してください");

                    //SDKのオブジェクトを解放
                    _livenessCheckSharpClass.releaseActivationChallengeWrapper();
                    _livenessCheckSharpClass.releaseLivenessCheckWrapper();
                    _livenessCheckSharpClass.Dispose();
                    _executable = false;
                    return;
                }

                _executing = false;
                _imageQueue = new ConcurrentQueue<Bitmap[]>();
                FaceCheckInterval = CST_DEFAULT_EXECUTION_INTERVAL;
                TimerCallback timerDelegate = new TimerCallback(ExecLivenessCheck);
                _faceDetectionTimer = new System.Threading.Timer(timerDelegate);
                _busy = false;
                _settingsInfo = settingsInfo;
                _bioDataStruct = new BioDataStruct();
                //なりすまし判定を使わない設定値の場合、なりすまし判定を開始しない
                if (_settingsInfo.is_livenessCheck)
                {
                    _executable = true;
                }

                //タイムアウト用のタイマーセット
                TimerCallback timoutTimerDelegate = new TimerCallback(TimeoutLivenessCheck);
                this._timeoutTimer = new System.Threading.Timer(timoutTimerDelegate);

                _messageDialog = messageDialog;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
                if(_livenessCheckSharpClass != null)
                {
                    _livenessCheckSharpClass.releaseActivationChallengeWrapper();
                    _livenessCheckSharpClass.releaseLivenessCheckWrapper();
                    _livenessCheckSharpClass.Dispose();
                    eDoktor.Common.Trace.OutputErrorTrace("releaseLivenessCheckWrapperインスタンス解放");
                }
                _executable = false;
            }
        }
        #endregion

        /// <summary>
        /// 顔検出を開始する。
        /// </summary>
        /// <param name="interval">顔検出間隔</param>
        /// <returns>true:開始しました false:開始せず</returns>
        public bool Start(int interval)
        {
            FaceCheckInterval = interval;
            return Start();
        }

        /// <summary>
        /// 顔検出を開始する。
        /// </summary>
        /// <returns>true:開始しました false:開始せず</returns>
        public bool Start()
        {
            if (_executable != true)
            {
                return false;
            }
            try
            {
                //なりすまし判定処理開始
                _faceDetectionTimer.Change(37, FaceCheckInterval);
                //タイムアウトタイマー開始
                _timeoutTimer.Change(_settingsInfo.liveness_check_timeout, 0);
                _executing = true;
                eDoktor.Common.Trace.OutputTrace("顔検出処理を開始しました。");
                return true;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
                _executing = false;
                return false;
            }
        }

        /// <summary>
        /// 顔検出を休止する。
        /// </summary>
        /// <returns>true:開始しました false:開始せず</returns>
        public bool Stop()
        {
            try
            {
                if (_executable != true)
                {
                    return false;
                }
                if (_executing == true)
                {
                    //なりすまし判定処理終了
                    _faceDetectionTimer.Change(Timeout.Infinite, Timeout.Infinite);
                    //タイムアウトタイマー終了
                    _timeoutTimer.Change(Timeout.Infinite, Timeout.Infinite);
                    eDoktor.Common.Trace.OutputTrace("顔検出処理を休止しました。");
                    _executing = false;
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
                return false;
            }
        }

        /// <summary>
        /// 顔検出を再開する。
        /// </summary>
        /// <returns></returns>
        public bool ReStart()
        {
            try
            {
                if (_executable != true)
                {
                    return false;
                }
                if (_executing != true)
                {
                    // 結果を初期化する
                    var result = new LivenessCheckResult(0, false, false, false, false);
                    LivenessCheckResult = result;

                    _isFake = false;
                    _isCurrentFake = false;
                    _bioDataList = new List<BioDataStruct>();
                    _stopwatch.Reset();
                    _faceCount = 0;
                    //なりすまし判定処理開始
                    _faceDetectionTimer.Change(37, FaceCheckInterval);
                    //タイムアウトタイマー開始
                    _timeoutTimer.Change(_settingsInfo.liveness_check_timeout, 0);
                    eDoktor.Common.Trace.OutputTrace("顔検出処理を再開しました。");
                    _executing = true;
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
                return false;
            }
        }

        /// <summary>
        /// 顔検出処理を終了します。
        /// </summary>
        public void End()
        {
            try
            {
                if (_faceDetectionTimer != null)
                {
                    //なりすまし判定処理終了
                    _faceDetectionTimer.Change(Timeout.Infinite, Timeout.Infinite);
                    _faceDetectionTimer.Dispose();
                    _executing = false;
                    _busy = false;
                    _faceDetectionTimer = null;
                }
                if(_timeoutTimer != null)
                {
                    //タイムアウトタイマー終了
                    _timeoutTimer.Change(Timeout.Infinite, Timeout.Infinite);
                }
                eDoktor.Common.Trace.OutputTrace("顔検出処理を終了しました。");
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
            _executable = false;
        }

        /// <summary>
        /// なりすまし判定処理実行中かどうかを返す。
        /// </summary>
        /// <returns>true:実行中 false:未実行</returns>
        public bool IsExecuting()
        {
            return _executing;
        }

        /// <summary>
        /// なりすまし判定処理実行可能かどうかを返す。
        /// </summary>
        /// <returns>true:実行可能 false:実行不可能</returns>
        public bool IsExecutable()
        {
            return _executable;
        }

        /// <summary>
        /// なりすまし判定を実行します。
        /// </summary>
        /// <param name="o"></param>
        private void ExecLivenessCheck(object o)
        {
            //タイムアウト用タイマーをリセット
            if (!this._messageDialog.Visible && !_isInvalidFace) //エラー出てないかつ正常な顔
            {
                _timeoutTimer.Change(_settingsInfo.liveness_check_timeout, 0);
            }

            if (_executable != true || _busy == true)
            {
                return;
            }

            if (_imageQueue.Count <= 0)
            {
                return;
            }
            try
            {
                Bitmap[] tmpBitmap = null;
                _busy = true;
                while (true)
                {
                    if (_imageQueue.Count <= 0)
                    {
                        break;
                    }

                    if (_imageQueue.TryDequeue(out tmpBitmap) != true)
                    {
                        break;
                    }

                    if (tmpBitmap.Length < 2)
                    {
                        break;
                    }

                    // なりすまし検出を行う
                    var livenessResult = _livenessCheckSharpClass.ProcessWrapper(tmpBitmap[0]);
                    //生体判定結果取得
                    _livenessCheckSharpClass.outputBioData(ref _bioDataStruct);
                    //顔の数
                    _faceCount = _bioDataStruct.faceCount;
                    eDoktor.Common.Trace.OutputTrace("_faceCount : {0}", _faceCount);
                    ////現在の画像でフェイク画像かどうか判定
                    //_isCurrentFake = checkCurrentFake(_bioDataStruct);
                    //正しい顔かどうかチェック
                    _isInvalidFace = isInValidFace(_bioDataStruct);
                    eDoktor.Common.Trace.OutputTrace("_isInvalidFace : {0}", _isInvalidFace);

                    // なりすまし検出を行う
                    livenessResult = _livenessCheckSharpClass.ProcessWrapper(tmpBitmap[1]);
                    //生体判定結果取得
                    _livenessCheckSharpClass.outputBioData(ref _bioDataStruct);
                    ////現在の画像でフェイク画像かどうか判定
                    _isCurrentFake = checkCurrentFake(_bioDataStruct);
                    eDoktor.Common.Trace.OutputTrace("_isCurrentFake : {0}", _isCurrentFake);
                    eDoktor.Common.Trace.OutputDebugTrace("_bioDataStruct.isFakeLikelihood={0}", _bioDataStruct.isFakeLikelihood);

                    //正しい顔でないときは判定処理を行わない
                    if (_isInvalidFace)
                    {
                        //判定処理に使用する変数を初期化する
                        _bioDataList = new List<BioDataStruct>();
                        _stopwatch.Reset();
                    }
                    else
                    {
                        //指定時間内に取得できた画像でフェイクかどうか判定
                        checkLivenessFake(_bioDataStruct);
                    }

                    // 結果を格納する
                    var result = new LivenessCheckResult(_faceCount, _isInvalidFace, _isFake, _isCurrentFake, _isTimeout);
                    LivenessCheckResult = result;

                    if (tmpBitmap != null)
                    {
                        if (tmpBitmap.Length > 0)
                        {
                            tmpBitmap[0].Dispose();
                        }

                        if (tmpBitmap.Length > 1)
                        {
                            tmpBitmap[1].Dispose();
                        }

                        tmpBitmap = null;
                    }

                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
            finally
            {
                _busy = false;
            }
        }

        /// <summary>
        /// タイムアウトを制御する
        /// </summary>
        /// <param name="sender"></param>
        private void TimeoutLivenessCheck(object sender)
        {
            _timeoutTimer.Change(Timeout.Infinite, Timeout.Infinite);
            _isTimeout = true;
            //タイムアウトポップアップを表示する
            if (!this._messageDialog.Visible)
            {
                //this._messageDialog.ShowDialog();
            }
            // 結果を返す
            var result = new LivenessCheckResult(0, false, false, false, true);
            LivenessCheckResult = result;
        }

        /// <summary>
        /// 生体かどうか判定する
        /// </summary>
        /// <param name="bioDataStruct"></param>
        private void checkLivenessFake(BioDataStruct bioDataStruct)
        {
            _stopwatch.Start();
            eDoktor.Common.Trace.OutputTrace("ストップウォッチ : {0}", _stopwatch.ElapsedMilliseconds);
            eDoktor.Common.Trace.OutputTrace("msg : {0}", bioDataStruct.msg);

            if (isInValidFace(bioDataStruct))
            {
                bioDataStruct.isFakeFace = true;
            }

            _bioDataList.Add(bioDataStruct);

            //時間で判断
            if (_stopwatch.ElapsedMilliseconds > _settingsInfo.judge_time)
            {
                _isFake = isFakeByBioDataStructsy(_bioDataList);
                eDoktor.Common.Trace.OutputTrace("なりすまし判定結果_isFake : {0}", _isFake);
                _bioDataList = new List<BioDataStruct>();
                _stopwatch.Reset();
                //エラーポップアップ表示
                if (_isFake && !_messageDialog.Visible)
                {
                    //エラー出したときからタイムアウトタイマースタート
                    _timeoutTimer.Change(_settingsInfo.liveness_check_timeout, 0);
                    //_messageDialog.ShowDialog();
                    // 顔認証時のみ、なりすまし画像登録要求を行う
                    _isThumbnail = true;
                }
            }
        }

        /// <summary>
        /// 生体判定結果からフェイク画像かどうか判定する
        /// </summary>
        /// <param name="bioDataList"></param>
        /// <returns>true:フェイク画像 false:生体画像</returns>
        private bool isFakeByBioDataStructsy(List<BioDataStruct> bioDataList)
        {
            int bioDataCount = bioDataList.Count;
            int bioCount = 0;
            foreach (BioDataStruct bioData in bioDataList)
            {
                eDoktor.Common.Trace.OutputTrace("生体判定Likehood値: {0}", bioData.isFakeLikelihood);
                eDoktor.Common.Trace.OutputTrace("isFakeFace: {0}", bioData.isFakeFace);
                if (bioData.isFakeFace)
                {
                    bioCount++;
                }
            }

            return 100 * bioCount / bioDataCount > 100 - _settingsInfo.judge_ratio;
        }

        /// <summary>
        /// 生体判定結果から生体判定できる正しい画像かどうか判別する
        /// </summary>
        /// <param name="bioDataStruct"></param>
        /// <returns>true:不正な画像 false:正常な画像</returns>
        private bool isInValidFace(BioDataStruct bioDataStruct)
        {
            //顔検出エリアが空かどうか
            if(bioDataStruct.isFaceAreaEmpty) 
            {
                return true;
            }

            //顔の数が1以外かどうか
            if(bioDataStruct.faceCount != 1)
            {
                return true;
            }

            //正しく顔が検出できる状態かどうか
            if (!bioDataStruct.isValidFace)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 生体判定結果から現在の画像がフェイク画像かどうか判定する
        /// </summary>
        /// <param name="bioDataStruct"></param>
        /// <returns>true:フェイク画像 false:生体画像</returns>
        private bool checkCurrentFake(BioDataStruct bioDataStruct)
        {
            //return isInValidFace(bioDataStruct) || bioDataStruct.isFakeFace;
            return bioDataStruct.isFakeFace;
        }

    }
}
