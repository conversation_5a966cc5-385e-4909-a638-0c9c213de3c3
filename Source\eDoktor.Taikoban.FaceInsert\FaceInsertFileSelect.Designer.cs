﻿
namespace eDoktor.Taikoban.FaceInsert
{
    partial class FaceInsertFileSelect
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FaceInsertFileSelect));
            this.btnReturn = new System.Windows.Forms.Button();
            this.BtnSelect = new System.Windows.Forms.Button();
            this.txtStaffId = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.txtFilePath = new System.Windows.Forms.TextBox();
            this.BtnEnrollment = new System.Windows.Forms.Button();
            this.SuspendLayout();
            // 
            // btnReturn
            // 
            this.btnReturn.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnReturn.BackColor = System.Drawing.Color.Transparent;
            this.btnReturn.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("btnReturn.BackgroundImage")));
            this.btnReturn.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
            this.btnReturn.FlatAppearance.BorderSize = 0;
            this.btnReturn.FlatAppearance.CheckedBackColor = System.Drawing.Color.Transparent;
            this.btnReturn.FlatAppearance.MouseDownBackColor = System.Drawing.Color.Transparent;
            this.btnReturn.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btnReturn.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnReturn.Font = new System.Drawing.Font("メイリオ", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
            this.btnReturn.ForeColor = System.Drawing.SystemColors.Window;
            this.btnReturn.Location = new System.Drawing.Point(85, 625);
            this.btnReturn.Name = "btnReturn";
            this.btnReturn.Size = new System.Drawing.Size(210, 44);
            this.btnReturn.TabIndex = 18;
            this.btnReturn.Text = "戻る";
            this.btnReturn.UseVisualStyleBackColor = false;
            this.btnReturn.Click += new System.EventHandler(this.btnReturn_Click);
            this.btnReturn.MouseDown += new System.Windows.Forms.MouseEventHandler(this.btn_MouseDown);
            this.btnReturn.MouseEnter += new System.EventHandler(this.btn_MouseEnter);
            this.btnReturn.MouseLeave += new System.EventHandler(this.btn_MouseLeave);
            // 
            // BtnSelect
            // 
            this.BtnSelect.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.BtnSelect.BackColor = System.Drawing.Color.Transparent;
            this.BtnSelect.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("BtnSelect.BackgroundImage")));
            this.BtnSelect.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
            this.BtnSelect.FlatAppearance.BorderSize = 0;
            this.BtnSelect.FlatAppearance.CheckedBackColor = System.Drawing.Color.Transparent;
            this.BtnSelect.FlatAppearance.MouseDownBackColor = System.Drawing.Color.Transparent;
            this.BtnSelect.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.BtnSelect.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.BtnSelect.Font = new System.Drawing.Font("メイリオ", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
            this.BtnSelect.ForeColor = System.Drawing.SystemColors.Window;
            this.BtnSelect.Location = new System.Drawing.Point(215, 446);
            this.BtnSelect.Name = "BtnSelect";
            this.BtnSelect.Size = new System.Drawing.Size(210, 44);
            this.BtnSelect.TabIndex = 16;
            this.BtnSelect.Text = "ファイル選択";
            this.BtnSelect.UseVisualStyleBackColor = false;
            this.BtnSelect.Click += new System.EventHandler(this.BtnSelect_Click);
            this.BtnSelect.MouseDown += new System.Windows.Forms.MouseEventHandler(this.btn_MouseDown);
            this.BtnSelect.MouseEnter += new System.EventHandler(this.btn_MouseEnter);
            this.BtnSelect.MouseLeave += new System.EventHandler(this.btn_MouseLeave);
            // 
            // txtStaffId
            // 
            this.txtStaffId.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtStaffId.Font = new System.Drawing.Font("メイリオ", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
            this.txtStaffId.Location = new System.Drawing.Point(191, 282);
            this.txtStaffId.Name = "txtStaffId";
            this.txtStaffId.Size = new System.Drawing.Size(328, 36);
            this.txtStaffId.TabIndex = 14;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.BackColor = System.Drawing.Color.Transparent;
            this.label3.Font = new System.Drawing.Font("メイリオ", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
            this.label3.ForeColor = System.Drawing.SystemColors.Window;
            this.label3.Location = new System.Drawing.Point(40, 375);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(126, 28);
            this.label3.TabIndex = 13;
            this.label3.Text = "ファイルパス";
            this.label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.BackColor = System.Drawing.Color.Transparent;
            this.label2.Font = new System.Drawing.Font("MS UI Gothic", 20.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
            this.label2.ForeColor = System.Drawing.SystemColors.Window;
            this.label2.Location = new System.Drawing.Point(140, 210);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(367, 27);
            this.label2.TabIndex = 12;
            this.label2.Text = "画像ファイルを選択してください。";
            this.label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.BackColor = System.Drawing.Color.Transparent;
            this.label1.Font = new System.Drawing.Font("メイリオ", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
            this.label1.ForeColor = System.Drawing.SystemColors.Window;
            this.label1.Location = new System.Drawing.Point(40, 290);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(94, 28);
            this.label1.TabIndex = 11;
            this.label1.Text = "利用者ID";
            this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // txtFilePath
            // 
            this.txtFilePath.AllowDrop = true;
            this.txtFilePath.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtFilePath.BackColor = System.Drawing.SystemColors.Window;
            this.txtFilePath.Font = new System.Drawing.Font("メイリオ", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
            this.txtFilePath.Location = new System.Drawing.Point(191, 367);
            this.txtFilePath.Multiline = true;
            this.txtFilePath.Name = "txtFilePath";
            this.txtFilePath.ReadOnly = true;
            this.txtFilePath.Size = new System.Drawing.Size(441, 36);
            this.txtFilePath.TabIndex = 19;
            this.txtFilePath.TabStop = false;
            // 
            // BtnEnrollment
            // 
            this.BtnEnrollment.BackColor = System.Drawing.Color.Transparent;
            this.BtnEnrollment.BackgroundImage = global::eDoktor.Taikoban.FaceInsert.Properties.Resources.btn_1;
            this.BtnEnrollment.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
            this.BtnEnrollment.FlatAppearance.BorderSize = 0;
            this.BtnEnrollment.FlatAppearance.MouseDownBackColor = System.Drawing.Color.Transparent;
            this.BtnEnrollment.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.BtnEnrollment.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.BtnEnrollment.Font = new System.Drawing.Font("メイリオ", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
            this.BtnEnrollment.ForeColor = System.Drawing.SystemColors.Window;
            this.BtnEnrollment.Location = new System.Drawing.Point(350, 625);
            this.BtnEnrollment.Name = "BtnEnrollment";
            this.BtnEnrollment.Size = new System.Drawing.Size(210, 44);
            this.BtnEnrollment.TabIndex = 20;
            this.BtnEnrollment.Text = "登録";
            this.BtnEnrollment.UseVisualStyleBackColor = false;
            this.BtnEnrollment.Click += new System.EventHandler(this.BtnEnrollment_Click);
            this.BtnEnrollment.MouseDown += new System.Windows.Forms.MouseEventHandler(this.btn_MouseDown);
            this.BtnEnrollment.MouseEnter += new System.EventHandler(this.btn_MouseEnter);
            this.BtnEnrollment.MouseLeave += new System.EventHandler(this.btn_MouseLeave);
            // 
            // FaceInsertFileSelect
            // 
            this.AllowDrop = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackgroundImage = global::eDoktor.Taikoban.FaceInsert.Properties.Resources.main3;
            this.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.ClientSize = new System.Drawing.Size(644, 761);
            this.Controls.Add(this.BtnEnrollment);
            this.Controls.Add(this.txtFilePath);
            this.Controls.Add(this.btnReturn);
            this.Controls.Add(this.BtnSelect);
            this.Controls.Add(this.txtStaffId);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.DoubleBuffered = true;
            this.MaximumSize = new System.Drawing.Size(660, 800);
            this.MinimumSize = new System.Drawing.Size(660, 800);
            this.Name = "FaceInsertFileSelect";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "ファイル選択画面";
            this.DragDrop += new System.Windows.Forms.DragEventHandler(this.FileSelect_DragDrop);
            this.DragEnter += new System.Windows.Forms.DragEventHandler(this.FileSelect_DragEnter);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnReturn;
        private System.Windows.Forms.Button BtnSelect;
        private System.Windows.Forms.TextBox txtStaffId;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox txtFilePath;
        private System.Windows.Forms.Button BtnEnrollment;
    }
}