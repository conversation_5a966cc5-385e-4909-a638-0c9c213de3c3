﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using eDoktor.Taikoban.FaceInterprocess;

namespace eDoktor.Taikoban.FaceAuthServer
{
    /// <summary>
    /// 顔テンプレート登録確認要求
    /// </summary>
    public class ExecuteFaceTemplateRegConfirmReq
    {
        /// <summary>
        /// データベース
        /// </summary>
        DB _db;

        /// <summary>
        /// 設定情報
        /// </summary>
        SettingsInfo _settingInfo;

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="database">データベース</param> 
        /// <param name="settingInfo">設定情報</param>
        public ExecuteFaceTemplateRegConfirmReq(Database database, SettingsInfo settingInfo)
        {
            this._db = new DB(database);
            this._settingInfo = settingInfo;
        }

        /// <summary>
        /// 顔テンプレート登録確認要求コマンドに対する受信処理です
        /// </summary>
        /// <param name="packet">受信コマンドの内容</param>
        public XElement Execute(FaceInterprocess.Packet packet)
        {
            try
            {
                var isCorrectData = this.GetRequestData(packet, out FaceTemplateRegConfirmReqData requestData);

                if (isCorrectData == false)
                {
                    Trace.OutputErrorTrace("ExecuteCmdQueryFaceTemplateRegConfirmReq：受信データが不正です。フォーマットエラーを返します。");

                    return this.SetFailElem(EnumErrorId.InputDataErr);
                }

                var errId = EnumErrorId.NoErr;

                // 指定の職員IDが存在するかチェック
                var loginUserInfo = this._db.GetUserInfo(requestData.LogonId, out errId);

                if (errId != EnumErrorId.NoErr)
                {
                    return this.SetFailElem(errId);
                }

                // 登録職員あり
                Trace.OutputDebugTrace("アカウント情報あり");

                // 対象のレコードが存在するかチェックする
                var isGetTemplate = this._db.GetTemplateData(loginUserInfo.account_id, out List<TemplateInfo> templateDataInfoList);

                if (isGetTemplate == false)
                {
                    return this.SetFailElem(EnumErrorId.DbErr);
                }

                var registState = EnumRegistState.NoRegist;

                if (templateDataInfoList.Count >= this._settingInfo.face_template_regist_max_count)
                {
                    // 取得したテンプレートリストの数が、設定．顔テンプレート登録上限に達している場合、「登録あり(上限)」で「正常終了」とする。
                    Trace.OutputDebugTrace("登録あり（上限）");
                    registState = EnumRegistState.RegistMax;
                }
                else if (templateDataInfoList.Count > 0)

                {
                    // 取得したテンプレートリストの数が、設定．顔テンプレート登録上限より少ない場合、「登録あり」で「正常終了」とする。
                    Trace.OutputDebugTrace("登録あり");
                    registState = EnumRegistState.Regist;
                }
                else
                {
                    // 顔テンプレートが登録されていない場合、「登録なし」で「正常終了」とする。
                    Trace.OutputDebugTrace("登録なし");
                    registState = EnumRegistState.NoRegist;
                }

                return this.SetSuccessElem(registState, loginUserInfo);
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return this.SetFailElem(EnumErrorId.Exception);
            }
        }

        /// <summary>
        /// リクエストデータ取得
        /// </summary>
        /// <param name="packet">受信コマンドの内容</param>
        /// <param name="requestData">リクエストデータ</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        private bool GetRequestData(FaceInterprocess.Packet packet, out FaceTemplateRegConfirmReqData requestData)
        {
            requestData = new FaceTemplateRegConfirmReqData();

            try
            {
                requestData.ToObject(packet.Data);
                return true;
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return false;
            }
        }

        /// <summary>
        /// 成功時応答データ作成
        /// </summary>
        /// <param name="registState">登録状態</param>
        /// <param name="userInfo">ユーザ情報</param>
        /// <returns></returns>
        private XElement SetSuccessElem(EnumRegistState registState, LoginUserInfo userInfo)
        {
            var elementData = new ElementDataCreate();
            elementData.CreateElem(nameof(FaceTemplateRegConfirmResData.ProcResult), (int)EnumProcResult.Success);
            elementData.CreateElem(nameof(FaceTemplateRegConfirmResData.ErrorId), (int)EnumErrorId.NoErr);
            elementData.CreateElem(nameof(FaceTemplateRegConfirmResData.RegistState), (int)registState);
            elementData.CreateElem(nameof(FaceTemplateRegConfirmResData.AccountId), userInfo.account_id);
            elementData.CreateElem(nameof(FaceTemplateRegConfirmResData.UserName), userInfo.name);

            Trace.OutputDebugTrace(elementData.StrElements);

            return elementData.Elements;
        }

        /// <summary>
        /// 失敗時応答データ作成
        /// </summary>
        /// <param name="errorId">エラーID</param>
        /// <returns></returns>
        private XElement SetFailElem(EnumErrorId errorId)
        {
            var elementData = new ElementDataCreate();
            elementData.CreateElem(nameof(FaceTemplateRegConfirmResData.ProcResult), (int)EnumProcResult.Fail);
            elementData.CreateElem(nameof(FaceTemplateRegConfirmResData.ErrorId), (int)errorId);
            elementData.CreateElem(nameof(FaceTemplateRegConfirmResData.RegistState), (int)EnumRegistState.NoRegist);
            elementData.CreateElem(nameof(FaceTemplateRegConfirmResData.AccountId), string.Empty);
            elementData.CreateElem(nameof(FaceTemplateRegConfirmResData.UserName), string.Empty);

            Trace.OutputDebugTrace(elementData.StrElements);

            return elementData.Elements;
        }
    }
}
