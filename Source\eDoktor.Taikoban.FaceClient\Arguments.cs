﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Drawing;
using System.Threading.Tasks;

namespace eDoktor.Taikoban.FaceClient
{
    /// <summary>
    /// COMから呼び出されるときの起動時引数
    /// </summary>
    public class Arguments
    {
        /// <summary>
        /// 親Windowのハンドル
        /// </summary>
        public int ParentHandle { get; private set; }

        /// <summary>
        /// インターフェースのハンドル
        /// </summary>
        public int InterfaceHandle { get; private set; }

        /// <summary>
        /// リトライ回数
        /// </summary>
        public int RetryCount { get; private set; }

        /// <summary>
        /// 表示位置
        /// </summary>
        public Point DispPoint { get; private set; }

        /// <summary>
        /// タイトル
        /// </summary>
        public string Title { get; private set; }

        /// <summary>
        /// コマンドライン引数を解析
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public bool AnalyzeArguments(string[] args)
        {
            DispPoint = new Point(0, 0);
            RetryCount = 0;
            ParentHandle = 0;
            InterfaceHandle = 0;
            Title = "__*AoSMPcfjIHAG9dKF8j0T5g__"; // タイトルの指定が正しくないとTaikobanは子画面を見つけることができない。

            if (args == null)
            {
                eDoktor.Common.Trace.OutputTrace("引数が指定されていませんでした。");
                return false;
            }
            if (args.Length == 0)
            {
                eDoktor.Common.Trace.OutputTrace("引数が指定されていませんでした。");
                return false;
            }
            int temp = 0;
            int temp1 = 0;

            bool retval = true;
            if (args.Length == 1)
            {
                if (int.TryParse(args[0], out temp) == true)
                {
                    ParentHandle = temp;
                    eDoktor.Common.Trace.OutputDebugTrace(string.Format("親Window ハンドル={0}", ParentHandle));
                }
                else
                {
                    eDoktor.Common.Trace.OutputDebugTrace("親Window ハンドルの値が不正です。");
                    return false;
                }
            }
            else if (args.Length >= 2)
            {
                if (int.TryParse(args[0], out temp) == true)
                {
                    ParentHandle = temp;
                    eDoktor.Common.Trace.OutputDebugTrace(string.Format("親Window ハンドル={0}", ParentHandle));
                }
                if (int.TryParse(args[1], out temp) == true)
                {
                    InterfaceHandle = temp;
                    eDoktor.Common.Trace.OutputDebugTrace(string.Format("インターフェースWindow ハンドル={0}", InterfaceHandle));
                }

                if (args.Length == 3)
                {
                    if (int.TryParse(args[2], out temp) == true)
                    {
                        int posY = 0;
                        DispPoint = new Point(temp, posY);
                        eDoktor.Common.Trace.OutputDebugTrace(string.Format("表示位置X,Y = ({0}, {1}) ** Yは省略値です。", DispPoint.X, DispPoint.Y));
                    }
                }
                else if (args.Length == 4)
                {
                    if (int.TryParse(args[2], out temp) == true && int.TryParse(args[3], out temp1) == true)
                    {
                        DispPoint = new Point(temp, temp1);
                        eDoktor.Common.Trace.OutputDebugTrace(string.Format("表示位置X,Y = ({0}, {1})", DispPoint.X, DispPoint.Y));
                    }
                    else
                    {
                        DispPoint = new Point(0, 0);
                        eDoktor.Common.Trace.OutputDebugTrace(string.Format("表示位置X,Y = ({0}, {1}) ** XまたはYが整数型でないため省略値にしました。", DispPoint.X, DispPoint.Y));
                    }
                }
                else
                {
                    if (int.TryParse(args[2], out temp) == true && int.TryParse(args[3], out temp1) == true)
                    {
                        DispPoint = new Point(temp, temp1);
                        eDoktor.Common.Trace.OutputDebugTrace(string.Format("表示位置X,Y = ({0}, {1})", DispPoint.X, DispPoint.Y));
                    }
                    else
                    {
                        DispPoint = new Point(0, 0);
                        eDoktor.Common.Trace.OutputDebugTrace(string.Format("表示位置X,Y = ({0}, {1}) ** XまたはYが整数型でないため省略値にしました。", DispPoint.X, DispPoint.Y));
                    }
                    if (int.TryParse(args[4], out temp) == true)
                    {
                        RetryCount = temp;
                        eDoktor.Common.Trace.OutputDebugTrace(string.Format("認証リトライ回数={0}", RetryCount));
                    }
                    else
                    {
                        RetryCount = 0;
                        eDoktor.Common.Trace.OutputDebugTrace(string.Format("認証リトライ回数={0} ** リトライ回数が整数型でないため省略値にしました。", RetryCount));
                    }
                    if (args.Length == 6)
                    {
                        Title = args[5];
                        eDoktor.Common.Trace.OutputDebugTrace(string.Format("タイトル={0}", Title));
                    }
                }
            }
            else
            {
                retval = false;
                eDoktor.Common.Trace.OutputTrace("引数が指定されていませんでした。");
            }
            return retval;
        }
    }
}
