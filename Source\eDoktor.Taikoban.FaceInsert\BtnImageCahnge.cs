﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace eDoktor.Taikoban.FaceInsert
{
    class BtnImageCahnge
    {

        /// <summary>
        /// マウスカーソルがボタン領域に入った時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        public void btn_MouseEnter(object sender, EventArgs e)
        {
            try
            {
                Button btn = (Button)sender;
                btn.BackgroundImage = eDoktor.Taikoban.FaceInsert.Properties.Resources.btn_2;
                btn.ForeColor = System.Drawing.Color.FromArgb(24, 109, 178);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }

        }

        /// <summary>
        /// マウスカーソルがボタン領域から抜けた時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        public void btn_MouseLeave(object sender, EventArgs e)
        {
            try
            {
                Button btn = (Button)sender;
                btn.BackgroundImage = eDoktor.Taikoban.FaceInsert.Properties.Resources.btn_1;
                btn.ForeColor = System.Drawing.SystemColors.Window;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// ボタン押下時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        public void btn_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            try
            {
                Button btn = (Button)sender;
                btn.BackgroundImage = eDoktor.Taikoban.FaceInsert.Properties.Resources.btn_3;
                btn.ForeColor = System.Drawing.SystemColors.Window;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

    }
}
