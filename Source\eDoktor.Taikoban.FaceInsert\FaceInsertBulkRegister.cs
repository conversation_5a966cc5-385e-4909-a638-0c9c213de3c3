﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using eDoktorTaikoban.FaceClientCommon;

namespace eDoktor.Taikoban.FaceInsert
{
    /// <summary>
    /// 一括登録画面
    /// </summary>
    public partial class FaceInsertBulkRegister : Form
    {
        #region Private Const

        /// <summary>
        /// CSVファイルパス ConfigKey
        /// </summary>
        private const string CONFIG_KEY_CSV_FILE_PATH = "CsvInputFilePath";

        /// <summary>
        /// CSVファイルパス ConfigKey
        /// </summary>
        private const string CONFIG_KEY_CSV_FILE_NAME = "CsvInputFileName";

        /// <summary>
        /// ファイル選択ダイアログの表示拡張子フィルタ
        /// </summary>
        private const string CSV_FILE_EXTENSION_FILTER = "CSV File(*.csv)|*.csv;";

        #endregion

        #region Private Fields

        /// <summary>
        /// 要求送信クラス
        /// </summary>
        RequestSendClass _requestSend = new RequestSendClass();

        /// <summary>
        /// ボタンアクションクラス
        /// </summary>
        BtnImageCahnge _btnImageCahnge = new BtnImageCahnge();

        /// <summary>
        /// 設定情報
        /// </summary>
        private SettingsInfo _settingInfo;

        #endregion

        #region Constructors

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="settingInfo">設定情報</param>
        public FaceInsertBulkRegister(SettingsInfo settingInfo)
        {
            InitializeComponent();
            this._settingInfo = settingInfo;
        }

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="requestSend">要求送信クラス</param>
        /// <param name="settingInfo">設定情報</param>
        public FaceInsertBulkRegister(RequestSendClass requestSend, SettingsInfo settingInfo)
            : this(settingInfo)
        {
            this._requestSend = requestSend;
        }

        #endregion

        /// <summary>
        /// 選択ボタン押下処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnSelect_Click(object sender, EventArgs e)
        {
            // 画像ファイルを開く
            ImageFileOpen();
        }

        /// <summary> 
        /// ファイルを開くダイアログボックスを表示して画像ファイルを開く 
        /// </summary> 
        private void ImageFileOpen()
        {
            OpenFileDialog ofd = new OpenFileDialog();

            // 開くCSVのファイルの初期ファイル名を指定する
            ofd.FileName = eDoktor.Common.Configuration.AppSetting(CONFIG_KEY_CSV_FILE_NAME);

            // CSVが保存されている先のパスを指定する
            ofd.InitialDirectory = eDoktor.Common.Configuration.AppSetting(CONFIG_KEY_CSV_FILE_PATH);

            ofd.Filter = CSV_FILE_EXTENSION_FILTER;

            ofd.FilterIndex = 1;

            ofd.Title = "開くファイルを選択してください";

            ofd.RestoreDirectory = true;

            ofd.CheckFileExists = true;

            ofd.CheckPathExists = true;

            // ファイルが選択された時点で、ファイルパス欄にファイルパスを反映する。
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                txtFilePath.Text = ofd.FileName;
            }
        }

        /// <summary>
        /// ドロップ操作完了時
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FileSelect_DragDrop(object sender, DragEventArgs e)
        {
            txtFilePath.Text = string.Empty;

            string[] files = (string[])e.Data.GetData(DataFormats.FileDrop, false);

            var file = files.FirstOrDefault();

            if (file == null || !(file.EndsWith(".csv", StringComparison.OrdinalIgnoreCase)))
            {
                // CSVファイルではありません。
                MessageBox.Show(Messages.Message(MessageId.Message026));
                return;
            }

            txtFilePath.Text = file;
        }

        /// <summary>
        /// アイテムドラッグ時
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FileSelect_DragEnter(object sender, DragEventArgs e)
        {
            e.Effect = DragDropEffects.All;
        }

        /// <summary>
        /// 登録ボタン押下時
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnEnrollment_Click(object sender, EventArgs e)
        {
            //ファイルパスが未入力の場合
            if (txtFilePath.TextLength < 1)
            {
                // CSVファイルを選択してください。
                MessageBox.Show(Messages.Message(MessageId.Message027));
                return;
            }

            // 一括登録中画面を表示する
            using (FaceInsertRegistering faceInsertRegistering = new FaceInsertRegistering(this.txtFilePath.Text, this._settingInfo))
            {
                // 登録中画面はモーダル表示したいので親画面は表示したままとする。
                faceInsertRegistering.ShowDialog(this);
                this.Show();
            }
        }

        /// <summary>
        /// 戻るボタン押下時
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnReturn_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// ファイルパス指定時(テキスト変更時)
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void txtFilePath_TextChanged(object sender, EventArgs e)
        {
            this.BtnEnrollment.Select();
        }


        /// <summary>
        /// マウスカーソルがボタン領域に入った時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseEnter(object sender, EventArgs e)
        {
            _btnImageCahnge.btn_MouseEnter(sender, e);
        }

        /// <summary>
        /// マウスカーソルがボタン領域から抜けた時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseLeave(object sender, EventArgs e)
        {
            _btnImageCahnge.btn_MouseLeave(sender, e);
        }

        /// <summary>
        /// ボタン押下時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            _btnImageCahnge.btn_MouseDown(sender, e);
        }
    }
}
