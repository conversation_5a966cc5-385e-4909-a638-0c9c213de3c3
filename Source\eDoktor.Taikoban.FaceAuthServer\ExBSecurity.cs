﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Runtime.InteropServices;

namespace eDoktor.Taikoban.FaceAuthServer
{
    public class ExBSecurity
    {
        #region Fields
        private static readonly string ProgID = "eDoktor.Security.SecurityWrapper";
        private static readonly string EncoderName = "MakeSecretCode";
        private static readonly string DecoderName = "DecodeSecretCode";
        #endregion

        #region Public Methods
        public static string MakeSecretCode(string message)
        {
            object outProc = null;

            try
            {
                Type type = Type.GetTypeFromProgID(ProgID);
                outProc = Activator.CreateInstance(type);
                string name = EncoderName;
                object[] args = new object[] { message, null };
                var mod = new System.Reflection.ParameterModifier(args.Length);
                //mod[0] = false;		// [in]message
                mod[1] = true;		// [out]secretCode
                System.Reflection.ParameterModifier[] mods = { mod };
                short errorCode = (short)type.InvokeMember(name, System.Reflection.BindingFlags.InvokeMethod, null, outProc, args, mods, null, null);

                if (errorCode == 0)
                {
                    return args[1].ToString();
                }

                throw new System.Exception(string.Format("{0} failed. errorCode={1}", name, errorCode));
            }
            finally
            {
                if ((outProc != null) && (Marshal.IsComObject(outProc)))
                {
                    Marshal.ReleaseComObject(outProc);
                }
            }
        }

        public static string DecodeSecretCode(string secretCode)
        {
            object outProc = null;

            try
            {
                Type type = Type.GetTypeFromProgID(ProgID);
                outProc = Activator.CreateInstance(type);
                string name = DecoderName;
                object[] args = new object[] { secretCode };
                var mod = new System.Reflection.ParameterModifier(args.Length);
                mod[0] = true;		// [ref]secretCode
                System.Reflection.ParameterModifier[] mods = { mod };
                short errorCode = (short)type.InvokeMember(name, System.Reflection.BindingFlags.InvokeMethod, null, outProc, args, mods, null, null);

                if (errorCode == 0)
                {
                    return args[0].ToString();
                }

                throw new System.Exception(string.Format("{0} failed. errorCode={1}", name, errorCode));
            }
            finally
            {
                if ((outProc != null) && (Marshal.IsComObject(outProc)))
                {
                    Marshal.ReleaseComObject(outProc);
                }
            }
        }
        #endregion
    }
}
