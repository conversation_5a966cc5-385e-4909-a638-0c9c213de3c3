﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceProcess;
using System.Text;
using System.Threading.Tasks;

namespace eDoktor.Taikoban.FaceAuthServerService
{
    static class Program
    {
        /// <summary>
        /// アプリケーションのメイン エントリ ポイントです。
        /// </summary>
        static void Main()
        {
            try
            {
                System.Environment.CurrentDirectory = System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);

                // ログ設定
                eDoktor.Common.Trace.EnableTrace = false;   // ログファイルのパスを設定するまでにエラーが発生するとログファイルのパスがデフォルトに確定してしまうのを防ぐ

                bool enableTrace = eDoktor.Common.Configuration.AppSetting("EnableTrace", false);
                bool enableDebugTrace = eDoktor.Common.Configuration.AppSetting("EnableDebugTrace", false);
                bool enableDetailedExceptionTrace = eDoktor.Common.Configuration.AppSetting("EnableDetailedExceptionTrace", false);
                string containingFolderPath = eDoktor.Common.Configuration.AppSetting("ContainingFolderPath");
                int traceTerm = eDoktor.Common.Configuration.AppSetting("TraceTermInDays", 10);

                if (!string.IsNullOrEmpty(containingFolderPath) && !System.IO.Directory.Exists(containingFolderPath))
                {
                    try
                    {
                        System.IO.Directory.CreateDirectory(containingFolderPath);
                        System.IO.FileAttributes uAttribute = System.IO.File.GetAttributes(containingFolderPath);
                        System.IO.File.SetAttributes(containingFolderPath, uAttribute | System.IO.FileAttributes.Hidden);
                    }
                    catch
                    {
                    }
                }

                eDoktor.Common.Trace.EnableTrace = enableTrace;
                eDoktor.Common.Trace.EnableDebugTrace = enableDebugTrace;
                eDoktor.Common.Trace.EnableDetailedExceptionTrace = enableDetailedExceptionTrace;
                eDoktor.Common.Trace.ContainingFolderPath = containingFolderPath;
                eDoktor.Common.Trace.TraceTerm = TimeSpan.FromDays(traceTerm);

                ServiceBase[] ServicesToRun = new ServiceBase[] { new FaceAuthServerService() };
                ServiceBase.Run(ServicesToRun);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }
    }
}
