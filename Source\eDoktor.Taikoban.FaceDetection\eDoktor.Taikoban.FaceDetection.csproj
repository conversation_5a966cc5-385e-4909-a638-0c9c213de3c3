﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{C772955C-661C-45FB-9A10-47A91CBE9209}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>eDoktor.Taikoban.FaceDetection</RootNamespace>
    <AssemblyName>eDoktor.Taikoban.FaceDetection</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="eDoktor.Common">
      <HintPath>..\eDoktor.Taikoban.AppExtension\eDoktor.Common.dll</HintPath>
    </Reference>
    <Reference Include="eDoktor.Taikoban.FaceImageCntl">
      <HintPath>..\eDoktor.Taikoban.FaceImageCntl\bin\x64\Release\eDoktor.Taikoban.FaceImageCntl.dll</HintPath>
    </Reference>
    <Reference Include="OpenCvSharp">
      <HintPath>..\packages\OpenCvSharp4.4.5.2.20210404\lib\net461\OpenCvSharp.dll</HintPath>
    </Reference>
    <Reference Include="OpenCvSharp.Extensions">
      <HintPath>..\packages\OpenCvSharp4.4.5.2.20210404\lib\net461\OpenCvSharp.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="MessageDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MessageDialog.Designer.cs">
      <DependentUpon>MessageDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="LivenessCheckDetection.cs" />
    <Compile Include="FaceDetection.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="haarcascade_eye_tree_eyeglasses.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="haarcascade_frontalface_alt_tree.xml" />
    <None Include="Resources\liveness_ok_on.png" />
    <None Include="Resources\liveness_ok_off.png" />
    <None Include="Resources\liveness_ok_hover.png" />
    <None Include="Resources\Vector Layer 3.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\eDoktor.Taikoban.FaceAuthCommon\eDoktor.Taikoban.FaceAuthCommon.csproj">
      <Project>{7a2d3a65-364e-43ad-93d9-9c1057e9c4b9}</Project>
      <Name>eDoktor.Taikoban.FaceAuthCommon</Name>
    </ProjectReference>
    <ProjectReference Include="..\eDoktorTaikoban.FaceClientCommon\eDoktorTaikoban.FaceClientCommon.csproj">
      <Project>{69caf41b-94a8-4d86-9fa0-9e01ff4bfc6a}</Project>
      <Name>eDoktorTaikoban.FaceClientCommon</Name>
    </ProjectReference>
    <ProjectReference Include="..\LivenessCheckSharp\LivenessCheckSharp.vcxproj">
      <Project>{7eb371fc-25e4-4d56-908a-b9a4418d80d2}</Project>
      <Name>LivenessCheckSharp</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="MessageDialog.resx">
      <DependentUpon>MessageDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>