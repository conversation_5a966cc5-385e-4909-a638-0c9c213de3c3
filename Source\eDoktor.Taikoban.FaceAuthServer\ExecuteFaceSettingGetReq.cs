﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceInterprocess;

namespace eDoktor.Taikoban.FaceAuthServer
{
    /// <summary>
    /// 設定取得要求
    /// </summary>
    public class ExecuteFaceSettingGetReq
    {
        /// <summary>
        /// データベース
        /// </summary>
        DB _db;

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="database">データベース</param>
        public ExecuteFaceSettingGetReq(Database database)
        {
            this._db = new DB(database);
        }

        /// <summary>
        /// 設定取得要求コマンドに対する受信処理です
        /// </summary>
        /// <param name="packet">受信コマンドの内容</param>
        public XElement Execute(FaceInterprocess.Packet packet)
        {
            try
            {
                var isCorrectData = this.GetRequestData(packet, out FaceSettingGetReqData requestData);

                if (isCorrectData == false)
                {
                    Trace.OutputErrorTrace("ExecuteCmdQueryFaceSettingGetReq：受信データが不正です。フォーマットエラーを返します。");

                    return this.SetFailElem(EnumErrorId.InputDataErr);
                }

                // DBから各設定値を取得する
                var result = this.InitialSettings(out FaceAuthSettingsInfo.SettingsInfo settingInfo, out Dictionary<string, string> messageDic, out List<string> useDeviceList, out List<string> notUseDeviceList);

                if (result == false)
                {
                    Trace.OutputErrorTrace("設定値の取得に失敗しました。");

                    return this.SetFailElem(EnumErrorId.DbErr);
                }
                else
                {
                    return this.SetSuccessElem(settingInfo, messageDic, useDeviceList, notUseDeviceList);
                }
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return this.SetFailElem(EnumErrorId.Exception);
            }
        }

        /// <summary>
        /// DBから各設定値を取得する
        /// </summary>
        /// <param name="settingInfo">設定情報</param>
        /// <param name="messageDic">メッセージ情報</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        public bool InitialSettings(out FaceAuthSettingsInfo.SettingsInfo settingInfo, out Dictionary<string, string> messageDic)
        {
            settingInfo = null;
            messageDic = new Dictionary<string, string>();

            try
            {
                int retryCount = eDoktor.Common.Configuration.AppSetting("MaxFaceRetryCount", 1);
                var isGetSettingResult = false;

                while (retryCount > 0)
                {
                    // 設定値を取得する
                    settingInfo = this._db.GetFaceAuthSettings();

                    if (settingInfo != null)
                    {
                        isGetSettingResult = true;
                        break;
                    }

                    retryCount--;
                }

                if (isGetSettingResult == false)
                {
                    eDoktor.Common.Trace.OutputErrorTrace("設定値の取得に失敗しましたので、終了します。");
                    return false;
                }

                // メッセージマスタを取得する
                var isGetMessageMst = this._db.GetFaceAuthMessages(out messageDic);

                if (isGetMessageMst == false)
                {
                    eDoktor.Common.Trace.OutputErrorTrace("メッセージマスタの取得に失敗しましたので、終了します。");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return false;
            }
        }

        /// <summary>
        /// DBから各設定値を取得する
        /// </summary>
        /// <param name="settingInfo">設定情報</param>
        /// <param name="messageDic">メッセージ情報</param>
        /// <param name="useDeviceList">デバイスホワイトリスト</param>
        /// <param name="notUseDeviceList">デバイスブラックリスト</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        public bool InitialSettings(out FaceAuthSettingsInfo.SettingsInfo settingInfo, out Dictionary<string, string> messageDic, out List<string> useDeviceList, out List<string> notUseDeviceList)
        {
            useDeviceList = new List<string>();
            notUseDeviceList = new List<string>();

            if (!this.InitialSettings(out settingInfo, out messageDic))
            {
                return false;
            }

            // 使用可能なデバイスを取得する
            var resGetUseDevice = this._db.GetFaceAuthUseDevice(out useDeviceList);

            if (!resGetUseDevice)
            {
                eDoktor.Common.Trace.OutputErrorTrace("使用可能なデバイスの取得に失敗しました。");
            }

            // 使用不可能なデバイスを取得する
            var resGetNotUseDevice = this._db.GetFaceAuthNotUseDevice(out notUseDeviceList);

            if (!resGetNotUseDevice)
            {
                eDoktor.Common.Trace.OutputErrorTrace("使用不可能なデバイスの取得に失敗しました。");
            }

            return true;
        }

        /// <summary>
        /// リクエストデータ取得
        /// </summary>
        /// <param name="packet">受信コマンドの内容</param>
        /// <param name="requestData">リクエストデータ</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        private bool GetRequestData(eDoktor.Taikoban.FaceInterprocess.Packet packet, out FaceSettingGetReqData requestData)
        {
            requestData = new FaceSettingGetReqData();

            try
            {
                requestData.ToObject(packet.Data);
                return true;
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return false;
            }
        }

        /// <summary>
        /// 成功時応答データ作成
        /// </summary>
        /// <param name="settingInfo">設定情報</param>
        /// <param name="meesageDic">メッセージ情報</param>
        /// <param name="useDeviceList">デバイスホワイトリスト</param>
        /// <param name="notUseDeviceList">デバイスブラックリスト</param>
        /// <returns></returns>
        private XElement SetSuccessElem(FaceAuthSettingsInfo.SettingsInfo settingInfo, Dictionary<string, string> meesageDic, List<string> useDeviceList, List<string> notUseDeviceList)
        {
            var elementData = new ElementDataCreate();
            elementData.CreateElem(nameof(FaceSettingGetResData.ProcResult), (int)EnumProcResult.Success);
            elementData.CreateElem(nameof(FaceSettingGetResData.ErrorId), (int)EnumErrorId.NoErr);

            var settingElement = new XElement(FaceSettingGetResData.SETTING);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.AuthMode), (int)settingInfo.auth_mode);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.RetryCount), settingInfo.retry_count);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceTemplateRegistMaxCount), settingInfo.face_template_regist_max_count);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceSearchWaitTime), settingInfo.face_search_wait_time);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.AuthStartWaitTime), settingInfo.auth_start_wait_time);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.Timeout), settingInfo.time_out);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.MaskScoreThreshold), settingInfo.mask_score_threshold);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.TemplateQualityThresholdAuth), settingInfo.template_quality_threshold_auth);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.TemplateQualityThresholdInsert), settingInfo.template_quality_threshold_insert);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceAuthThreshold), settingInfo.face_auth_threshold);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceRecogDataType), (int)settingInfo.face_recog_data_type);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsEstimateGenderAge), settingInfo.is_estimate_gender_age);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceRecogQualityThreshold), settingInfo.face_recog_quality_threshold);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceSearchCountMax), settingInfo.face_search_count_max);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceSearchScaleMin), settingInfo.face_search_scale_min);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceSearchScaleMax), settingInfo.face_search_scale_max);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.InputImageWidth), settingInfo.input_image_width);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.InputImageHeight), settingInfo.input_image_height);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsHoldTemplateData), settingInfo.is_hold_template_data);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.TemplateGetWaitTime), settingInfo.template_get_wait_time);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsDisplayAuthUserDialog), settingInfo.is_display_auth_user_daialog);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsDisplayAuthImage), settingInfo.is_display_auth_image);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsUseEyeBlink), settingInfo.is_use_eye_blink);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsUseFaceForBlinking), settingInfo.is_use_face_for_blinking);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceSizeMax), settingInfo.face_size_max);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceSizeMin), settingInfo.face_size_min);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.EyeSizeMax), settingInfo.eye_size_max);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.EyeSizeMin), settingInfo.eye_size_min);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.BlinkWaitingTime), settingInfo.blink_waiting_time);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.BlinkCount), settingInfo.blink_count);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.BlinkingTime), settingInfo.blinking_time);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.BlinkAreaX), settingInfo.blink_area_x);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.BlinkAreaY), settingInfo.blink_area_y);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.BlinkAreaWidth), settingInfo.blink_area_width);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.BlinkAreaHeight), settingInfo.blink_area_height);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsDisplayBlinkMessage), settingInfo.is_display_blink_message);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.BlinkMessage), settingInfo.blink_message);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.DeviceWhiteList), string.Join("|", useDeviceList));
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.DeviceBlackList), string.Join("|", notUseDeviceList));
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsDisplayFaceGuide), settingInfo.is_display_face_guide);

            //なりすまし判定設定値
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.TokenFilePath), settingInfo.token_file_path);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceDetectModelFilePath), settingInfo.faceDetectModel_file_path);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceDetectParamFilePath), settingInfo.faceDetectParam_file_path);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FakeModelHFilePath), settingInfo.fakeModelH_file_path);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FakeModelVFilePath), settingInfo.fakeModelV_file_path);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceDirHmodelFilePath), settingInfo.faceDirHModel_file_path);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceDirVmodelFilePath), settingInfo.faceDirVModel_file_path);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.GlassesModelFilePath), settingInfo.glassesModel_file_path);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.MaskModelFilePath), settingInfo.maskModel_file_path);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.EyeDetectModelFilePath), settingInfo.eyeDetectModel_file_path);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.EyeStatusModelFilePath), settingInfo.eyeStatusModel_file_path);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.EyeDirModelFilePath), settingInfo.eyeDirModel_file_path);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.MinFaceWidthParam), settingInfo.minFaceWidth_param);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.MaxFaceWidthParam), settingInfo.maxFaceWidth_param);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceAreaMinRatioParam), settingInfo.faceAreaMinRatio_param);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceAreaMaxRatioParam), settingInfo.faceAreaMaxRatio_param);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.EdgePosErrModeParam), settingInfo.edgePosErrMode_param);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsFakeModeParam), settingInfo.isFakeMode_param);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FakeJudgeThParam), settingInfo.fakeJudgeTh_param);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsFaceDirModeParam), settingInfo.isFaceDirMode_param);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsGlassesModeParam), settingInfo.isGlassesMode_param);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsMaskModeParam), settingInfo.isMaskMode_param);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.MaskJudgeThParam), settingInfo.maskJudgeTh_param);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsEyeModeParam), settingInfo.isEyeMode_param);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsEyeDirModeParam), settingInfo.isEyeDirMode_param);
            //なりすまし判定方法
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsLivenessCheck), settingInfo.is_livenessCheck);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.JudgeTime), settingInfo.judge_time);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.JudgeRatio), settingInfo.judge_ratio);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.LivenessCheckErrorMessage), settingInfo.liveness_check_error_message);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.LivenessCheckTimeoutErrorMessage), settingInfo.liveness_check_timeout_error_message);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.LivenessCheckTimeout), settingInfo.liveness_check_timeout);

            elementData.Elements.Add(settingElement);

            var messageElement = new XElement(FaceSettingGetResData.MESSAGES);

            foreach (var msgData in meesageDic)
            {
                messageElement.Add(new XElement(FaceSettingGetResData.MESSAGE
                                , new XAttribute(FaceSettingGetResData.MESSAGE_ID, msgData.Key)
                                , new XAttribute(FaceSettingGetResData.MESSAGE_VALUE, msgData.Value)));
            }

            elementData.Elements.Add(messageElement);

            Trace.OutputDebugTrace(elementData.StrElements);

            return elementData.Elements;
        }

        /// <summary>
        /// 失敗時応答データ作成
        /// </summary>
        /// <param name="errorId">エラーID</param>
        /// <returns></returns>
        private XElement SetFailElem(EnumErrorId errorId)
        {
            var elementData = new ElementDataCreate();
            elementData.CreateElem(nameof(FaceSettingGetResData.ProcResult), (int)EnumProcResult.Fail);
            elementData.CreateElem(nameof(FaceSettingGetResData.ErrorId), (int)errorId);

            var settingElement = new XElement(FaceSettingGetResData.SETTING);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.AuthMode), (int)EnumAuthMode.Server);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.RetryCount), 0);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceTemplateRegistMaxCount), 0);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceSearchWaitTime), 0);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.AuthStartWaitTime), 0);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.Timeout), 0);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.MaskScoreThreshold), 100);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.TemplateQualityThresholdAuth), 50);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.TemplateQualityThresholdInsert), 50);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceAuthThreshold), 85);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceRecogDataType), (int)GFRL.FaceRecognition.FaceRecogDataType.Standard);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsEstimateGenderAge), false);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceRecogQualityThreshold), 2);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceSearchCountMax), 1);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceSearchScaleMin), 25);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceSearchScaleMax), 100);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.InputImageWidth), 0);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.InputImageHeight), 0);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsHoldTemplateData), false);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.TemplateGetWaitTime), 60);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsUseEyeBlink), false);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsDisplayBlinkMessage), false);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.BlinkMessage), "");
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.DeviceWhiteList), string.Empty);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.DeviceBlackList), string.Empty);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsDisplayFaceGuide), false);

            //なりすまし判定設定値
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.TokenFilePath), "");
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceDetectModelFilePath), "");
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceDetectParamFilePath), "");
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FakeModelHFilePath), "");
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FakeModelVFilePath), "");
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceDirHmodelFilePath), "");
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceDirVmodelFilePath), "");
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.GlassesModelFilePath), "");
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.MaskModelFilePath), "");
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.EyeDetectModelFilePath), "");
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.EyeStatusModelFilePath), "");
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.EyeDirModelFilePath), "");
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.MinFaceWidthParam), DB.MIN_FACE_WIDTH);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.MaxFaceWidthParam), DB.MAX_FACE_WIDTH);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceAreaMinRatioParam), DB.FACE_AREA_MIN_RATIO);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FaceAreaMaxRatioParam), DB.FACE_AREA_MAX_RATIO);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.EdgePosErrModeParam), DB.EDGE_POS_ERR_MODE);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsFakeModeParam), DB.IS_FAKE_MODE);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.FakeJudgeThParam), DB.FAKE_JUDGE_TH);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsFaceDirModeParam), DB.IS_FACE_DIR_MODE);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsGlassesModeParam), DB.IS_GLASSES_MODE);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsMaskModeParam), DB.IS_MASK_MODE);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.MaskJudgeThParam), DB.MASK_JUDGE_TH);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsEyeModeParam), DB.IS_EYE_MODE);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsEyeDirModeParam), DB.IS_EYE_DIR_MODE);
            //なりすまし判定方法
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.IsLivenessCheck), DB.IS_LIVENESS_CHECK);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.JudgeTime), DB.JUDGE_TIME);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.JudgeRatio), DB.JUDGE_RATIO);
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.LivenessCheckErrorMessage), "");
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.LivenessCheckTimeoutErrorMessage), "");
            settingElement.SetAttributeValue(nameof(FaceSettingGetResData.LivenessCheckTimeout), DB.LIVENESS_CHECK_TIMEOUT);

            elementData.Elements.Add(settingElement);

            elementData.Elements.Add(new XElement(FaceSettingGetResData.MESSAGES));

            Trace.OutputDebugTrace(elementData.StrElements);

            return elementData.Elements;
        }
    }
}
