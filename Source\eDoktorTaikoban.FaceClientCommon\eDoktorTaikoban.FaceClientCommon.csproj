﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{69CAF41B-94A8-4D86-9FA0-9E01FF4BFC6A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>eDoktorTaikoban.FaceClientCommon</RootNamespace>
    <AssemblyName>eDoktorTaikoban.FaceClientCommon</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AForge">
      <HintPath>..\packages\AForge.2.2.5\lib\AForge.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Video">
      <HintPath>..\packages\AForge.Video.2.2.5\lib\AForge.Video.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Video.DirectShow">
      <HintPath>..\packages\AForge.Video.DirectShow.2.2.5\lib\AForge.Video.DirectShow.dll</HintPath>
    </Reference>
    <Reference Include="eDoktor.Common">
      <HintPath>..\eDoktor.Taikoban.AppExtension\eDoktor.Common.dll</HintPath>
    </Reference>
    <Reference Include="OpenCvSharp, Version=1.0.0.0, Culture=neutral, PublicKeyToken=6adad1e807fea099, processorArchitecture=MSIL">
      <HintPath>..\packages\OpenCvSharp4.4.5.2.20210404\lib\net461\OpenCvSharp.dll</HintPath>
    </Reference>
    <Reference Include="OpenCvSharp.Extensions, Version=1.0.0.0, Culture=neutral, PublicKeyToken=6adad1e807fea099, processorArchitecture=MSIL">
      <HintPath>..\packages\OpenCvSharp4.4.5.2.20210404\lib\net461\OpenCvSharp.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CameraInfo.cs" />
    <Compile Include="ClientCommonProc.cs" />
    <Compile Include="RequestSendClass.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SearchFaceByCascadeClassifier.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\eDoktor.Taikoban.FaceAuthCommon\eDoktor.Taikoban.FaceAuthCommon.csproj">
      <Project>{7A2D3A65-364E-43AD-93D9-9C1057E9C4B9}</Project>
      <Name>eDoktor.Taikoban.FaceAuthCommon</Name>
    </ProjectReference>
    <ProjectReference Include="..\eDoktor.Taikoban.FaceInterprocess\eDoktor.Taikoban.FaceInterprocess.csproj">
      <Project>{6EB7D3C9-23E9-4FAB-83DB-67272CAA8A0E}</Project>
      <Name>eDoktor.Taikoban.FaceInterprocess</Name>
    </ProjectReference>
    <ProjectReference Include="..\LivenessCheckSharp\LivenessCheckSharp.vcxproj">
      <Project>{7eb371fc-25e4-4d56-908a-b9a4418d80d2}</Project>
      <Name>LivenessCheckSharp</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="haarcascade_frontalface_default.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>