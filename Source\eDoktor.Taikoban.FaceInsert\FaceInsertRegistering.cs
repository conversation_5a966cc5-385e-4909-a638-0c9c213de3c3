﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Text;
using System.Windows.Forms;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using eDoktor.Taikoban.FaceInterprocess;
using eDoktorTaikoban.FaceClientCommon;

namespace eDoktor.Taikoban.FaceInsert
{
    /// <summary>
    /// 一括登録画面の登録中画面
    /// </summary>
    public partial class FaceInsertRegistering : Form
    {
        #region Private Fields

        /// <summary>
        /// 要求送信クラス
        /// </summary>
        RequestSendClass _requestSend = new RequestSendClass();

        /// <summary>
        /// CSVファイルパス
        /// </summary>
        private string _csvFilePath;

        /// <summary>
        /// 設定情報
        /// </summary>
        private SettingsInfo _settingInfo;

        /// <summary>
        /// 処理結果保持リスト
        /// </summary>
        private List<BulkRegistResult> _bulkRegistResultList;

        /// <summary>
        /// 現在処理中の行番号
        /// </summary>
        private int _rowNo = 0;

        /// <summary>
        /// ボタンアクションクラス
        /// </summary>
        BtnImageCahnge _btnImageCahnge = new BtnImageCahnge();

        #endregion

        #region Constructors

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="csvFilePath">CSVファイルパス</param>
        /// <param name="settingInfo">設定情報</param>
        public FaceInsertRegistering(string csvFilePath, SettingsInfo settingInfo)
        {
            InitializeComponent();

            this._csvFilePath = csvFilePath;
            this._settingInfo = settingInfo;
        }

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="requestSend">要求送信クラス</param>
        /// <param name="csvFilePath">CSVファイルパス</param>
        /// <param name="settingInfo">設定情報</param>
        public FaceInsertRegistering(RequestSendClass requestSend, string csvFilePath, SettingsInfo settingInfo)
            : this(csvFilePath, settingInfo)
        {
            this._requestSend = requestSend;
        }

        #endregion

        #region EventHandler

        /// <summary>
        /// フォームロード
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FaceInsertRegistering_Load(object sender, EventArgs e)
        {
            this._bulkRegistResultList = new List<BulkRegistResult>();
            button_cancel.Enabled = true;

            // プログレスバーのアニメーション速度を設定する
            progressBar1.MarqueeAnimationSpeed = 1;
            label_number.Text = string.Empty;

            backgroundWorker1.WorkerReportsProgress = true;
            backgroundWorker1.WorkerSupportsCancellation = true;

            // バックグラウンド操作の実行を開始します。
            backgroundWorker1.RunWorkerAsync();
        }

        /// <summary>
        /// 時間のかかる処理を行う  ここでCSVデータを読み込む。
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BackgroundWorker1_DoWork(object sender, DoWorkEventArgs e)
        {
            BackgroundWorker backgroundWorker = (BackgroundWorker)sender;

            Trace.OutputDebugTrace("CSVファイルの読込みを開始");

            // 選択したCSVファイルのデータを読み込む
            using (StreamReader reader = new StreamReader(_csvFilePath, Encoding.GetEncoding("Shift_JIS")))
            {
                int cnt = 0;

                while (!reader.EndOfStream)
                {
                    // キャンセルされたかを調べる
                    if (backgroundWorker.CancellationPending)
                    {
                        e.Cancel = true;
                        reader.Close();
                        Trace.OutputDebugTrace("キャンセルされました。");
                        return;
                    }

                    // 行を読み込む
                    string currentRow = reader.ReadLine();

                    _rowNo++; // 行数をカウントする

                    Trace.OutputDebugTrace("読込み行数 : {0}", _rowNo);

                    if (_rowNo == 1)
                    {
                        // 1行目はタイトル行のため読み取ばす
                        continue;
                    }

                    // カンマで区切る
                    string[] arrCurrentRow = currentRow.Split(',');

                    var staffId = string.Empty;
                    var imageFilePath = string.Empty;

                    // 読み込んだ値を表示
                    for (int i = 0; i < arrCurrentRow.Length; i++)
                    {
                        Trace.OutputDebugTrace("読込みレコード : {0}", arrCurrentRow[i]);
                        if (i == 0)
                        {
                            staffId = arrCurrentRow[i];
                        }
                        else if (i == 1)
                        {
                            imageFilePath = arrCurrentRow[i];
                        }
                    }

                    if (this._settingInfo.auth_mode == EnumAuthMode.Server)
                    {
                        // サーバモード時
                        // 顔テンプレート登録確認要求等を送受信する
                        this.FaceTemplateRegReq(staffId, imageFilePath);
                    }
                    else
                    {
                        // TODO クライアントモードの処理
                        // クライアントモード時
                        // モードの設定が不正です
                        MessageBox.Show(Messages.Message(MessageId.Message011));
                        this.Close();
                    }

                    System.Threading.Thread.Sleep(1000);

                    cnt++;
                    // ProgressChanged イベントハンドラをコールし、コントロールの表示を変更する
                    backgroundWorker.ReportProgress(cnt);
                }

                // ProgressChangedで取得できる結果を設定する
                e.Result = cnt;
            }

            Trace.OutputDebugTrace("CSVファイルの読込みを終了");
        }

        /// <summary>
        /// コントロールの操作を行う。Dowork完了毎にコールする。
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BackgroundWorker1_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            label_number.Text = e.ProgressPercentage.ToString() + "件登録";
        }

        /// <summary>
        /// 一連の処理が終わった時にコールされる処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BackgroundWorker1_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            try
            {
                if (e.Error != null)
                {
                    label1.Text = "エラー：" + e.Error.Message;
                    label1.Update();
                }
                else if (e.Cancelled)
                {
                    label1.Text = "キャンセルされました。";
                    label1.Update();
                }
                else
                {
                    // 正常終了時
                    int result = (int)e.Result;
                    label_number.Text = result.ToString() + "件登録完了";

                    // 件数表示を再描画する
                    label_number.Update();

                    // 登録が完了した時点でプログレスバーのアニメーションを停止する
                    progressBar1.MarqueeAnimationSpeed = 0;

                    // 行番号もクリアする
                    _rowNo = 0;

                    // 1秒待機
                    System.Threading.Thread.Sleep(1000);

                    this.DisplayFaceInsertRegistResult();
                }

            }
            catch (InvalidOperationException ex)
            {
                Trace.OutputExceptionTrace(ex);
                Trace.OutputErrorTrace("操作が取り消されました。");
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// キャンセルボタン押下処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void button_cancel_Click(object sender, EventArgs e)
        {
            // キャンセルする
            backgroundWorker1.CancelAsync();

            // プログレスバーのアニメーションを停止する
            progressBar1.MarqueeAnimationSpeed = 0;
            this.DisplayFaceInsertRegistResult();
        }


        /// <summary>
        /// マウスカーソルがボタン領域に入った時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseEnter(object sender, EventArgs e)
        {
            _btnImageCahnge.btn_MouseEnter(sender, e);
        }

        /// <summary>
        /// マウスカーソルがボタン領域から抜けた時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseLeave(object sender, EventArgs e)
        {
            _btnImageCahnge.btn_MouseLeave(sender, e);
        }

        /// <summary>
        /// ボタン押下時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            _btnImageCahnge.btn_MouseDown(sender, e);
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 一括登録結果画面を表示する
        /// </summary>
        private void DisplayFaceInsertRegistResult()
        {
            // 一括登録結果画面を表示する
            using (FaceInsertRegistResult faceInsertRegistResult = new FaceInsertRegistResult(this._bulkRegistResultList))
            {
                this.Hide();
                faceInsertRegistResult.ShowDialog(this);
                // 登録中画面を表示する必要はないので、結果画面から戻ってきた際は閉じる。
                this.Close();
            }
        }

        /// <summary>
        /// サーバーとの通信処理を行う
        /// </summary>
        /// <param name="staffId">ログオンID</param>
        /// <param name="imageFilePath">画像ファイルパス</param>
        private void FaceTemplateRegReq(string staffId, string imageFilePath)
        {
            // 顔テンプレート登録確認要求を送信する
            var res = this._requestSend.RequestFaceTemplateRegistCheck(staffId, out FaceTemplateRegConfirmResData resData);

            if (!res)
            {
                var result = new BulkRegistResult();

                result.RowNo = _rowNo;                              // 行番号
                result.LogonId = staffId;                           // ログオンID
                result.LogonName = string.Empty;                    // ユーザ名
                result.IsResult = false;                            // 処理結果(bool)
                result.ErrorMessage = ErrorIdConvert(-1, staffId);  // エラー内容

                this._bulkRegistResultList.Add(result);
                return;
            }

            var errorId = (int)EnumErrorId.NoErr;

            var isResult = false;

            if (resData.ProcResult != EnumProcResult.Success)
            {
                // 処理結果が異常終了
                errorId = (int)resData.ErrorId;
            }
            else if (resData.RegistState == EnumRegistState.RegistMax)
            {
                // 応答結果が「上限」の場合
                Trace.OutputDebugTrace("RegistState:{0}", Messages.Message(MessageId.Message009));
                // 上限に達している場合は、顔テンプレート登録要求(サーバー認証モード)を送信しない為、エラーIDが付与されず、結果画面のエラー内容に反映されない為、ここで設定する。
                errorId = (int)EnumErrorId.HoldOver;
            }
            else
            {
                // 処理結果が正常終了
                isResult = this.RegistFaceTemplate(resData.AccountId, imageFilePath, out errorId);
            }

            var bulkRegistResult = new BulkRegistResult();
            bulkRegistResult.RowNo = _rowNo;                                    // 行番号
            bulkRegistResult.LogonId = staffId;                                 // ログオンID
            bulkRegistResult.LogonName = resData.UserName;                      // ユーザ名
            bulkRegistResult.IsResult = isResult;                               // 処理結果(bool)
            bulkRegistResult.ErrorMessage = ErrorIdConvert(errorId, staffId);   // エラー内容

            this._bulkRegistResultList.Add(bulkRegistResult);
        }

        /// <summary>
        /// CSVで指定しているファイルパスの画像とIDで顔テンプレート登録処理を行う
        /// </summary>
        /// <param name="accountId">アカウントID</param>
        /// <param name="imageFilePath">画像ファイルパス</param>
        /// <param name="errId">処理結果エラーID</param>
        /// <returns>処理結果(true：正常終了、false：異常終了)</returns>
        private bool RegistFaceTemplate(int accountId, string imageFilePath, out int errId)
        {
            errId = (int)EnumErrorId.NoErr;

            try
            {
                var imageData = this.GetImafeFromFilePath(imageFilePath);
                if (imageData == null)
                {
                    errId = (int)EnumErrorId.InputDataErr;
                    return false;
                }

                // base64で暗号化する。
                var imageDataStr = Convert.ToBase64String(imageData);

                // 顔テンプレート登録要求(サーバー認証モード)を送信する
                var res = this._requestSend.RequestFaceTemplateRegistServerMode(accountId, imageDataStr, out FaceTemplateRegServerResData faceTemplateRegResData);

                if (!res)
                {
                    errId = -1;
                    return false;
                }

                // 応答結果を設定する
                errId = (int)faceTemplateRegResData.ErrorId;

                return faceTemplateRegResData.ProcResult == EnumProcResult.Success;
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                errId = (int)EnumErrorId.Exception;
            }

            return false;
        }

        /// <summary>
        /// ファイルパスから画像を取得
        /// </summary>
        /// <param name="imageFilePath"></param>
        /// <returns></returns>
        private byte[] GetImafeFromFilePath(string imageFilePath)
        {
            byte[] imageData = null;

            try
            {
                // ファイルが存在しているかチェックする
                if (File.Exists(imageFilePath) == false)
                {
                    Trace.OutputErrorTrace("ファイルが存在しません。");
                }
                else
                {
                    // ファイルを開く
                    using (var fs = new FileStream(imageFilePath, FileMode.Open, FileAccess.Read))
                    {
                        // ファイルを読み込むバイト型配列を作成する
                        imageData = new byte[fs.Length];

                        // ファイルの内容をすべて読み込む
                        fs.Read(imageData, 0, imageData.Length);

                        // 閉じる
                        fs.Close();
                    }
                }
            }
            catch (FileNotFoundException ex)
            {
                Trace.OutputExceptionTrace(ex);
                Trace.OutputErrorTrace("ファイルが存在しません。");
            }

            return imageData;
        }

        /// <summary>
        /// エラーIDを表示文字列に変換
        /// </summary>
        /// <param name="errorId"></param>
        /// <param name="accountId"></param>
        /// <returns></returns>
        private string ErrorIdConvert(int errorId, string accountId)
        {
            string retStr = string.Empty;

            switch (errorId)
            {
                case -1:
                    // 接続タイムアウト
                    retStr = Messages.Message(MessageId.Message004);
                    break;
                case (int)EnumErrorId.NoErr:
                    // エラーなし
                    retStr = "エラーなし";
                    break;
                default:
                    // 処理結果が異常終了
                    retStr = ClientCommonProc.ErrorIdToErrorMessage((EnumErrorId)errorId, accountId);
                    break;
            }

            return retStr;
        }

        #endregion
    }
}
