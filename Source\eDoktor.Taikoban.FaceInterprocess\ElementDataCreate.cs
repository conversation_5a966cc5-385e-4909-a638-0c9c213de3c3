﻿using eDoktor.Common;
using eDoktor.Taikoban.FaceInterprocess;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace eDoktor.Taikoban.FaceInterprocess
{
    public class ElementDataCreate
    {
        private XElement elements;

        public ElementDataCreate()
        {
            elements = new XElement(ReqResDataBass.CONTENTS);
        }

        public XElement Elements { get { return elements; } }

        public string StrElements { get { return elements.ToString(); } }


        public void CreateElem(string elemName, object item)
        {
            this.elements.Add(new XElement(elemName, new XAttribute(ReqResDataBass.VALUE, item)));
        }
    }
}
