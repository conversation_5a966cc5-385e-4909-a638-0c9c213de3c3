﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace eDoktor.Taikoban.FaceAuthSDK
{
    /// <summary>
    /// 顔認証SDKの初期化処理
    /// </summary>
    public class SDKInitialize
    {
        /// <summary>
        /// SDKの初期化処理
        /// </summary>
        /// <param name="client">コマンド送信もとのクライアント情報</param>
        /// <param name="packet">受信コマンドの内容</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        public void Initialize(FaceAuthSettingsInfo.SettingsInfo settingInfo)
        {
            int parallelNum = eDoktor.Common.Configuration.AppSetting("ParallelNum", 1);
            int temporaryNum = eDoktor.Common.Configuration.AppSetting("TemporaryNum", 1);
            int lifespan = eDoktor.Common.Configuration.AppSetting("Lifespan", 10);

            //
            // インスタンス管理：それぞれ並列処理の必要分を作成（ 並列処理必要分, インスタンスを使い切ったときのテンポラリ上限数, インスタンス使用の寿命[秒] )
            //
            RetrieverEngineObjectManager.Instance.Initialize(parallelNum, temporaryNum, lifespan, settingInfo); // 顔照合用インスタンス
            SearcherEngineObjectManager.Instance.Initialize(parallelNum, temporaryNum, lifespan, settingInfo); // 顔サーチ用インスタンス
            CreatorEngineObjectManager.Instance.Initialize(parallelNum, temporaryNum, lifespan, settingInfo); // 特徴抽出用インスタンス
            // EstimatorEngineObjectManager.Instance.Initialize(parallelNum, temporaryNum, lifespan); // 性別年齢推定インスタンス
        }
    }
}
