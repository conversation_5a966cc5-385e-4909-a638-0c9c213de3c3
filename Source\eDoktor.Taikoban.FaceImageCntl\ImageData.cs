﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Resources;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace eDoktor.Taikoban.FaceImageCntl
{
    /// <summary>
    /// PictureBoxを使用したボタン用のイメージデータの管理用クラス
    /// (イメージデータはリソース上に存在)
    /// </summary>
    /// <remarks>スレッドセーフではありません</remarks>
    public class ImageData
    {
        #region Enum
        /// <summary>
        /// イメージデータの種類
        /// </summary>
        public enum ImageKind
        {
            /// <summary>通常</summary>
            Normal = 0,
            /// <summary>マウスEnter/summary>
            MouseEnter = 1,
            /// <summary>マウスLeave</summary>
            MouseLeave = 2,
            /// <summary>マウスUp</summary>
            MouseUp = 3,
            /// <summary>マウスDown</summary>
            MouseDown = 4,
            /// <summary>選択中</summary>
            Choosing = 5,
        }
        #endregion

        #region Private Fields
        private Assembly _assembly;
        private string _resourceName;
        private Dictionary<ImageKind, string> _dicImage;
        #endregion

        #region Constructors
        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="assembly">実行アセンブリー</param>
        /// <param name="resource">リソース名</param>
        public ImageData(Assembly assembly, string resourceName)
        {
            _assembly = assembly;
            _resourceName = resourceName;
            _dicImage = new Dictionary<ImageKind, string>();
        }
        #endregion

        #region Public Method
        /// <summary>
        /// リソース内のイメージの名称のデータを指定した種類で登録する。
        /// </summary>
        /// <param name="kind">イメージデータの種類</param>
        /// <param name="imageName">リソース内でのイメージの名前</param>
        public void AddImageData(ImageKind kind, string imageName)
        {
            _dicImage.Add(kind, imageName);
        }

        /// <summary>
        /// 登録内容を全てクリアします。
        /// </summary>
        public void Clear()
        {
            _dicImage.Clear();
        }

        /// <summary>
        /// 指定種類のイメージデータが登録済みか調べる。
        /// </summary>
        /// <param name="kind">イメージデータの種類</param>
        /// <returns>true:登録済み false:未登録</returns>
        public bool IsRegistered(ImageKind kind)
        {
            return _dicImage.ContainsKey(kind);
        }

        /// <summary>
        /// 指定イメージデータを取得する。
        /// </summary>
        /// <param name="kind"></param>
        /// <returns>取得したイメージ(未登録時はnull)</returns>
        public Image GetImage(ImageKind kind)
        {
            if (IsRegistered(kind))
            {
                ResourceManager resources = new ResourceManager(_resourceName, this._assembly);
                return (Image)resources.GetObject(_dicImage[kind]);
            }
            else
            {
                return null;
            }
        }
        #endregion

    }
}
