﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.3.32922.545
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.Taikoban.FaceInsert", "eDoktor.Taikoban.FaceInsert\eDoktor.Taikoban.FaceInsert.csproj", "{B152491C-8D0F-4188-9996-0B8327BBF9E1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.Taikoban.FaceAuthServer", "eDoktor.Taikoban.FaceAuthServer\eDoktor.Taikoban.FaceAuthServer.csproj", "{267CB3E5-5208-4351-B666-ADD746542ACE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.Taikoban.FaceAuthServerConsole", "eDoktor.Taikoban.FaceAuthServerConsole\eDoktor.Taikoban.FaceAuthServerConsole.csproj", "{82A8ADE6-54D3-4250-98C5-8A7D58EE93D0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.Taikoban.FaceInterprocess", "eDoktor.Taikoban.FaceInterprocess\eDoktor.Taikoban.FaceInterprocess.csproj", "{6EB7D3C9-23E9-4FAB-83DB-67272CAA8A0E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.Taikoban.FaceCrypt", "eDoktor.Taikoban.FaceCrypt\eDoktor.Taikoban.FaceCrypt.csproj", "{C540424B-0963-465A-A8BB-568F4309E0FA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.Taikoban.FaceClient", "eDoktor.Taikoban.FaceClient\eDoktor.Taikoban.FaceClient.csproj", "{E521FEDE-A3DD-4AF7-970B-04B4372B5293}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.Taikoban.FaceAuthServerService", "eDoktor.Taikoban.FaceAuthServerService\eDoktor.Taikoban.FaceAuthServerService.csproj", "{4DA02AED-7290-4DBF-B170-5F3CA05A9845}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.Taikoban.FaceAuthSDK", "eDoktor.Taikoban.FaceAuthSDK\eDoktor.Taikoban.FaceAuthSDK.csproj", "{92D59348-6C32-4C23-B019-1B3DB2715D00}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.Taikoban.FaceAuthTemplateUpdateJudge", "eDoktor.Taikoban.FaceAuthTemplateUpdateJudge\eDoktor.Taikoban.FaceAuthTemplateUpdateJudge.csproj", "{9A08B0FA-5509-4B51-804E-56F7355689A4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.Taikoban.FaceAuthCommon", "eDoktor.Taikoban.FaceAuthCommon\eDoktor.Taikoban.FaceAuthCommon.csproj", "{7A2D3A65-364E-43AD-93D9-9C1057E9C4B9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktorTaikoban.FaceClientCommon", "eDoktorTaikoban.FaceClientCommon\eDoktorTaikoban.FaceClientCommon.csproj", "{69CAF41B-94A8-4D86-9FA0-9E01FF4BFC6A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.Taikoban.FaceImageCntl", "eDoktor.Taikoban.FaceImageCntl\eDoktor.Taikoban.FaceImageCntl.csproj", "{BD2B8ACE-0230-46B3-84DA-45D919C55FC9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TestImageData", "TestImageData\TestImageData.csproj", "{82A84A2E-2187-4289-82F3-F66C6BBD6B26}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.Taikoban.FaceDetection", "eDoktor.Taikoban.FaceDetection\eDoktor.Taikoban.FaceDetection.csproj", "{C772955C-661C-45FB-9A10-47A91CBE9209}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "LivenessCheckSharp", "LivenessCheckSharp\LivenessCheckSharp.vcxproj", "{7EB371FC-25E4-4D56-908A-B9A4418D80D2}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{B152491C-8D0F-4188-9996-0B8327BBF9E1}.Debug|Any CPU.ActiveCfg = Debug|x86
		{B152491C-8D0F-4188-9996-0B8327BBF9E1}.Debug|Any CPU.Build.0 = Debug|x86
		{B152491C-8D0F-4188-9996-0B8327BBF9E1}.Debug|x64.ActiveCfg = Debug|x64
		{B152491C-8D0F-4188-9996-0B8327BBF9E1}.Debug|x64.Build.0 = Debug|x64
		{B152491C-8D0F-4188-9996-0B8327BBF9E1}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B152491C-8D0F-4188-9996-0B8327BBF9E1}.Debug|x86.Build.0 = Debug|Any CPU
		{B152491C-8D0F-4188-9996-0B8327BBF9E1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B152491C-8D0F-4188-9996-0B8327BBF9E1}.Release|Any CPU.Build.0 = Release|Any CPU
		{B152491C-8D0F-4188-9996-0B8327BBF9E1}.Release|x64.ActiveCfg = Release|x64
		{B152491C-8D0F-4188-9996-0B8327BBF9E1}.Release|x64.Build.0 = Release|x64
		{B152491C-8D0F-4188-9996-0B8327BBF9E1}.Release|x86.ActiveCfg = Release|Any CPU
		{B152491C-8D0F-4188-9996-0B8327BBF9E1}.Release|x86.Build.0 = Release|Any CPU
		{267CB3E5-5208-4351-B666-ADD746542ACE}.Debug|Any CPU.ActiveCfg = Debug|x86
		{267CB3E5-5208-4351-B666-ADD746542ACE}.Debug|Any CPU.Build.0 = Debug|x86
		{267CB3E5-5208-4351-B666-ADD746542ACE}.Debug|x64.ActiveCfg = Debug|x64
		{267CB3E5-5208-4351-B666-ADD746542ACE}.Debug|x64.Build.0 = Debug|x64
		{267CB3E5-5208-4351-B666-ADD746542ACE}.Debug|x86.ActiveCfg = Debug|x86
		{267CB3E5-5208-4351-B666-ADD746542ACE}.Debug|x86.Build.0 = Debug|x86
		{267CB3E5-5208-4351-B666-ADD746542ACE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{267CB3E5-5208-4351-B666-ADD746542ACE}.Release|Any CPU.Build.0 = Release|Any CPU
		{267CB3E5-5208-4351-B666-ADD746542ACE}.Release|x64.ActiveCfg = Release|x64
		{267CB3E5-5208-4351-B666-ADD746542ACE}.Release|x64.Build.0 = Release|x64
		{267CB3E5-5208-4351-B666-ADD746542ACE}.Release|x86.ActiveCfg = Release|x86
		{267CB3E5-5208-4351-B666-ADD746542ACE}.Release|x86.Build.0 = Release|x86
		{82A8ADE6-54D3-4250-98C5-8A7D58EE93D0}.Debug|Any CPU.ActiveCfg = Debug|x86
		{82A8ADE6-54D3-4250-98C5-8A7D58EE93D0}.Debug|Any CPU.Build.0 = Debug|x86
		{82A8ADE6-54D3-4250-98C5-8A7D58EE93D0}.Debug|x64.ActiveCfg = Debug|x64
		{82A8ADE6-54D3-4250-98C5-8A7D58EE93D0}.Debug|x64.Build.0 = Debug|x64
		{82A8ADE6-54D3-4250-98C5-8A7D58EE93D0}.Debug|x86.ActiveCfg = Debug|x86
		{82A8ADE6-54D3-4250-98C5-8A7D58EE93D0}.Debug|x86.Build.0 = Debug|x86
		{82A8ADE6-54D3-4250-98C5-8A7D58EE93D0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{82A8ADE6-54D3-4250-98C5-8A7D58EE93D0}.Release|Any CPU.Build.0 = Release|Any CPU
		{82A8ADE6-54D3-4250-98C5-8A7D58EE93D0}.Release|x64.ActiveCfg = Release|x64
		{82A8ADE6-54D3-4250-98C5-8A7D58EE93D0}.Release|x64.Build.0 = Release|x64
		{82A8ADE6-54D3-4250-98C5-8A7D58EE93D0}.Release|x86.ActiveCfg = Release|x86
		{82A8ADE6-54D3-4250-98C5-8A7D58EE93D0}.Release|x86.Build.0 = Release|x86
		{6EB7D3C9-23E9-4FAB-83DB-67272CAA8A0E}.Debug|Any CPU.ActiveCfg = Debug|x86
		{6EB7D3C9-23E9-4FAB-83DB-67272CAA8A0E}.Debug|Any CPU.Build.0 = Debug|x86
		{6EB7D3C9-23E9-4FAB-83DB-67272CAA8A0E}.Debug|x64.ActiveCfg = Debug|x64
		{6EB7D3C9-23E9-4FAB-83DB-67272CAA8A0E}.Debug|x64.Build.0 = Debug|x64
		{6EB7D3C9-23E9-4FAB-83DB-67272CAA8A0E}.Debug|x86.ActiveCfg = Debug|x86
		{6EB7D3C9-23E9-4FAB-83DB-67272CAA8A0E}.Debug|x86.Build.0 = Debug|x86
		{6EB7D3C9-23E9-4FAB-83DB-67272CAA8A0E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6EB7D3C9-23E9-4FAB-83DB-67272CAA8A0E}.Release|Any CPU.Build.0 = Release|Any CPU
		{6EB7D3C9-23E9-4FAB-83DB-67272CAA8A0E}.Release|x64.ActiveCfg = Release|x64
		{6EB7D3C9-23E9-4FAB-83DB-67272CAA8A0E}.Release|x64.Build.0 = Release|x64
		{6EB7D3C9-23E9-4FAB-83DB-67272CAA8A0E}.Release|x86.ActiveCfg = Release|x86
		{6EB7D3C9-23E9-4FAB-83DB-67272CAA8A0E}.Release|x86.Build.0 = Release|x86
		{C540424B-0963-465A-A8BB-568F4309E0FA}.Debug|Any CPU.ActiveCfg = Debug|x86
		{C540424B-0963-465A-A8BB-568F4309E0FA}.Debug|Any CPU.Build.0 = Debug|x86
		{C540424B-0963-465A-A8BB-568F4309E0FA}.Debug|x64.ActiveCfg = Debug|x64
		{C540424B-0963-465A-A8BB-568F4309E0FA}.Debug|x64.Build.0 = Debug|x64
		{C540424B-0963-465A-A8BB-568F4309E0FA}.Debug|x86.ActiveCfg = Debug|x86
		{C540424B-0963-465A-A8BB-568F4309E0FA}.Debug|x86.Build.0 = Debug|x86
		{C540424B-0963-465A-A8BB-568F4309E0FA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C540424B-0963-465A-A8BB-568F4309E0FA}.Release|Any CPU.Build.0 = Release|Any CPU
		{C540424B-0963-465A-A8BB-568F4309E0FA}.Release|x64.ActiveCfg = Release|x64
		{C540424B-0963-465A-A8BB-568F4309E0FA}.Release|x64.Build.0 = Release|x64
		{C540424B-0963-465A-A8BB-568F4309E0FA}.Release|x86.ActiveCfg = Release|x86
		{C540424B-0963-465A-A8BB-568F4309E0FA}.Release|x86.Build.0 = Release|x86
		{E521FEDE-A3DD-4AF7-970B-04B4372B5293}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E521FEDE-A3DD-4AF7-970B-04B4372B5293}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E521FEDE-A3DD-4AF7-970B-04B4372B5293}.Debug|x64.ActiveCfg = Debug|x64
		{E521FEDE-A3DD-4AF7-970B-04B4372B5293}.Debug|x64.Build.0 = Debug|x64
		{E521FEDE-A3DD-4AF7-970B-04B4372B5293}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E521FEDE-A3DD-4AF7-970B-04B4372B5293}.Debug|x86.Build.0 = Debug|Any CPU
		{E521FEDE-A3DD-4AF7-970B-04B4372B5293}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E521FEDE-A3DD-4AF7-970B-04B4372B5293}.Release|Any CPU.Build.0 = Release|Any CPU
		{E521FEDE-A3DD-4AF7-970B-04B4372B5293}.Release|x64.ActiveCfg = Release|x64
		{E521FEDE-A3DD-4AF7-970B-04B4372B5293}.Release|x64.Build.0 = Release|x64
		{E521FEDE-A3DD-4AF7-970B-04B4372B5293}.Release|x86.ActiveCfg = Release|x86
		{E521FEDE-A3DD-4AF7-970B-04B4372B5293}.Release|x86.Build.0 = Release|x86
		{4DA02AED-7290-4DBF-B170-5F3CA05A9845}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4DA02AED-7290-4DBF-B170-5F3CA05A9845}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4DA02AED-7290-4DBF-B170-5F3CA05A9845}.Debug|x64.ActiveCfg = Debug|x64
		{4DA02AED-7290-4DBF-B170-5F3CA05A9845}.Debug|x64.Build.0 = Debug|x64
		{4DA02AED-7290-4DBF-B170-5F3CA05A9845}.Debug|x86.ActiveCfg = Debug|x86
		{4DA02AED-7290-4DBF-B170-5F3CA05A9845}.Debug|x86.Build.0 = Debug|x86
		{4DA02AED-7290-4DBF-B170-5F3CA05A9845}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4DA02AED-7290-4DBF-B170-5F3CA05A9845}.Release|Any CPU.Build.0 = Release|Any CPU
		{4DA02AED-7290-4DBF-B170-5F3CA05A9845}.Release|x64.ActiveCfg = Release|x64
		{4DA02AED-7290-4DBF-B170-5F3CA05A9845}.Release|x64.Build.0 = Release|x64
		{4DA02AED-7290-4DBF-B170-5F3CA05A9845}.Release|x86.ActiveCfg = Release|x86
		{4DA02AED-7290-4DBF-B170-5F3CA05A9845}.Release|x86.Build.0 = Release|x86
		{92D59348-6C32-4C23-B019-1B3DB2715D00}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{92D59348-6C32-4C23-B019-1B3DB2715D00}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{92D59348-6C32-4C23-B019-1B3DB2715D00}.Debug|x64.ActiveCfg = Debug|x64
		{92D59348-6C32-4C23-B019-1B3DB2715D00}.Debug|x64.Build.0 = Debug|x64
		{92D59348-6C32-4C23-B019-1B3DB2715D00}.Debug|x86.ActiveCfg = Debug|Any CPU
		{92D59348-6C32-4C23-B019-1B3DB2715D00}.Debug|x86.Build.0 = Debug|Any CPU
		{92D59348-6C32-4C23-B019-1B3DB2715D00}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{92D59348-6C32-4C23-B019-1B3DB2715D00}.Release|Any CPU.Build.0 = Release|Any CPU
		{92D59348-6C32-4C23-B019-1B3DB2715D00}.Release|x64.ActiveCfg = Release|x64
		{92D59348-6C32-4C23-B019-1B3DB2715D00}.Release|x64.Build.0 = Release|x64
		{92D59348-6C32-4C23-B019-1B3DB2715D00}.Release|x86.ActiveCfg = Release|x86
		{92D59348-6C32-4C23-B019-1B3DB2715D00}.Release|x86.Build.0 = Release|x86
		{9A08B0FA-5509-4B51-804E-56F7355689A4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9A08B0FA-5509-4B51-804E-56F7355689A4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9A08B0FA-5509-4B51-804E-56F7355689A4}.Debug|x64.ActiveCfg = Debug|x64
		{9A08B0FA-5509-4B51-804E-56F7355689A4}.Debug|x64.Build.0 = Debug|x64
		{9A08B0FA-5509-4B51-804E-56F7355689A4}.Debug|x86.ActiveCfg = Debug|Any CPU
		{9A08B0FA-5509-4B51-804E-56F7355689A4}.Debug|x86.Build.0 = Debug|Any CPU
		{9A08B0FA-5509-4B51-804E-56F7355689A4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9A08B0FA-5509-4B51-804E-56F7355689A4}.Release|Any CPU.Build.0 = Release|Any CPU
		{9A08B0FA-5509-4B51-804E-56F7355689A4}.Release|x64.ActiveCfg = Release|x64
		{9A08B0FA-5509-4B51-804E-56F7355689A4}.Release|x64.Build.0 = Release|x64
		{9A08B0FA-5509-4B51-804E-56F7355689A4}.Release|x86.ActiveCfg = Release|x86
		{9A08B0FA-5509-4B51-804E-56F7355689A4}.Release|x86.Build.0 = Release|x86
		{7A2D3A65-364E-43AD-93D9-9C1057E9C4B9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7A2D3A65-364E-43AD-93D9-9C1057E9C4B9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7A2D3A65-364E-43AD-93D9-9C1057E9C4B9}.Debug|x64.ActiveCfg = Debug|x64
		{7A2D3A65-364E-43AD-93D9-9C1057E9C4B9}.Debug|x64.Build.0 = Debug|x64
		{7A2D3A65-364E-43AD-93D9-9C1057E9C4B9}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7A2D3A65-364E-43AD-93D9-9C1057E9C4B9}.Debug|x86.Build.0 = Debug|Any CPU
		{7A2D3A65-364E-43AD-93D9-9C1057E9C4B9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7A2D3A65-364E-43AD-93D9-9C1057E9C4B9}.Release|Any CPU.Build.0 = Release|Any CPU
		{7A2D3A65-364E-43AD-93D9-9C1057E9C4B9}.Release|x64.ActiveCfg = Release|x64
		{7A2D3A65-364E-43AD-93D9-9C1057E9C4B9}.Release|x64.Build.0 = Release|x64
		{7A2D3A65-364E-43AD-93D9-9C1057E9C4B9}.Release|x86.ActiveCfg = Release|x86
		{7A2D3A65-364E-43AD-93D9-9C1057E9C4B9}.Release|x86.Build.0 = Release|x86
		{69CAF41B-94A8-4D86-9FA0-9E01FF4BFC6A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{69CAF41B-94A8-4D86-9FA0-9E01FF4BFC6A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{69CAF41B-94A8-4D86-9FA0-9E01FF4BFC6A}.Debug|x64.ActiveCfg = Debug|x64
		{69CAF41B-94A8-4D86-9FA0-9E01FF4BFC6A}.Debug|x64.Build.0 = Debug|x64
		{69CAF41B-94A8-4D86-9FA0-9E01FF4BFC6A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{69CAF41B-94A8-4D86-9FA0-9E01FF4BFC6A}.Debug|x86.Build.0 = Debug|Any CPU
		{69CAF41B-94A8-4D86-9FA0-9E01FF4BFC6A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{69CAF41B-94A8-4D86-9FA0-9E01FF4BFC6A}.Release|Any CPU.Build.0 = Release|Any CPU
		{69CAF41B-94A8-4D86-9FA0-9E01FF4BFC6A}.Release|x64.ActiveCfg = Release|x64
		{69CAF41B-94A8-4D86-9FA0-9E01FF4BFC6A}.Release|x64.Build.0 = Release|x64
		{69CAF41B-94A8-4D86-9FA0-9E01FF4BFC6A}.Release|x86.ActiveCfg = Release|Any CPU
		{69CAF41B-94A8-4D86-9FA0-9E01FF4BFC6A}.Release|x86.Build.0 = Release|Any CPU
		{BD2B8ACE-0230-46B3-84DA-45D919C55FC9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BD2B8ACE-0230-46B3-84DA-45D919C55FC9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BD2B8ACE-0230-46B3-84DA-45D919C55FC9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{BD2B8ACE-0230-46B3-84DA-45D919C55FC9}.Debug|x64.Build.0 = Debug|Any CPU
		{BD2B8ACE-0230-46B3-84DA-45D919C55FC9}.Debug|x86.ActiveCfg = Debug|Any CPU
		{BD2B8ACE-0230-46B3-84DA-45D919C55FC9}.Debug|x86.Build.0 = Debug|Any CPU
		{BD2B8ACE-0230-46B3-84DA-45D919C55FC9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BD2B8ACE-0230-46B3-84DA-45D919C55FC9}.Release|Any CPU.Build.0 = Release|Any CPU
		{BD2B8ACE-0230-46B3-84DA-45D919C55FC9}.Release|x64.ActiveCfg = Release|x64
		{BD2B8ACE-0230-46B3-84DA-45D919C55FC9}.Release|x64.Build.0 = Release|x64
		{BD2B8ACE-0230-46B3-84DA-45D919C55FC9}.Release|x86.ActiveCfg = Release|Any CPU
		{BD2B8ACE-0230-46B3-84DA-45D919C55FC9}.Release|x86.Build.0 = Release|Any CPU
		{82A84A2E-2187-4289-82F3-F66C6BBD6B26}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{82A84A2E-2187-4289-82F3-F66C6BBD6B26}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{82A84A2E-2187-4289-82F3-F66C6BBD6B26}.Debug|x64.ActiveCfg = Debug|x64
		{82A84A2E-2187-4289-82F3-F66C6BBD6B26}.Debug|x86.ActiveCfg = Debug|Any CPU
		{82A84A2E-2187-4289-82F3-F66C6BBD6B26}.Debug|x86.Build.0 = Debug|Any CPU
		{82A84A2E-2187-4289-82F3-F66C6BBD6B26}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{82A84A2E-2187-4289-82F3-F66C6BBD6B26}.Release|Any CPU.Build.0 = Release|Any CPU
		{82A84A2E-2187-4289-82F3-F66C6BBD6B26}.Release|x64.ActiveCfg = Release|x64
		{82A84A2E-2187-4289-82F3-F66C6BBD6B26}.Release|x86.ActiveCfg = Release|Any CPU
		{82A84A2E-2187-4289-82F3-F66C6BBD6B26}.Release|x86.Build.0 = Release|Any CPU
		{C772955C-661C-45FB-9A10-47A91CBE9209}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C772955C-661C-45FB-9A10-47A91CBE9209}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C772955C-661C-45FB-9A10-47A91CBE9209}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C772955C-661C-45FB-9A10-47A91CBE9209}.Debug|x64.Build.0 = Debug|Any CPU
		{C772955C-661C-45FB-9A10-47A91CBE9209}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C772955C-661C-45FB-9A10-47A91CBE9209}.Debug|x86.Build.0 = Debug|Any CPU
		{C772955C-661C-45FB-9A10-47A91CBE9209}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C772955C-661C-45FB-9A10-47A91CBE9209}.Release|Any CPU.Build.0 = Release|Any CPU
		{C772955C-661C-45FB-9A10-47A91CBE9209}.Release|x64.ActiveCfg = Release|x64
		{C772955C-661C-45FB-9A10-47A91CBE9209}.Release|x64.Build.0 = Release|x64
		{C772955C-661C-45FB-9A10-47A91CBE9209}.Release|x86.ActiveCfg = Release|Any CPU
		{C772955C-661C-45FB-9A10-47A91CBE9209}.Release|x86.Build.0 = Release|Any CPU
		{7EB371FC-25E4-4D56-908A-B9A4418D80D2}.Debug|Any CPU.ActiveCfg = Debug|x64
		{7EB371FC-25E4-4D56-908A-B9A4418D80D2}.Debug|Any CPU.Build.0 = Debug|x64
		{7EB371FC-25E4-4D56-908A-B9A4418D80D2}.Debug|x64.ActiveCfg = Debug|x64
		{7EB371FC-25E4-4D56-908A-B9A4418D80D2}.Debug|x64.Build.0 = Debug|x64
		{7EB371FC-25E4-4D56-908A-B9A4418D80D2}.Debug|x86.ActiveCfg = Debug|Win32
		{7EB371FC-25E4-4D56-908A-B9A4418D80D2}.Debug|x86.Build.0 = Debug|Win32
		{7EB371FC-25E4-4D56-908A-B9A4418D80D2}.Release|Any CPU.ActiveCfg = Release|x64
		{7EB371FC-25E4-4D56-908A-B9A4418D80D2}.Release|Any CPU.Build.0 = Release|x64
		{7EB371FC-25E4-4D56-908A-B9A4418D80D2}.Release|x64.ActiveCfg = Release|x64
		{7EB371FC-25E4-4D56-908A-B9A4418D80D2}.Release|x64.Build.0 = Release|x64
		{7EB371FC-25E4-4D56-908A-B9A4418D80D2}.Release|x86.ActiveCfg = Release|Win32
		{7EB371FC-25E4-4D56-908A-B9A4418D80D2}.Release|x86.Build.0 = Release|Win32
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {B2FE7788-763D-4E34-9968-B15C0064E4A9}
	EndGlobalSection
EndGlobal
