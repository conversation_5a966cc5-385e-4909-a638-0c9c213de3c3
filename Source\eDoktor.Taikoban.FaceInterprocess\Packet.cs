﻿// Packet.cs

namespace eDoktor.Taikoban.FaceInterprocess
{
	public class Packet : eDoktor.Common.Packet
	{
		#region Enumerations
		public enum CommandType
		{
			// Client⇒Server
			/// <summary>設定取得要求</summary>
			FaceSettingGetReq = 101,
			/// <summary>顔テンプレート登録確認要求</summary>
			FaceTemplateRegConfirmReq = 103,
			/// <summary>顔テンプレート取得要求</summary>
			FaceTemplateGetReq = 105,
			/// <summary>顔認証要求</summary>
			FaceAuthtReq = 107,
			/// <summary>顔テンプレート登録要求(サーバ認証モード)</summary>
			FaceTemplateRegServerReq = 109,
			/// <summary>顔テンプレート登録要求(クライアント認証モード)</summary>
			FaceTemplateRegClientReq = 111,
			/// <summary>顔テンプレート削除要求</summary>
			FaceTemplateDelReq = 113,
			/// <summary>顔テンプレート追加更新要求</summary>
			FaceTemplateAddUpdateReq = 115,
			/// <summary>本人確認・顔テンプレート登録確認要求</summary>
			IdentificationFaceTemplateRegConfirmReq = 117,
			/// <summary>ユーザ検索顔認証要求</summary>
			UserSearchFaceAuthReq = 119,
			/// <summary>認証ログ登録(クライアント認証モード)要求</summary>
			FaceAuthLogRegistReq = 121,
            /// <summary>なりすまし画像ログ登録要求</summary>
            LivenessLogRegistReq = 123,

            // Server⇒Client 
            /// <summary>設定取得応答</summary>
            FaceSettingGetRes = 102,
			/// <summary>顔テンプレート登録確認応答</summary>
			FaceTemplateRegConfirmRes = 104,
			/// <summary>顔テンプレート取得応答</summary>
			FaceTemplateGetRes = 106,
			/// <summary>顔認証応答</summary>
			FaceAuthtRes = 108,
			/// <summary>顔テンプレート登録応答(サーバ認証モード)</summary>
			FaceTemplateRegServerRes = 110,
			/// <summary>顔テンプレート登録応答(クライアント認証モード)</summary>
			FaceTemplateRegClientRes = 112,
			/// <summary>顔テンプレート削除応答</summary>
			FaceTemplateDelRes = 114,
			/// <summary>顔テンプレート追加更新応答</summary>
			FaceTemplateAddUpdateRes = 116,
			/// <summary>本人確認・顔テンプレート登録確認応答</summary>
			IdentificationFaceTemplateRegConfirmRes = 118,
			/// <summary>ユーザ検索顔認証応答</summary>
			UserSearchFaceAuthRes = 120,
			/// <summary>認証ログ登録(クライアント認証モード)応答</summary>
			FaceAuthLogRegistRes = 122,
            /// <summary>なりすまし画像ログ登録応答</summary>
            LivenessLogRegistRes = 124,
        }

		private enum HeaderSectionType
		{
			Command,		// コマンド
			Sequence,		// シーケンス番号
            ParameterType,  // パラメータタイプ
		}

        public enum ParameterKind
        {
            CSV = 0,        // CSV
        }
		#endregion

		#region Fields
        public static readonly string Terminator = System.Convert.ToChar(0x0D).ToString() + System.Convert.ToChar(0x0A).ToString(); 				// base64で使用される文字(A–Z a–z 0–9 + / = )以外を指定する事
		public static readonly byte[] TerminatorBytes;
        public static readonly string Delimitor = "|";
		public static readonly int MaxPacketLength;
		public static readonly int MaxSequence = 99;				// 2桁の整数で表現できる最大値
		public static readonly int MinSequence = 0;
		public static readonly int BroadcastSequence = 0;			// ブロードキャスト用シーケンス番号
        private static readonly string HeaderString = "eDoktor";
		private static readonly int HeaderSectionLength = 12; // ここもヘッダーの長さに合わせて調整すること
		private static readonly int DataSectionOffset = HeaderSectionLength;
		private static readonly int MaxDataSectionLength = 16777215;	// 単なる安全対策 必要であれば値を大きくする事
		//private static readonly int DataLengthWidth = 7;			// 7桁
		private static readonly int MaxDataLength = 9999999;		// 7桁の整数で表現できる最大値
		private static System.Text.Encoding Encoding;				// スレッドセーフ？
		private static readonly eDoktor.Common.SectionCollection<HeaderSectionType> HeaderSections;
		private static readonly eDoktor.Common.Section<HeaderSectionType> CommandSection;
		private static readonly eDoktor.Common.Section<HeaderSectionType> SequenceSection;
        private static readonly eDoktor.Common.Section<HeaderSectionType> ParameterTypeSection;
        private CommandType _command;
        private int _sequence = 0;

        private static int _sendSequence = MaxSequence;             // 送信用シーケンス
        private static readonly object SyncRoot = new object();

//        private ParameterKind _parameterType;
		private string[] _data;
		private string _packetString;
		#endregion

		#region Indexers
		private string this[int i]
		{
			get { return ((i < 0) || (_data == null) || (_data.Length <= i)) ? string.Empty : _data[i]; }
		}
		#endregion

		#region Properties
		public CommandType Command
		{
			get { return _command; }
		}

		public int Sequence
		{
			get { return _sequence; }
			set { UpdateSequence(value); }
		}

        //public ParameterKind ParameterType
        //{
        //    get { return _parameterType; }
        //}

        public string Data
        {
            get
            {
                string temp = ToString();
                return (temp.Length < HeaderSectionLength) ? string.Empty : temp.Substring(HeaderSectionLength, temp.Length - HeaderSectionLength);
            }
        }
		#endregion

		#region Constructors
		static Packet()
		{
			try
			{
				MaxPacketLength = HeaderSectionLength + MaxDataSectionLength;

				Encoding = System.Text.Encoding.UTF8;
				TerminatorBytes = Encoding.GetBytes(Terminator);

				HeaderSections = new eDoktor.Common.SectionCollection<HeaderSectionType>(new eDoktor.Common.Section<HeaderSectionType>[]
				{
					CommandSection = new eDoktor.Common.Section<HeaderSectionType>(HeaderSectionType.Command, 7, 3), // eDoktor の長さ分だけでよい
					SequenceSection = new eDoktor.Common.Section<HeaderSectionType>(HeaderSectionType.Sequence, 10, 2), // シーケンス番号は2桁で、0詰めだから、コマンドの後の10桁目にに追加する
                    ParameterTypeSection = new eDoktor.Common.Section<HeaderSectionType>(HeaderSectionType.ParameterType, 12, 1),
				});

				CommandSection = HeaderSections[HeaderSectionType.Command];
				SequenceSection = HeaderSections[HeaderSectionType.Sequence];
                ParameterTypeSection = HeaderSections[HeaderSectionType.ParameterType];
			}
			catch (System.Exception ex)
			{
				eDoktor.Common.Trace.OutputExceptionTrace(ex);
			}
		}

		public Packet(byte[] rawData)
			: this(rawData, true)
		{

		}

		protected Packet(byte[] rawData, bool splitDataString)
			: base(rawData)
		{
			PopulateProperties();

			if (splitDataString)
			{
				SplitDataString();
			}
		}
		#endregion

		#region Public Methods
		public static Packet CreateRequestPacket(CommandType command, params object[] requestData)
		{
            int sequence = GetNextSequenceForTransmission();
            return CreatePacket(command, sequence, requestData);
		}

		public Packet CreateResponsePacket(params object[] responseData)
		{
            return CreatePacket(Command, Sequence, responseData);
		}

        public Packet CreateRequestOrResponsePacket(CommandType command, int sequence, params object[] responseData)
        {
            return CreatePacket(command, sequence, responseData);
        }
     
#if false
        public T GetData<T>()
			where T : class, new()
		{
			T responseData = null;
            //return responseData = Serialization.FromCompressedStringToObject(this[0]) as T ?? new T();
            return responseData;
		}
#endif
        public override string ToString()
		{
			if (string.IsNullOrEmpty(_packetString))
			{
				//_packetString = Crypto.DecryptString(Encoding.GetString(RawData, 0, RawData.Length - TerminatorBytes.Length));
                _packetString = Encoding.GetString(RawData, 0, RawData.Length - TerminatorBytes.Length);
			}

			return _packetString;
		}

        /// <summary>
        /// コマンドに対応するレスポンスタイプを取得する
        /// </summary>
        /// <param name="command">コマンド</param>
        /// <returns>レスポンスタイプ</returns>
        public static  CommandType GetResponseType(CommandType command)
        {
            CommandType response = CommandType.FaceSettingGetReq;

			// 設定取得要求
			if (command == CommandType.FaceSettingGetReq)
            {
                response = CommandType.FaceSettingGetRes;
            }
			// 顔テンプレート登録確認要求
			else if (command == CommandType.FaceTemplateRegConfirmReq)
			{
				response = CommandType.FaceTemplateRegConfirmRes;
			}
			// 顔テンプレート取得要求
			else if (command == CommandType.FaceTemplateGetReq)
			{
				response = CommandType.FaceTemplateGetRes;
			}
			// 顔認証要求
			else if (command == CommandType.FaceAuthtReq)
			{
				response = CommandType.FaceAuthtRes;
			}
			// 顔テンプレート登録要求(サーバ認証モード)
			else if (command == CommandType.FaceTemplateRegServerReq)
			{
				response = CommandType.FaceTemplateRegServerRes;
			}
			// 顔テンプレート登録要求(クライアント認証モード)
			else if (command == CommandType.FaceTemplateRegClientReq)
			{
				response = CommandType.FaceTemplateRegClientRes;
			}
			// 顔テンプレート削除要求
			else if (command == CommandType.FaceTemplateDelReq)
			{
				response = CommandType.FaceTemplateDelRes;
			}
			// 顔テンプレート追加更新要求
			else if (command == CommandType.FaceTemplateAddUpdateReq)
			{
				response = CommandType.FaceTemplateAddUpdateRes;
			}
			// 本人確認・顔テンプレート登録確認要求
			else if (command == CommandType.IdentificationFaceTemplateRegConfirmReq)
			{
				response = CommandType.IdentificationFaceTemplateRegConfirmRes;
			}
			// ユーザ検索顔認証要求
			else if (command == CommandType.UserSearchFaceAuthReq)
			{
				response = CommandType.UserSearchFaceAuthRes;
            }
            // なりすまし画像登録要求
            else if (command == CommandType.LivenessLogRegistReq)
            {
                response = CommandType.LivenessLogRegistRes;
            }
            return response;
        }

		#endregion

		#region Private Methods
		private void UpdateSequence(int sequence)
		{
            //string packetString = ToString();
            //_packetString = string.Format("{0}{1}{2}", HeaderString, packetString.Substring(CommandSection.Offset, CommandSection.Length), FromIntToString(sequence, SequenceSection.Length));
            //string encryptedString = Crypto.EncryptString(_packetString);
            //// ▼ MOD Common.dll修正対応 2015/09/25
            ////_rawData = Encoding.GetBytes(string.Format("{0}{1}", encryptedString, Terminator));
            //RawData = Encoding.GetBytes(string.Format("{0}{1}", encryptedString, Terminator));
            // ▲ MOD Common.dll修正対応 2015/09/25
			_sequence = sequence;
		}

		private void PopulateProperties()
		{
			string packetString = ToString();
			_command = (CommandType)FromStringToInt(packetString.Substring(CommandSection.Offset, CommandSection.Length));
			_sequence = FromStringToInt(packetString.Substring(SequenceSection.Offset, SequenceSection.Length));
//            _parameterType = (ParameterKind)FromStringToInt(packetString.Substring(ParameterTypeSection.Offset, ParameterTypeSection.Length)); // CSV形式ではないので、今回は不要
		}

		private void SplitDataString()
		{
			string packetString = ToString();

			if (packetString.Length > DataSectionOffset)
			{
				_data = SplitDataString(packetString.Substring(DataSectionOffset, packetString.Length - HeaderSectionLength));
			}
		}

		private static string[] SplitDataString(string dataString)
		{
			if (string.IsNullOrEmpty(dataString))
			{
				return null;
			}

			//int index = 0;
			int lengthLeft = dataString.Length;
			System.Collections.Generic.List<string> dataStringList = new System.Collections.Generic.List<string>();

            //while (lengthLeft > DataLengthWidth)
            //{
            //    int length = int.Parse(dataString.Substring(index, DataLengthWidth));
            //    index += DataLengthWidth;
            //    dataStringList.Add(dataString.Substring(index, length));
            //    index += length;
            //    lengthLeft -= length + DataLengthWidth;
            //}

			// return dataStringList.ToArray();
            return dataString.Split(new string[] { Delimitor }, System.StringSplitOptions.RemoveEmptyEntries);
		}

		private static string BuildDataString(params object[] args)
		{
			if ((args == null) || (args.Length == 0))
			{
				return string.Empty;
			}

			System.Text.StringBuilder stringBuilder = new System.Text.StringBuilder();

			for (int i = 0; i < args.Length; i++)
			{
				string arg = (args[i] != null) ? (args[i] is string) ? args[i] as string : args[i].ToString() : string.Empty;

                // 文字コードを設定されている文字コードに変換する
                string temp = Util.ConvertEncodeling(arg, Encoding);
                // 暗号化を行う
                arg = eDoktor.Taikoban.FaceCrypt.Crypto.Encrypt(temp);

                if (i != 0)
                {
                    arg = Delimitor + arg;
                }

				int argLength = arg.Length;

				if (argLength > MaxDataLength)
				{
					throw new System.ArgumentOutOfRangeException();
				}

				if (stringBuilder.Length + argLength > MaxDataSectionLength)
				{
					throw new System.ArgumentOutOfRangeException();
				}

				// stringBuilder.Append(FromIntToString(argLength, DataLengthWidth));
				stringBuilder.Append(arg);
			}

			return stringBuilder.ToString();
		}


        private static Packet CreatePacket(CommandType command, int sequence, params object[] args)
		{
            return CreatePacket(command, sequence, (int)ParameterKind.CSV, args);
		}

        private static Packet CreatePacket(CommandType command, int sequence, int parmType, params object[] args)
        {
            string packetString = string.Format("{0}{1}{2}{3}",
                                                    HeaderString,
                                                    FromIntToString((int)command, CommandSection.Length),
                                                    FromIntToString(sequence, SequenceSection.Length),
//                                                    FromIntToString(parmType, ParameterTypeSection.Length), 
                                                    BuildDataString(args));
            //string encryptedString = Crypto.EncryptString(packetString);
            byte[] rawData = Encoding.GetBytes(string.Format("{0}{1}", packetString, Terminator));
            return new Packet(rawData, false);
        }
        
        private static string FromIntToString(int value, int width)
		{
			if (value < 0)
			{
				width -= 1;	// -符号分1減らす
			}

			string formatString = string.Format("{{0:D{0}}}", width);
			string intString = string.Format(formatString, value);

			if (intString.Length != width)
			{
				throw new System.ArgumentOutOfRangeException("value");
			}

			return intString;
		}

		private static int FromStringToInt(string value)
		{
			return System.Convert.ToInt32(value);
		}

		private static int FromBoolToInt(bool value)
		{
			return (value) ? 1 : 0;
		}

		private static bool FromIntStringToBool(string value)
		{
			return (FromStringToInt(value) != 0);
		}

		private static System.DateTime FromStringToDateTime(string value)
		{
			return System.DateTime.Parse(value);
		}

		private static string FromDateTimeToString(System.DateTime value)
		{
			return value.ToString("yyyy-MM-dd HH:mm:ss.fffffff");
		}

        private static int GetNextSequenceForTransmission()
        {
            using (eDoktor.Common.TimedLock.Lock(SyncRoot))
            {
                if (++_sendSequence > Packet.MaxSequence)
                {
                    _sendSequence = Packet.MinSequence;
                }

                return _sendSequence;
            }
        }

		#endregion
	}
}
