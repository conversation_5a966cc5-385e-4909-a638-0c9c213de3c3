﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using eDoktorTaikoban.FaceClientCommon;

namespace eDoktor.Taikoban.FaceInsert
{
    /// <summary>
    /// 撮影登録画面の本人確認画面
    /// </summary>
    public partial class FaceInsertIdentification : Form
    {
        #region Private Const

        /// <summary>
        /// パスワード伏字
        /// </summary>
        private const char CHAR_HIDDEN_WORD = '*';

        #endregion

        #region Private Fields

        /// <summary>
        /// 要求送信クラス
        /// </summary>
        RequestSendClass _requestSend = new RequestSendClass();

        /// <summary>
        /// 設定情報
        /// </summary>
        private SettingsInfo _settingInfo;

        /// <summary>
        /// カメラデバイス ホワイトリスト
        /// </summary>
        private List<string> _whiteList;

        /// <summary>
        /// カメラデバイス ブラックリスト
        /// </summary>
        private List<string> _blackList;

        /// <summary>
        /// ボタンアクションクラス
        /// </summary>
        BtnImageCahnge _btnImageCahnge = new BtnImageCahnge();

        #endregion

        #region Constructors

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="whiteList">デバイスホワイトリスト</param>
        /// <param name="blackList">デバイスブラックリスト</param>
        /// <param name="settingInfo">設定情報</param>
        public FaceInsertIdentification(List<string> whiteList, List<string> blackList, SettingsInfo settingInfo)
        {
            InitializeComponent();

            this._settingInfo = settingInfo;
            this._whiteList = whiteList;
            this._blackList = blackList;
        }

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="requestSend">要求送信クラス</param>
        /// <param name="whiteList">デバイスホワイトリスト</param>
        /// <param name="blackList">デバイスブラックリスト</param>
        /// <param name="settingInfo">設定情報</param>
        public FaceInsertIdentification(RequestSendClass requestSend, List<string> whiteList, List<string> blackList, SettingsInfo settingInfo)
            : this(whiteList,  blackList, settingInfo)
        {
            this._requestSend = requestSend;
        }


        #endregion

        /// <summary>
        /// フォームロード
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FaceInsertIdentification_Load(object sender, EventArgs e)
        {
            // 起動中ラベルを非表示にする
            label3.Visible = false;

            // パスワードは見えないようにする
            textBoxPassword.PasswordChar = CHAR_HIDDEN_WORD;
        }

        /// <summary>
        /// 確認ボタン押下処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void button1_Click(object sender, EventArgs e)
        {
            // ログインIDの入力チェック
            if (string.IsNullOrEmpty(textBoxLogin.Text))
            {
                // ログインIDを入力してください
                MessageBox.Show(Messages.Message(MessageId.Message024));
                return;
            }

            // パスワードの入力チェック
            if (string.IsNullOrEmpty(textBoxPassword.Text))
            {
                // パスワードを入力してください
                MessageBox.Show(Messages.Message(MessageId.Message025));
                return;
            }

            // 本人確認・顔テンプレート登録確認要求を行う
            var res = this._requestSend.RequestIdentificationCheck(this.textBoxLogin.Text, this.textBoxPassword.Text, out FaceInterprocess.IdentificationFaceTemplateRegConfirmResData resData);

            if (!res)
            {
                // サーバーからの応答がありません。
                MessageBox.Show(Messages.Message(MessageId.Message004));
                return;
            }

            // 処理結果が正常終了
            if (resData.ProcResult == (int)EnumProcResult.Success)
            {
                Trace.OutputDebugTrace("処理結果が正常終了");

                // 認証OK
                if (resData.IdentificationResult == EnumIdentificationResult.AuthOK)
                {
                    Trace.OutputDebugTrace("認証OK");

                    // 登録状態が上限以外
                    if (resData.RegistState != EnumRegistState.RegistMax)
                    {
                        // 認証に成功した場合は、撮影登録画面に遷移する
                        using (FaceInsertPhotoGraphy faceInsertPhotoGraphy = 
                            new FaceInsertPhotoGraphy(_whiteList, _blackList, this.textBoxLogin.Text, resData.UserName, resData.AccountId, this._settingInfo))
                        {
                            // 起動中ラベルを表示にする
                            label3.Visible = true;
                            label3.Update();

                            this.Hide();
                            faceInsertPhotoGraphy.ShowDialog(this);
                            this.Close();
                        }
                    }
                    else
                    {
                        // 上限
                        Trace.OutputDebugTrace("上限");
                        MessageBox.Show(Messages.Message(MessageId.Message009));
                    }
                }
                else
                {
                    Trace.OutputErrorTrace("認証NG");

                    // IDもしくはパスワードが間違っています。
                    MessageBox.Show(Messages.Message(MessageId.Message016));
                }
            }
            else
            {
                Trace.OutputErrorTrace("処理結果が異常終了");
                MessageBox.Show(Messages.Message(MessageId.Message001));
            }
        }

        /// <summary>
        /// キャンセルボタン押下処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void button2_Click(object sender, EventArgs e)
        {
            this.Close();
        }


        /// <summary>
        /// マウスカーソルがボタン領域に入った時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseEnter(object sender, EventArgs e)
        {
            _btnImageCahnge.btn_MouseEnter(sender, e);
        }

        /// <summary>
        /// マウスカーソルがボタン領域から抜けた時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseLeave(object sender, EventArgs e)
        {
            _btnImageCahnge.btn_MouseLeave(sender, e);
        }

        /// <summary>
        /// ボタン押下時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            _btnImageCahnge.btn_MouseDown(sender, e);
        }

    }
}
