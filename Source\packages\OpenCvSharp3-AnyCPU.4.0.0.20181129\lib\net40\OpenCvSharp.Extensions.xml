<?xml version="1.0"?>
<doc>
    <assembly>
        <name>OpenCvSharp.Extensions</name>
    </assembly>
    <members>
        <member name="T:OpenCvSharp.Extensions.Binarizer">
            <summary>
            Various binarization methods (ATTENTION : The methods of this class is not implemented in OpenCV)
            </summary>
        </member>
        <member name="M:OpenCvSharp.Extensions.Binarizer.Niblack(OpenCvSharp.Mat,OpenCvSharp.Mat,System.Int32,System.Double)">
            <summary>
            Binarizes by <PERSON><PERSON><PERSON>'s method (This is faster but memory-hogging)
            </summary>
            <param name="src">Input image</param>
            <param name="dst">Output image</param>
            <param name="kernelSize">Window size</param>
            <param name="k">Adequate coefficient</param>
        </member>
        <member name="M:OpenCvSharp.Extensions.Binarizer.Sauvola(OpenCvSharp.Mat,OpenCvSharp.Mat,System.Int32,System.Double,System.Double)">
            <summary>
            Binarizes by Sau<PERSON><PERSON>'s method (This is faster but memory-hogging)
            </summary>
            <param name="src">Input image</param>
            <param name="dst">Output image</param>
            <param name="kernelSize">Window size</param>
            <param name="k">Adequate coefficient</param>
            <param name="r">Adequate coefficient</param>
        </member>
        <member name="M:OpenCvSharp.Extensions.Binarizer.Bernsen(OpenCvSharp.Mat,OpenCvSharp.Mat,System.Int32,System.Byte,System.Byte)">
            <summary>
            Binarizes by Bernsen's method
            </summary>
            <param name="src">Input image</param>
            <param name="dst">Output image</param>
            <param name="kernelSize">Window size</param>
            <param name="constrastMin">Adequate coefficient</param>
            <param name="bgThreshold">Adequate coefficient</param>
        </member>
        <member name="M:OpenCvSharp.Extensions.Binarizer.Nick(OpenCvSharp.Mat,OpenCvSharp.Mat,System.Int32,System.Double)">
            <summary>
            Binarizes by Nick's method
            </summary>
            <param name="src">Input image</param>
            <param name="dst">Output image</param>
            <param name="kernelSize">Window size</param>
            <param name="k">Adequate coefficient</param>
        </member>
        <member name="M:OpenCvSharp.Extensions.Binarizer.MinMax(OpenCvSharp.Mat,System.Int32,System.Int32,System.Int32,System.Byte@,System.Byte@)">
            <summary>
            注目画素の周辺画素の最大値と最小値を求める
            </summary>
            <param name="img">画像の画素データ</param>
            <param name="x">x座標</param>
            <param name="y">y座標</param>
            <param name="size">周辺画素の探索サイズ。奇数でなければならない</param>
            <param name="min">出力される最小値</param>
            <param name="max">出力される最大値</param>
        </member>
        <member name="T:OpenCvSharp.Extensions.BitmapConverter">
            <summary>
            static class which provides conversion between System.Drawing.Bitmap and Mat
            </summary>
        </member>
        <member name="M:OpenCvSharp.Extensions.BitmapConverter.ToMat(System.Drawing.Bitmap)">
            <summary>
            Converts System.Drawing.Bitmap to Mat
            </summary>
            <param name="src">System.Drawing.Bitmap object to be converted</param>
            <returns>A Mat object which is converted from System.Drawing.Bitmap</returns>
        </member>
        <member name="M:OpenCvSharp.Extensions.BitmapConverter.ToMat(System.Drawing.Bitmap,OpenCvSharp.Mat)">
            <summary>
            Converts System.Drawing.Bitmap to Mat
            </summary>
            <param name="src">System.Drawing.Bitmap object to be converted</param>
            <param name="dst">A Mat object which is converted from System.Drawing.Bitmap</param>
        </member>
        <member name="M:OpenCvSharp.Extensions.BitmapConverter.ToBitmap(OpenCvSharp.Mat)">
            <summary>
            Converts Mat to System.Drawing.Bitmap
            </summary>
            <param name="src">Mat</param>
            <returns></returns>
        </member>
        <member name="M:OpenCvSharp.Extensions.BitmapConverter.ToBitmap(OpenCvSharp.Mat,System.Drawing.Imaging.PixelFormat)">
            <summary>
            Converts Mat to System.Drawing.Bitmap
            </summary>
            <param name="src">Mat</param>
            <param name="pf">Pixel Depth</param>
            <returns></returns>
        </member>
        <member name="M:OpenCvSharp.Extensions.BitmapConverter.ToBitmap(OpenCvSharp.Mat,System.Drawing.Bitmap)">
            <summary>
            Converts Mat to System.Drawing.Bitmap
            </summary>
            <param name="src">Mat</param>
            <param name="dst">Mat</param>
            <remarks>Author: shimat, Gummo (ROI support)</remarks>
        </member>
        <member name="T:OpenCvSharp.Extensions.BitmapSourceConverter">
            <summary>
            Static class which provides conversion between System.Windows.Media.Imaging.BitmapSource and IplImage
            </summary>
        </member>
        <member name="M:OpenCvSharp.Extensions.BitmapSourceConverter.ToBitmapSource(OpenCvSharp.Mat)">
            <summary>
            Converts Mat to BitmapSource.
            </summary>
            <param name="src">Input IplImage</param>
            <returns>BitmapSource</returns>
        </member>
        <member name="M:OpenCvSharp.Extensions.BitmapSourceConverter.ToBitmapSource(OpenCvSharp.Mat,System.Int32,System.Int32,System.Windows.Media.PixelFormat,System.Windows.Media.Imaging.BitmapPalette)">
            <summary>
            Converts Mat to BitmapSource.
            </summary>
            <param name="src">Input IplImage</param>
            <param name="horizontalResolution"></param>
            <param name="verticalResolution"></param>
            <param name="pixelFormat"></param>
            <param name="palette"></param>
            <returns>BitmapSource</returns>
        </member>
        <member name="M:OpenCvSharp.Extensions.BitmapSourceConverter.ToBitmapSource(System.Drawing.Bitmap)">
            <summary>
            Converts System.Drawing.Bitmap to BitmapSource.
            </summary>
            <param name="src">Input System.Drawing.Bitmap</param>
            <remarks>http://www.codeproject.com/Articles/104929/Bitmap-to-BitmapSource</remarks>
            <returns>BitmapSource</returns>
        </member>
        <member name="M:OpenCvSharp.Extensions.BitmapSourceConverter.ToMat(System.Windows.Media.Imaging.BitmapSource)">
            <summary>
            Converts BitmapSource to Mat
            </summary>
            <param name="src">Input BitmapSource</param>
            <returns>IplImage</returns>
        </member>
        <member name="M:OpenCvSharp.Extensions.BitmapSourceConverter.ToMat(System.Windows.Media.Imaging.BitmapSource,OpenCvSharp.Mat)">
            <summary>
            Converts BitmapSource to Mat
            </summary>
            <param name="src">Input BitmapSource</param>
            <param name="dst">Output Mat</param>
        </member>
        <member name="M:OpenCvSharp.Extensions.BitmapSourceConverter.CopyFrom(OpenCvSharp.Mat,System.Windows.Media.Imaging.BitmapSource)">
            <summary>
            Copies pixel data from System.Windows.Media.Imaging.BitmapSource to IplImage instance
            </summary>
            <param name="mat"></param>
            <param name="wb"></param>
            <returns></returns>
        </member>
        <member name="T:OpenCvSharp.Extensions.CvExtensions">
            <summary>
            
            </summary>
        </member>
        <member name="M:OpenCvSharp.Extensions.CvExtensions.HoughLinesProbabilisticEx(OpenCvSharp.Mat,System.Double,System.Double,System.Int32,System.Double,System.Double,System.Double,System.Double)">
            <summary>
            
            </summary>
            <param name="img"></param>
            <param name="rho"></param>
            <param name="theta"></param>
            <param name="threshold"></param>
            <param name="minLineLength"></param>
            <param name="maxLineGap"></param>
            <param name="thetaMin"></param>
            <param name="thetaMax"></param>
            <returns></returns>
        </member>
        <member name="T:OpenCvSharp.Extensions.MyParallel">
            <summary>
            Task Parallel Library for .NET 2.0
            </summary>
        </member>
        <member name="F:OpenCvSharp.Extensions.MyParallel.NumThread">
            <summary>
            Number of Threads
            </summary>
        </member>
        <member name="M:OpenCvSharp.Extensions.MyParallel.For(System.Int32,System.Int32,System.Action{System.Int32})">
            <summary>
            Executes a for loop in which iterations may run in parallel.
            </summary>
            <param name="fromInclusive">The start index, inclusive.</param>
            <param name="toExclusive">The end index, exclusive.</param>
            <param name="body">The delegate that is invoked once per iteration.</param>
        </member>
        <member name="T:OpenCvSharp.Extensions.OS">
            <summary>
            
            </summary>
        </member>
        <member name="T:OpenCvSharp.Extensions.Runtime">
            <summary>
            
            </summary>
        </member>
        <member name="T:OpenCvSharp.Extensions.Platform">
            <summary>
            Provides information for the platform which the user is using 
            </summary>
        </member>
        <member name="F:OpenCvSharp.Extensions.Platform.OS">
            <summary>
            OS type
            </summary>
        </member>
        <member name="F:OpenCvSharp.Extensions.Platform.Runtime">
            <summary>
            Runtime type
            </summary>
        </member>
        <member name="T:OpenCvSharp.Extensions.WriteableBitmapConverter">
            <summary>
            Static class which provides conversion between System.Windows.Media.Imaging.WriteableBitmap and Mat
            </summary>
        </member>
        <member name="M:OpenCvSharp.Extensions.WriteableBitmapConverter.GetOptimumChannels(System.Windows.Media.PixelFormat)">
            <summary>
            指定したPixelFormatに適合するMatのチャンネル数を返す
            </summary>
            <param name="f"></param>
            <returns></returns>
        </member>
        <member name="M:OpenCvSharp.Extensions.WriteableBitmapConverter.GetOptimumType(System.Windows.Media.PixelFormat)">
            <summary>
            指定したPixelFormatに適合するMatTypeを返す
            </summary>
            <param name="f"></param>
            <returns></returns>
        </member>
        <member name="M:OpenCvSharp.Extensions.WriteableBitmapConverter.GetOptimumPixelFormats(OpenCvSharp.MatType)">
            <summary>
            指定したMatのビット深度・チャンネル数に適合するPixelFormatを返す
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:OpenCvSharp.Extensions.WriteableBitmapConverter.SwapChannelsIfNeeded(OpenCvSharp.Mat)">
            <summary>
            BGR -> RGB
            </summary>
            <param name="src"></param>
            <returns></returns>
        </member>
        <member name="M:OpenCvSharp.Extensions.WriteableBitmapConverter.ToWriteableBitmap(OpenCvSharp.Mat,System.Double,System.Double,System.Windows.Media.PixelFormat,System.Windows.Media.Imaging.BitmapPalette)">
            <summary>
            Converts Mat to WriteableBitmap.
            The arguments of this method corresponds the consructor of WriteableBitmap.
            </summary>
            <param name="src">Input Mat</param>
            <param name="dpiX">Horizontal dots per inch</param>
            <param name="dpiY">Vertical dots per inch</param>
            <param name="pf">Pixel format of output WriteableBitmap</param>
            <param name="bp">Bitmap pallette</param>
            <returns>WriteableBitmap</returns>
        </member>
        <member name="M:OpenCvSharp.Extensions.WriteableBitmapConverter.ToWriteableBitmap(OpenCvSharp.Mat,System.Windows.Media.PixelFormat)">
            <summary>
            Converts Mat to WriteableBitmap (dpi=96, BitmapPalette=null)
            </summary>
            <param name="src">Input Mat</param>
            <param name="pf">Pixel format of output WriteableBitmap</param>
            <returns>WriteableBitmap</returns>
        </member>
        <member name="M:OpenCvSharp.Extensions.WriteableBitmapConverter.ToWriteableBitmap(OpenCvSharp.Mat)">
            <summary>
            Converts Mat to WriteableBitmap (dpi=96, BitmapPalette=null)
            </summary>
            <param name="src">Input Mat</param>
            <returns>WriteableBitmap</returns>
        </member>
        <member name="M:OpenCvSharp.Extensions.WriteableBitmapConverter.ToWriteableBitmap(OpenCvSharp.Mat,System.Windows.Media.Imaging.WriteableBitmap)">
            <summary>
            Converts Mat to WriteableBitmap.
            This method is more efficient because new instance of WriteableBitmap is not allocated.
            </summary>
            <param name="src">Input Mat</param>
            <param name="dst">Output WriteableBitmap</param>
        </member>
    </members>
</doc>
