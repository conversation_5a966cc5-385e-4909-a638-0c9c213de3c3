﻿// TcpClient.cs

namespace eDoktor.Taikoban.FaceInterprocess
{
    //*************************************
    //* eDoktor.Common.dllのバージョンアップ対応について(2015/09/28)
    //* バージョンアップ版では以下が削除されている。
    //* ・MaxPacketSizeプロパティ
    //* ・受信バッファより大きいサイズのパケットを受信した時のパケットの終端を見つける処理
    //* 改修方法
    //*  1. MaxPacketSizeプロパティは当クラスで持つ。
    //*  2. 受信バッファより大きいサイズのパケットを受信した時のパケットの終端を見つける処理は
    //*     旧ソースを当クラスにコピーして、それを内部で使用するようにする。
    //*     コピーしたメソッド名は　SplitReceivedDataWithTerminator
    //*  3. その他必要なルーティンを(パケットサイズチェックルーティンなど)をコピーする。
    //*************************************
	public class TcpClient : eDoktor.Common.TcpClient
	{
		#region Fields
		private readonly object SyncRoot = new object();
		private volatile int _sequence;
        private eDoktor.Common.ComSpec _comSpec = new eDoktor.Common.ComSpec();
        // ▼ ADD Common.dll修正対応 2015/09/25
        public static readonly int DefaultMaxPacketSize = 1024;
        private int _maxPacketSize = DefaultMaxPacketSize;
        // ▲ ADD Common.dll修正対応 2015/09/25
        #endregion

        #region Properties
        public eDoktor.Common.ComSpec ComSpec
        {
            get { return _comSpec; }
            set { _comSpec = value; }
        }

        // ▼ ADD Common.dll修正対応 2015/09/25
        private int MaxPacketSize
        {
            get { return _maxPacketSize; }
            set { _maxPacketSize = value; }
        }
        // ▲ ADD Common.dll修正対応 2015/09/25

        #endregion

        #region Constructors
        public TcpClient()
		{
			MaxPacketSize = Packet.MaxPacketLength;
			ReceiveBufferSize = 4096;
		}
		#endregion

		#region Public Methods
		public Packet SendPacketAndWaitForResponse(Packet packet, int millisecondsTimeout)
		{
			return Transceive(packet, delegate(eDoktor.Common.Packet receivedPacket) { return (((receivedPacket as Packet).Command == Packet.GetResponseType(packet.Command)) && ((receivedPacket as Packet).Sequence == packet.Sequence)); }, millisecondsTimeout) as Packet;
		}
        #endregion

        #region Overridden Methods
        protected override eDoktor.Common.Packet[] SplitReceivedDataIntoPackets(byte[] receivedData, out byte[] fragment)
		{
			byte[][] rawDataList = SplitReceivedDataWithTerminator(receivedData, out fragment, Packet.TerminatorBytes);

			System.Collections.Generic.List<eDoktor.Common.Packet> packetList = new System.Collections.Generic.List<eDoktor.Common.Packet>();

			foreach (byte[] rawData in rawDataList)
			{
				packetList.Add(new Packet(rawData));
			}

            // ▼ MOD Common.dll修正対応 2015/09/25
            //return packetList.ToArray();
            eDoktor.Common.Packet[] rtnPacket = packetList.ToArray();

            if ((fragment != null) && (fragment.Length >= MaxPacketSize))
            {
                eDoktor.Common.Trace.OutputDebugTrace("{0}/{1}", fragment.Length, MaxPacketSize);

                throw new System.Net.Sockets.SocketException((int)eDoktor.Common.Network.SocketError.WSAENOBUFS);
            }
            return rtnPacket;
            // ▲ MOD Common.dll修正対応 2015/09/25
		}

		protected int GetNextSequence()
		{
			using (eDoktor.Common.TimedLock.Lock(SyncRoot))
			{
				if (++_sequence > Packet.MaxSequence)
				{
					_sequence = Packet.MinSequence;
				}

				return _sequence;
			}
		}

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);

            if (_comSpec != null)
            {
                _comSpec.Dispose();
            }
        }
		#endregion

        // ▼ ADD Common.dll修正対応 2015/09/25
        #region バージョンアップ前eDoktor.Commn.TcpClientt.csよりコピー
        private static byte[][] SplitReceivedDataWithTerminator(byte[] receivedData, out byte[] fragment, byte[] terminator)
        {
            fragment = null;
            int terminatorIndex = 0;
            bool hit = false;
            int startIndex = 0;
            int currentIndex = 0;

            System.Collections.Generic.List<byte[]> rawDataList = new System.Collections.Generic.List<byte[]>();

            foreach (byte b in receivedData)
            {
                if (((terminatorIndex == 0) || (hit)) && (b == terminator[terminatorIndex]))
                {
                    if (terminatorIndex == terminator.Length - 1)
                    {
                        int rawDataLength = currentIndex - startIndex + 1;
                        byte[] rawData = eDoktor.Common.ByteArray.Clone(receivedData, startIndex, rawDataLength);
                        rawDataList.Add(rawData);

                        startIndex = currentIndex + 1;
                        hit = false;
                        terminatorIndex = 0;
                    }
                    else
                    {
                        hit = true;
                        ++terminatorIndex;
                    }
                }
                else
                {
                    hit = false;
                    terminatorIndex = 0;
                }

                ++currentIndex;
            }

            if (startIndex < currentIndex)
            {
                int fragmentLength = currentIndex - startIndex;
                fragment = eDoktor.Common.ByteArray.Clone(receivedData, startIndex, fragmentLength);
            }

            return rawDataList.ToArray();
        }
        #endregion
        // ▲ ADD Common.dll修正対応 2015/09/25

	}
}
