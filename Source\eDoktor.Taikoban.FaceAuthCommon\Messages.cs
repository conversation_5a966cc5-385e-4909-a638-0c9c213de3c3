﻿using System.Collections.Generic;

namespace eDoktor.Taikoban.FaceAuthCommon
{
    /// <summary>
    /// メッセージ情報
    /// </summary>
    public class Messages
    {
        /// <summary>
        /// アカウントID置き換え文字
        /// </summary>
        private const string ACCOUNT_ID = "%AccountId%";

        /// <summary>
        /// ユーザ名置き換え文字
        /// </summary>
        private const string USER_NAME = "%UserName%";

        /// <summary>
        /// メッセージリスト
        /// </summary>
        private static Dictionary<string, string> _messageList = new Dictionary<string, string>();

        /// <summary>
        /// メッセージリスト取得
        /// </summary>
        public static Dictionary<string, string> MessageList { get { return _messageList; } }

        /// <summary>
        /// メッセージ設定
        /// </summary>
        /// <param name="messageId">メッセージID</param>
        /// <param name="message">メッセージ内容</param>
        public static void SetMessageList(string messageId, string message)
        {
            if (!string.IsNullOrEmpty(messageId) && !string.IsNullOrEmpty(message))
            {
                _messageList[messageId] = message;
            }
        }

        /// <summary>
        /// メッセージ取得
        /// </summary>
        /// <param name="messageId">メッセージID</param>
        /// <returns></returns>
        public static string Message(string messageId)
        {
            var message = string.Empty;
            _messageList.TryGetValue(messageId, out message);
            return message;
        }

        /// <summary>
        /// メッセージ取得("%AccountId%"をIDに置き換え)
        /// </summary>
        /// <param name="messageId">メッセージ</param>
        /// <param name="accountId">ログオンID</param>
        /// <returns></returns>
        public static string Message(string messageId, string accountId)
        {
            var message = string.Empty;

            if (_messageList.TryGetValue(messageId, out message))
            {
                message = message.Replace(ACCOUNT_ID, accountId);
            }

            return message;
        }

        /// <summary>
        /// メッセージ取得("%AccountId%"と"%UserName%"をID、氏名に置き換え)
        /// </summary>
        /// <param name="messageId"></param>
        /// <param name="accountId"></param>
        /// <param name="name"></param>
        /// <returns></returns>
        public static string Message(string messageId, string accountId, string name)
        {
            var message = string.Empty;

            if (_messageList.TryGetValue(messageId, out message))
            {
                message = message.Replace(ACCOUNT_ID, accountId);
                message = message.Replace(USER_NAME, name);
            }

            return message;
        }
    }
}
