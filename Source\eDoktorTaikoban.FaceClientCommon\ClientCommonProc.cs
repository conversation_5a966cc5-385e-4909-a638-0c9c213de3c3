﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace eDoktor.Taikoban.FaceAuthCommon
{
    /// <summary>
    /// クライアント共通処理
    /// </summary>
    public class ClientCommonProc
    {
        /// <summary>
        /// エラーIDからエラーメッセージを決定
        /// </summary>
        /// <param name="errorId">エラーID</param>
        /// <returns>エラーメッセージ</returns>
        public static string ErrorIdToErrorMessage(EnumErrorId errorId)
        {
            return ErrorIdToErrorMessage(errorId, string.Empty);
        }

        /// <summary>
        /// エラーIDからエラーメッセージを決定
        /// </summary>
        /// <param name="errorId">エラーID</param>
        /// <param name="logonId">ログオンID</param>
        /// <returns>エラーメッセージ</returns>
        public static string ErrorIdToErrorMessage(EnumErrorId errorId, string logonId)
        {
            var errMsg = string.Empty;

            try
            {
                switch (errorId)
                {
                    case EnumErrorId.Exception:
                        // 処理失敗
                        errMsg = Messages.Message(MessageId.Message001);
                        break;
                    case EnumErrorId.InputDataErr:
                        // 入力データ不正
                        errMsg = Messages.Message(MessageId.Message003);
                        break;
                    case EnumErrorId.DbErr:
                        // DBエラー
                        errMsg = Messages.Message(MessageId.Message002);
                        break;
                    case EnumErrorId.NoStaff:
                        // 該当職員なし
                        errMsg = Messages.Message(MessageId.Message010, logonId);
                        break;
                    case EnumErrorId.DifferentMode:
                        // モード違い
                        errMsg = Messages.Message(MessageId.Message011);
                        break;
                    case EnumErrorId.FaceSearchNG:
                        // 顔サーチNG
                        errMsg = Messages.Message(MessageId.Message012);
                        break;
                    case EnumErrorId.FaceTemplateCreateFail:
                        // 顔テンプレート作成失敗
                        errMsg = Messages.Message(MessageId.Message015);
                        break;
                    case EnumErrorId.HoldOver:
                        // 保持数オーバー
                        errMsg = Messages.Message(MessageId.Message009);
                        break;
                    case EnumErrorId.NoUpdate:
                        // 更新対象外
                        errMsg = Messages.Message(MessageId.Message012);
                        break;
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }

            return errMsg;
        }

        /// <summary>
        /// 画像が画面の表示領域内に収まるようなサイズを算出
        /// </summary>
        /// <param name="originalImageSize">オリジナル画像サイズ</param>
        /// <param name="width">変更する幅</param>
        /// <param name="height">変更する高さ</param>
        /// <param name="location">変更後表示位置</param>
        public static void PictureBoxResize(Size originalImageSize, ref int width, ref int height, ref Point location)
        {
            // 画面の表示領域に入るように調整する高さと幅を計算

            // 幅に合わせる場合の高さを算出
            var newHeight = originalImageSize.Height * width / originalImageSize.Width;

            if (height > newHeight)
            {
                // 幅に合わせると表示領域に収まる
                // 表示領域の真ん中に表示されるように表示位置Yを調整
                location.Y += ((height - newHeight) / 2);

                // 幅に合わせて高さ調整
                height = newHeight;
            }
            else
            {
                // 幅に合わせると高さが収まらない
                // 高さに合わせる場合の幅を算出
                var newWidth = originalImageSize.Width * height / originalImageSize.Height;

                if (width > newWidth)
                {
                    // 高さに合わせると表示領域に収まる
                    // 表示領域の真ん中に表示されるように表示位置Xを調整
                    location.X += ((width - newWidth) / 2);

                    // 高さに合わせて幅調整
                    width = newWidth;
                }
                else
                {
                    // 比率が同じであればそのまま領域のサイズにはまる
                }
            }
        }
    }
}
