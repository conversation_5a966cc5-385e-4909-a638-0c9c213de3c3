<?xml version="1.0"?>
<doc>
    <assembly>
        <name>AForge.Controls</name>
    </assembly>
    <members>
        <member name="T:AForge.Controls.PictureBox">
            <summary>
            Picture box control for displaying an image.
            </summary>
            
            <remarks><para>This control is inherited from System.Windows.Forms.PictureBox and is
            aimed to resolve one of its issues - inability to display images with high color depth,
            like 16 bpp grayscale, 48 bpp and 64 bpp color images. .NET framework does not handle
            16 bpp grayscale images at all, throwing exception when user tries to display them. Color
            images with 48 bpp and 64 bpp are "kind of" supported, but only maximum of 13 bits for each
            color plane are allowed. Therefore this control is created, which allows to display as
            16 bpp grayscale images, as 48 bpp and 64 bpp color images.</para>
            
            <para><note>To display high color depth images, the control does internal conversion of them
            to lower color depth images - 8 bpp grayscale, 24 bpp and 32 bpp color images respectively. In
            the case source image already has low color depth, it is displayed without any conversions.
            </note></para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.PictureBox.Image">
            <summary>
            Gets or sets the image that the PictureBox displays.
            </summary>
            
            <remarks><para>The property is used to set image to be displayed or to get currently
            displayed image.</para>
            
            <para><note>In the case if source image has high color depth, like 16 bpp grayscale image,
            48 bpp or 64 bpp color image, it is converted to lower color depth before displaying -
            to 8 bpp grayscale, 24 bpp or 32 bpp color image respectively.</note></para>
            
            <para><note>During color conversion the original source image is kept unmodified, but internal
            converted copy is created. The property always returns original source image.</note></para>
            </remarks>
            
        </member>
        <member name="T:AForge.Controls.ColorSlider">
            <summary>
            Color slider control.
            </summary>
            
            <remarks><para>The control represent a color slider, which allows selecting
            one or two values in the [0, 255] range. The application of this control
            includes mostly areas of image processing and computer vision, where it is required
            to select color threshold or ranges for different type of color filtering.</para>
            
            <para>Depending on the control's <see cref="P:AForge.Controls.ColorSlider.Type"/>, it has different look and may suite
            different tasks. See documentation to <see cref="T:AForge.Controls.ColorSlider.ColorSliderType"/> for information
            about available type and possible control's looks.</para>
            </remarks>
            
        </member>
        <member name="M:AForge.Controls.ColorSlider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Controls.ColorSlider"/> class.
            </summary>
            
        </member>
        <member name="M:AForge.Controls.ColorSlider.Dispose(System.Boolean)">
            <summary>
            Dispose the object.
            </summary>
            
            <param name="disposing">Specifies if disposing was invoked by user's code.</param>
            
        </member>
        <member name="E:AForge.Controls.ColorSlider.ValuesChanged">
            <summary>
            An event, to notify about changes of <see cref="P:AForge.Controls.ColorSlider.Min"/> or <see cref="P:AForge.Controls.ColorSlider.Max"/> properties.
            </summary>
            
            <remarks><para>The event is fired after changes of <see cref="P:AForge.Controls.ColorSlider.Min"/> or <see cref="P:AForge.Controls.ColorSlider.Max"/> property,
            which is caused by user dragging the corresponding control’s arrow (slider).</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.ColorSlider.StartColor">
             <summary>
             Start color for gradient filling.
             </summary>
            
             <remarks>See documentation to <see cref="T:AForge.Controls.ColorSlider.ColorSliderType"/> enumeration for information about
             the usage of this property.</remarks>
            
        </member>
        <member name="P:AForge.Controls.ColorSlider.EndColor">
             <summary>
             End color for gradient filling.
             </summary>
            
             <remarks>See documentation to <see cref="T:AForge.Controls.ColorSlider.ColorSliderType"/> enumeration for information about
             the usage of this property.</remarks>
            
        </member>
        <member name="P:AForge.Controls.ColorSlider.FillColor">
             <summary>
             Color to fill control's background in filtered zones.
             </summary>
            
             <remarks>See documentation to <see cref="T:AForge.Controls.ColorSlider.ColorSliderType"/> enumeration for information about
             the usage of this property.</remarks>
            
        </member>
        <member name="P:AForge.Controls.ColorSlider.Type">
             <summary>
             Specifies control's type.
             </summary>
             
             <remarks>See documentation to <see cref="T:AForge.Controls.ColorSlider.ColorSliderType"/> enumeration for information about
             the usage of this property.</remarks>
            
        </member>
        <member name="P:AForge.Controls.ColorSlider.Min">
            <summary>
            Minimum selected value, [0, 255].
            </summary>
            
        </member>
        <member name="P:AForge.Controls.ColorSlider.Max">
            <summary>
            Maximum selected value, [0, 255].
            </summary>
            
        </member>
        <member name="P:AForge.Controls.ColorSlider.DoubleArrow">
            <summary>
            Single or Double arrow slider control.
            </summary>
            
            <remarks><para>The property specifies if the slider has one or two selection arrows (sliders).</para>
            
            <para>The single arrow allows only to specify one value, which is set by <see cref="P:AForge.Controls.ColorSlider.Min"/>
            property. The single arrow slider is useful for applications, where it is required to select
            color threshold, for example.</para>
            
            <para>The double arrow allows to specify two values, which are set by <see cref="P:AForge.Controls.ColorSlider.Min"/>
            and <see cref="P:AForge.Controls.ColorSlider.Max"/> properties. The double arrow slider is useful for applications, where it is
            required to select filtering color range, for example.</para>
            </remarks>
            
        </member>
        <member name="T:AForge.Controls.ColorSlider.ColorSliderType">
             <summary>
             Enumeration of color slider types.
             </summary>
             
             <remarks>
             <para>The <see cref="F:AForge.Controls.ColorSlider.ColorSliderType.Gradient"/> slider's type supposes the control's
             background filled with gradient startting from <see cref="P:AForge.Controls.ColorSlider.StartColor"/> color and ending
             with <see cref="P:AForge.Controls.ColorSlider.EndColor"/> color. The <see cref="P:AForge.Controls.ColorSlider.FillColor"/> color does not have
             impact on control's look.</para>
             
             <para>This type allows as one-arrow, as two-arrows control.</para>
             
             <para><b>Sample control's look:</b></para>
             <img src="img/controls/slider_gradient.png" width="258" height="17"/>    
             
             <para>The <see cref="F:AForge.Controls.ColorSlider.ColorSliderType.InnerGradient"/> slider's type supposes the control's
             background filled with gradient startting from <see cref="P:AForge.Controls.ColorSlider.StartColor"/> color and ending
             with <see cref="P:AForge.Controls.ColorSlider.EndColor"/> color. In addition the areas, which are outside of
             [<see cref="P:AForge.Controls.ColorSlider.Min"/>, <see cref="P:AForge.Controls.ColorSlider.Max"/>] range, are filled with <see cref="P:AForge.Controls.ColorSlider.FillColor"/> color.</para>
             
             <para>This type allows only two-arrows control.</para>
             
             <para><b>Sample control's look:</b></para>
             <img src="img/controls/slider_inner_gradient.png" width="258" height="17"/>
             
             <para>The <see cref="F:AForge.Controls.ColorSlider.ColorSliderType.OuterGradient"/> slider's type supposes the
             control's background filled with gradient startting from <see cref="P:AForge.Controls.ColorSlider.StartColor"/> color
             and ending with <see cref="P:AForge.Controls.ColorSlider.EndColor"/> color. In addition the area, which is inside of
             [<see cref="P:AForge.Controls.ColorSlider.Min"/>, <see cref="P:AForge.Controls.ColorSlider.Max"/>] range, is filled with <see cref="P:AForge.Controls.ColorSlider.FillColor"/> color.</para>
             
             <para>This type allows only two-arrows control.</para>
             
             <para><b>Sample control's look:</b></para>
             <img src="img/controls/slider_outer_gradient.png" width="258" height="17"/>
             
             <para>The <see cref="F:AForge.Controls.ColorSlider.ColorSliderType.Threshold"/> slider's type supposes filling areas
             outside of [<see cref="P:AForge.Controls.ColorSlider.Min"/>, <see cref="P:AForge.Controls.ColorSlider.Max"/>] range with <see cref="P:AForge.Controls.ColorSlider.StartColor"/> and
             inside the range with <see cref="P:AForge.Controls.ColorSlider.EndColor"/>. The <see cref="P:AForge.Controls.ColorSlider.FillColor"/> color does not
             have impact on control's look.</para>
             
             <para>This type allows as one-arrow, as two-arrows control.</para>
             
             <para><b>Sample control's look:</b></para>
             <img src="img/controls/slider_threshold.png" width="258" height="17"/>
             </remarks>
            
        </member>
        <member name="F:AForge.Controls.ColorSlider.ColorSliderType.Gradient">
            <summary>
            Gradient color slider type.
            </summary>
        </member>
        <member name="F:AForge.Controls.ColorSlider.ColorSliderType.InnerGradient">
            <summary>
            Inner gradient color slider type.
            </summary>
        </member>
        <member name="F:AForge.Controls.ColorSlider.ColorSliderType.OuterGradient">
            <summary>
            Outer gradient color slider type.
            </summary>
        </member>
        <member name="F:AForge.Controls.ColorSlider.ColorSliderType.Threshold">
            <summary>
            Threshold color slider type.
            </summary>
        </member>
        <member name="T:AForge.Controls.VideoSourcePlayer">
            <summary>
            Video source player control.
            </summary>
            
            <remarks><para>The control is aimed to play video sources, which implement
            <see cref="T:AForge.Video.IVideoSource"/> interface. To start playing a video
            the <see cref="P:AForge.Controls.VideoSourcePlayer.VideoSource"/> property should be initialized first and then
            <see cref="M:AForge.Controls.VideoSourcePlayer.Start"/> method should be called. In the case if user needs to
            perform some sort of image processing with video frames before they are displayed,
            the <see cref="E:AForge.Controls.VideoSourcePlayer.NewFrame"/> event may be used.</para>
            
            <para>Sample usage:</para>
            <code>
            // set new frame event handler if we need processing of new frames
            playerControl.NewFrame += new VideoSourcePlayer.NewFrameHandler( this.playerControl_NewFrame );
            
            // create video source
            IVideoSource videoSource = new ...
            // start playing it
            playerControl.VideoSource = videoSource;
            playerControl.Start( );
            ...
            
            // new frame event handler
            private void playerControl_NewFrame( object sender, ref Bitmap image )
            {
                // process new frame somehow ...
                
                // Note: it may be even changed, so the control will display the result
                // of image processing done here
            }
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Controls.VideoSourcePlayer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Controls.VideoSourcePlayer"/> class.
            </summary>
        </member>
        <member name="M:AForge.Controls.VideoSourcePlayer.Start">
            <summary>
            Start video source and displaying its frames.
            </summary>
        </member>
        <member name="M:AForge.Controls.VideoSourcePlayer.Stop">
            <summary>
            Stop video source.
            </summary>
            
            <remarks><para>The method stops video source by calling its <see cref="M:AForge.Video.IVideoSource.Stop"/>
            method, which abourts internal video source's thread. Use <see cref="M:AForge.Controls.VideoSourcePlayer.SignalToStop"/> and
            <see cref="M:AForge.Controls.VideoSourcePlayer.WaitForStop"/> for more polite video source stopping, which gives a chance for
            video source to perform proper shut down and clean up.
            </para></remarks>
            
        </member>
        <member name="M:AForge.Controls.VideoSourcePlayer.SignalToStop">
            <summary>
            Signal video source to stop. 
            </summary>
            
            <remarks><para>Use <see cref="M:AForge.Controls.VideoSourcePlayer.WaitForStop"/> method to wait until video source
            stops.</para></remarks>
            
        </member>
        <member name="M:AForge.Controls.VideoSourcePlayer.WaitForStop">
            <summary>
            Wait for video source has stopped. 
            </summary>
            
            <remarks><para>Waits for video source stopping after it was signaled to stop using
            <see cref="M:AForge.Controls.VideoSourcePlayer.SignalToStop"/> method. If <see cref="M:AForge.Controls.VideoSourcePlayer.SignalToStop"/> was not called, then
            it will be called automatically.</para></remarks>
            
        </member>
        <member name="M:AForge.Controls.VideoSourcePlayer.GetCurrentVideoFrame">
            <summary>
            Get clone of current video frame displayed by the control.
            </summary>
            
            <returns>Returns copy of the video frame, which is currently displayed
            by the control - the last video frame received from video source. If the
            control did not receive any video frames yet, then the method returns
            <see langword="null"/>.</returns>
            
        </member>
        <member name="F:AForge.Controls.VideoSourcePlayer.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:AForge.Controls.VideoSourcePlayer.Dispose(System.Boolean)">
            <summary> 
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:AForge.Controls.VideoSourcePlayer.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:AForge.Controls.VideoSourcePlayer.AutoSizeControl">
            <summary>
            Auto size control or not.
            </summary>
            
            <remarks><para>The property specifies if the control should be autosized or not.
            If the property is set to <see langword="true"/>, then the control will change its size according to
            video size and control will change its position automatically to be in the center
            of parent's control.</para>
            
            <para><note>Setting the property to <see langword="true"/> has no effect if
            <see cref="P:System.Windows.Forms.Control.Dock"/> property is set to <see cref="F:System.Windows.Forms.DockStyle.Fill"/>.</note></para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.VideoSourcePlayer.BorderColor">
            <summary>
            Control's border color.
            </summary>
            
            <remarks><para>Specifies color of the border drawn around video frame.</para></remarks>
            
        </member>
        <member name="P:AForge.Controls.VideoSourcePlayer.VideoSource">
            <summary>
            Video source to play.
            </summary>
            
            <remarks><para>The property sets the video source to play. After setting the property the
            <see cref="M:AForge.Controls.VideoSourcePlayer.Start"/> method should be used to start playing the video source.</para>
            
            <para><note>Trying to change video source while currently set video source is still playing
            will generate an exception. Use <see cref="P:AForge.Controls.VideoSourcePlayer.IsRunning"/> property to check if current video
            source is still playing or <see cref="M:AForge.Controls.VideoSourcePlayer.Stop"/> or <see cref="M:AForge.Controls.VideoSourcePlayer.SignalToStop"/> and <see cref="M:AForge.Controls.VideoSourcePlayer.WaitForStop"/>
            methods to stop current video source.</note></para>
            </remarks>
            
            <exception cref="T:System.Exception">Video source can not be changed while current video source is still running.</exception>
            
        </member>
        <member name="P:AForge.Controls.VideoSourcePlayer.IsRunning">
            <summary>
            State of the current video source.
            </summary>
            
            <remarks><para>Current state of the current video source object - running or not.</para></remarks>
            
        </member>
        <member name="E:AForge.Controls.VideoSourcePlayer.NewFrame">
            <summary>
            New frame event.
            </summary>
            
            <remarks><para>The event is fired on each new frame received from video source. The
            event is fired right after receiving and before displaying, what gives user a chance to
            perform some image processing on the new frame and/or update it.</para>
            
            <para><note>Users should not keep references of the passed to the event handler image.
            If user needs to keep the image, it should be cloned, since the original image will be disposed
            by the control when it is required.</note></para>
            </remarks>
            
        </member>
        <member name="E:AForge.Controls.VideoSourcePlayer.PlayingFinished">
            <summary>
            Playing finished event.
            </summary>
            
            <remarks><para>The event is fired when/if video playing finishes. The reason of video
            stopping is provided as an argument to the event handler.</para></remarks>
            
        </member>
        <member name="T:AForge.Controls.VideoSourcePlayer.NewFrameHandler">
            <summary>
            Delegate to notify about new frame.
            </summary>
            
            <param name="sender">Event sender.</param>
            <param name="image">New frame.</param>
            
        </member>
        <member name="T:AForge.Controls.SliderControl">
             <summary>
             Slider control.
             </summary>
            
             <remarks>
             <para>The control represents a slider, which can be dragged in the [-1, 1] range.
             Default position of the slider is set 0, which corresponds to center of the control.<br />
             <img src="img/controls/slider_control.png" width="227" height="56" />
             </para>
             </remarks>
            
        </member>
        <member name="M:AForge.Controls.SliderControl.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Controls.SliderControl"/> class.
            </summary>
        </member>
        <member name="F:AForge.Controls.SliderControl.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:AForge.Controls.SliderControl.Dispose(System.Boolean)">
            <summary> 
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:AForge.Controls.SliderControl.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:AForge.Controls.SliderControl.ResetPositionOnMouseRelease">
            <summary>
            Determines behaviour of manipulator, when mouse button is released.
            </summary>
            
            <remarks>
            <para>The property controls behaviour of manipulator on releasing mouse button. If
            the property is set to <see langword="true"/>, then position of manipulator is reset
            to 0, when mouse button is released. Otherwise manipulator stays on the place,
            where it was left.</para>
            
            <para>Default value is set to <see langword="true"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.SliderControl.BorderColor">
            <summary>
            Color used for drawing borders.
            </summary>
            
            <remarks>
            <para>Default value is set to <see cref="P:System.Drawing.Color.Black"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.SliderControl.PositiveAreaBrush">
            <summary>
            Background color used for filling area corresponding to positive values.
            </summary>
            
            <remarks>
            <para>Default value is set to <see cref="P:System.Drawing.Color.White"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.SliderControl.NegativeAreaBrush">
            <summary>
            Background color used for filling area corresponding to negative values.
            </summary>
            
            <remarks>
            <para>Default value is set to <see cref="P:System.Drawing.Color.LightGray"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.SliderControl.ManipulatorColor">
            <summary>
            Color used for filling manipulator.
            </summary>
            
            <remarks>
            <para>Default value is set to <see cref="P:System.Drawing.Color.LightSeaGreen"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.SliderControl.IsHorizontal">
            <summary>
            Defines if control has horizontal or vertical look.
            </summary>
            
            <remarks>
            <para>Default value is set to <see langword="true"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.SliderControl.ManipulatorPosition">
            <summary>
            Current manipulator's position, [-1, 1].
            </summary>
            
            <remarks><para>The property equals to current manipulator's position.</para>
            </remarks>
            
        </member>
        <member name="E:AForge.Controls.SliderControl.PositionChanged">
            <summary>
            Event used for notification about manipulator's position changes.
            </summary>
        </member>
        <member name="T:AForge.Controls.SliderControl.PositionChangedHandler">
            <summary>
            Delegate used for notification about manipulator's position changes.
            </summary>
            
            <param name="sender">Event sender - object sending the event.</param>
            <param name="position">Current position of manipulator.</param>
            
        </member>
        <member name="T:AForge.Controls.Joystick">
            <summary>
            The class provides simple API for enumerating available joysticks and checking their
            current status.
            </summary>
            
            <remarks><para>The class provides simple access to joysticks (game controllers) through using
            Win32 API, which allows to enumerate available devices and query their status (state of all buttons,
            axes, etc).</para>
            
            <para>Sample usage:</para>
            <code>
            // enumerate available devices
            List&lt;Joystick.DeviceInfo&gt; devices = Joystick.GetAvailableDevices( );
            
            foreach ( Joystick.DeviceInfo di in devices )
            {
                System.Diagnostics.Debug.WriteLine(
                    string.Format( "{0} : {1} ({2} axes, {3} buttons)",
                        di.ID, di.Name, di.Axes, di.Buttons ) );
            }
            
            
            // create new joystick and initialize it
            Joystick joystick = new Joystick( 0 );
            // get its current status
            Joystick.Status status = joystick.GetCurrentStatus( );
            // check if 1st button is pressed
            if ( status.IsButtonPressed( Joystick.Buttons.Button1 ) )
            {
                // 1st button is pressed
            }
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Controls.Joystick.GetAvailableDevices">
            <summary>
            Get list of available joysticks connected to the system.
            </summary>
            
            <returns>Returns list containing information about available joysticks connected to
            the system.</returns>
            
        </member>
        <member name="M:AForge.Controls.Joystick.#ctor">
             <summary>
             Initializes a new instance of the <see cref="T:AForge.Controls.Joystick"/> class.
             </summary>
             
             <remarks><para>This constructor does not make initialization of any joystick
             device, so <see cref="M:AForge.Controls.Joystick.Init(System.Int32)"/> method should be used before querying joystick
             status or properties.</para></remarks>
            
        </member>
        <member name="M:AForge.Controls.Joystick.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Controls.Joystick"/> class.
            </summary>
            
            <param name="id">Joystick ID to initialize, [0, 15].</param>
            
            <remarks><para>This constructor initializes joystick with specified ID using
            <see cref="M:AForge.Controls.Joystick.Init(System.Int32)"/> method, so the object becomes ready for querying joystick's
            status.</para></remarks>
            
        </member>
        <member name="M:AForge.Controls.Joystick.Init(System.Int32)">
            <summary>
            Initialize joystick with the specified ID.
            </summary>
            
            <param name="id">Joystick's ID to initialize, [0, 15].</param>
            
            <remarks><para></para></remarks>
            
            <exception cref="T:System.ArgumentException">Invalid joystick ID was specified. It must be in [0, 15] range.</exception>
            <exception cref="T:AForge.NotConnectedException">The requested joystick is not connected to the system.</exception>
            
        </member>
        <member name="M:AForge.Controls.Joystick.GetCurrentStatus">
             <summary>
             Get joystick's status.
             </summary>
             
             <returns>Returns current status of initialized joystick, which provides information
             about current state of all axes, buttons and point of view.</returns>
             
             <remarks><para><note>Before using this method the joystick object needs to be initialized
             using <see cref="M:AForge.Controls.Joystick.Init(System.Int32)"/> method or <see cref="M:AForge.Controls.Joystick.#ctor(System.Int32)"/> constructor.</note></para></remarks>
             
             <exception cref="T:AForge.NotConnectedException">The requested joystick is not connected to the system.</exception>
             <exception cref="T:System.ApplicationException">Joystick was not initialized.</exception>
            
        </member>
        <member name="P:AForge.Controls.Joystick.Info">
             <summary>
             Information about initialized joystick.
             </summary>
            
             <remarks><para>The property keeps information about joystick, which was
             initialized using <see cref="M:AForge.Controls.Joystick.Init(System.Int32)"/> method. If no joystick was initialized,
             then accessing this property will generate <see cref="T:System.ApplicationException"/>
             exception.</para></remarks>
             
             <exception cref="T:System.ApplicationException">Joystick was not initialized.</exception>
            
        </member>
        <member name="T:AForge.Controls.Joystick.DeviceInfo">
            <summary>
            Information about joystick connected to the system.
            </summary>
            
        </member>
        <member name="F:AForge.Controls.Joystick.DeviceInfo.ID">
            <summary>
            Joystick ID, [0..15].
            </summary>
        </member>
        <member name="P:AForge.Controls.Joystick.DeviceInfo.Name">
            <summary>
            Joystick name.
            </summary>
        </member>
        <member name="P:AForge.Controls.Joystick.DeviceInfo.Axes">
            <summary>
            Number of joystick axes.
            </summary>
        </member>
        <member name="P:AForge.Controls.Joystick.DeviceInfo.Buttons">
            <summary>
            Number of joystick buttons.
            </summary>
        </member>
        <member name="T:AForge.Controls.Joystick.Status">
             <summary>
             Class describing current joystick's status.
             </summary>
            
             <remarks><para><note>All joystick axes' positions are measured in [-1, 1] range, where
             0 corresponds to center position - axis is not deflected (directed) to any side.</note></para></remarks>
            
        </member>
        <member name="M:AForge.Controls.Joystick.Status.IsButtonPressed(AForge.Controls.Joystick.Buttons)">
             <summary>
             Check if certain button (or combination of buttons) is pressed.
             </summary>
             
             <param name="button">Button to check state of.</param>
             
             <returns>Returns <see langword="true"/> if the specified button is pressed or
             <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="P:AForge.Controls.Joystick.Status.XAxis">
            <summary>
            Position of X axis, [-1, 1].
            </summary>
        </member>
        <member name="P:AForge.Controls.Joystick.Status.YAxis">
            <summary>
            Position of Y axis, [-1, 1].
            </summary>
        </member>
        <member name="P:AForge.Controls.Joystick.Status.ZAxis">
            <summary>
            Position of Z axis, [-1, 1].
            </summary>
        </member>
        <member name="P:AForge.Controls.Joystick.Status.RAxis">
            <summary>
            Position of R axis - 4th joystick's axes, [-1, 1].
            </summary>
        </member>
        <member name="P:AForge.Controls.Joystick.Status.UAxis">
            <summary>
            Position of U axis - 5th joystick's axes, [-1, 1].
            </summary>
        </member>
        <member name="P:AForge.Controls.Joystick.Status.VAxis">
            <summary>
            Position of V axis - 6th joystick's axes, [-1, 1].
            </summary>
        </member>
        <member name="P:AForge.Controls.Joystick.Status.Buttons">
            <summary>
            Joystick buttons' state.
            </summary>
        </member>
        <member name="P:AForge.Controls.Joystick.Status.PointOfView">
            <summary>
            Current point of view state, [0, 359].
            </summary>
            
        </member>
        <member name="T:AForge.Controls.Joystick.Buttons">
            <summary>
            Flags enumeration of joystick buttons.
            </summary>
        </member>
        <member name="F:AForge.Controls.Joystick.Buttons.Button1">
            <summary>
            1st button.
            </summary>
        </member>
        <member name="F:AForge.Controls.Joystick.Buttons.Button2">
            <summary>
            2nd button.
            </summary>
        </member>
        <member name="F:AForge.Controls.Joystick.Buttons.Button3">
            <summary>
            3rd button.
            </summary>
        </member>
        <member name="F:AForge.Controls.Joystick.Buttons.Button4">
            <summary>
            4th button.
            </summary>
        </member>
        <member name="F:AForge.Controls.Joystick.Buttons.Button5">
            <summary>
            5th button.
            </summary>
        </member>
        <member name="F:AForge.Controls.Joystick.Buttons.Button6">
            <summary>
            6th button.
            </summary>
        </member>
        <member name="F:AForge.Controls.Joystick.Buttons.Button7">
            <summary>
            7th button.
            </summary>
        </member>
        <member name="F:AForge.Controls.Joystick.Buttons.Button8">
            <summary>
            8th button.
            </summary>
        </member>
        <member name="F:AForge.Controls.Joystick.Buttons.Button9">
            <summary>
            9th button.
            </summary>
        </member>
        <member name="F:AForge.Controls.Joystick.Buttons.Button10">
            <summary>
            10th button.
            </summary>
        </member>
        <member name="F:AForge.Controls.Joystick.Buttons.Button11">
            <summary>
            11th button.
            </summary>
        </member>
        <member name="F:AForge.Controls.Joystick.Buttons.Button12">
            <summary>
            12th button.
            </summary>
        </member>
        <member name="F:AForge.Controls.Joystick.Buttons.Button13">
            <summary>
            13th button.
            </summary>
        </member>
        <member name="F:AForge.Controls.Joystick.Buttons.Button14">
            <summary>
            14th button.
            </summary>
        </member>
        <member name="F:AForge.Controls.Joystick.Buttons.Button15">
            <summary>
            15th button.
            </summary>
        </member>
        <member name="F:AForge.Controls.Joystick.Buttons.Button16">
            <summary>
            16th button.
            </summary>
        </member>
        <member name="T:AForge.Controls.HuePicker">
            <summary>
            Hue picker control.
            </summary>
            
            <remarks><para>The control allows selecting hue value (or range) from HSL color space. Hue values
            are integer values in the [0, 359] range.</para>
            
            <para>If control's type is set to <see cref="F:AForge.Controls.HuePicker.HuePickerType.Value"/>, then it allows selecting single
            hue value and looks like this:<br/>
            <img src="img/controls/hue_picker1.png" width="220" height="220"/>
            </para>
            
            <para>If control's type is set to <see cref="F:AForge.Controls.HuePicker.HuePickerType.Range"/>, then it allows selecting range
            of hue values and looks like this:<br/>
            <img src="img/controls/hue_picker2.png" width="220" height="220"/>
            </para>
            </remarks>
            
        </member>
        <member name="M:AForge.Controls.HuePicker.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Controls.HuePicker"/> class.
            </summary>
            
        </member>
        <member name="M:AForge.Controls.HuePicker.Dispose(System.Boolean)">
            <summary>
            Dispose the object.
            </summary>
            
            <param name="disposing">Specifies if disposing was invoked by user's code.</param>
            
        </member>
        <member name="M:AForge.Controls.HuePicker.OnPaint(System.Windows.Forms.PaintEventArgs)">
            <summary>
            Paint the controls.
            </summary>
            
            <param name="pe">Paint event arguments.</param>
            
        </member>
        <member name="E:AForge.Controls.HuePicker.ValuesChanged">
            <summary>
            An event, to notify about changes of <see cref="P:AForge.Controls.HuePicker.Min"/> or <see cref="P:AForge.Controls.HuePicker.Max"/> properties.
            </summary>
            
            <remarks><para>The event is fired after changes of its <see cref="P:AForge.Controls.HuePicker.Value"/>, <see cref="P:AForge.Controls.HuePicker.Min"/> or
            <see cref="P:AForge.Controls.HuePicker.Max"/> properties, which is caused by user dragging the corresponding hue picker's bullets.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.HuePicker.Value">
            <summary>
            Selected value of the hue picker control in <see cref="F:AForge.Controls.HuePicker.HuePickerType.Value"/> mode.
            </summary>
        </member>
        <member name="P:AForge.Controls.HuePicker.Min">
            <summary>
            Minimum selected value of the hue picker control in <see cref="F:AForge.Controls.HuePicker.HuePickerType.Range"/> mode.
            </summary>
        </member>
        <member name="P:AForge.Controls.HuePicker.Max">
            <summary>
            Maximum selected value of the hue picker control in <see cref="F:AForge.Controls.HuePicker.HuePickerType.Range"/> mode.
            </summary>
        </member>
        <member name="P:AForge.Controls.HuePicker.Type">
            <summary>
            Current type of the hue picker control.
            </summary>
            
            <remarks><para>See <see cref="T:AForge.Controls.HuePicker.HuePickerType"/> enumeration for description of the available types.</para></remarks>
            
        </member>
        <member name="T:AForge.Controls.HuePicker.HuePickerType">
            <summary>
            Enumeration of hue picker types.
            </summary>
            
            <remarks>
            <para>The <see cref="F:AForge.Controls.HuePicker.HuePickerType.Value"/> type provides single bullet to drag, which allows
            selecting single hue value. The value is accessible through <see cref="F:AForge.Controls.HuePicker.HuePickerType.Value"/> property.</para>
            
            <para>The <see cref="F:AForge.Controls.HuePicker.HuePickerType.Range"/> type provides two bullets to drag, which correspond
            to minimum and maximum values of the hue range. These values are accessible through
            <see cref="P:AForge.Controls.HuePicker.Min"/> and <see cref="P:AForge.Controls.HuePicker.Max"/> properties.</para>
            </remarks>
            
        </member>
        <member name="F:AForge.Controls.HuePicker.HuePickerType.Value">
            <summary>
            Selecting single hue value.
            </summary>
        </member>
        <member name="F:AForge.Controls.HuePicker.HuePickerType.Range">
            <summary>
            Selecting hue values range.
            </summary>
        </member>
        <member name="T:AForge.Controls.ManipulatorControl">
             <summary>
             Manipulator control.
             </summary>
            
             <remarks>
             <para>The manipulator control can be used to mimic behaviour of analogue joystick using
             regular mouse. By dragging manipulator away from control's centre, it fires <see cref="E:AForge.Controls.ManipulatorControl.PositionChanged"/>
             event notifying about its X/Y coordinates (or about R/Theta coordinates in Polar coordinates system).
             </para>
             
             <para>For example, in robotics applications the control can be used to drive robots. If user drags manipulator
             further from centre (increasing distance between centre and manipulator), then higher power (speed) should be
             set for robot's motors. But dragging it in different directions away from centre should result in changing
             robot's direction: straight forward, backward, turning right or left, etc.<br/>
             <img src="img/controls/manipulator_round.png" width="215" height="215"/>
             </para>
             
             <para>Another possible application of the control is to control position of some device, etc.
             For example, the control could be used with pan-tilt camera - by dragging control away from centre,
             the camera may rotate in one of the directions.<br/>
             <img src="img/controls/manipulator_square.png" width="215" height="215"/>
             </para>
             </remarks>
            
        </member>
        <member name="M:AForge.Controls.ManipulatorControl.Dispose(System.Boolean)">
            <summary> 
            Clean up any resources being used.
            </summary>
            
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:AForge.Controls.ManipulatorControl.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:AForge.Controls.ManipulatorControl.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Controls.ManipulatorControl"/> class.
            </summary>
        </member>
        <member name="P:AForge.Controls.ManipulatorControl.IsSquareLook">
            <summary>
            Determines if the control has square or round look.
            </summary>
            
            <remarks>
            <para>The control has a square look if the property is set to <see langword="true"/>,
            otherwise it has round look.</para>
            
            <para>Default value is set to <see langword="false"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.ManipulatorControl.DrawHorizontalAxis">
            <summary>
            Determines if horizontal axis should be drawn or not.
            </summary>
            
            <remarks>
            <para>Default value is set to <see langword="true"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.ManipulatorControl.DrawVerticalAxis">
            <summary>
            Determines if vertical axis should be drawn or not.
            </summary>
            
            <remarks>
            <para>Default value is set to <see langword="false"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.ManipulatorControl.ResetPositionOnMouseRelease">
            <summary>
            Determines behaviour of manipulator, when mouse button is released.
            </summary>
            
            <remarks>
            <para>The property controls behaviour of manipulator on releasing mouse button. If
            the property is set to <see langword="true"/>, then position of manipulator is reset
            to (0, 0), when mouse button is released. Otherwise manipulator stays on the place,
            where it was left.</para>
            
            <para>Default value is set to <see langword="true"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.ManipulatorControl.BorderColor">
            <summary>
            Color used for drawing borders and axis's.
            </summary>
            
            <remarks>
            <para>Default value is set to <see cref="P:System.Drawing.Color.Black"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.ManipulatorControl.TopLeftBackgroundColor">
            <summary>
            Background color used for filling top left quarter of the control.
            </summary>
            
            <remarks>
            <para>Default value is set to <see cref="P:System.Drawing.Color.White"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.ManipulatorControl.TopRightBackgroundColor">
            <summary>
            Background color used for filling top right quarter of the control.
            </summary>
            
            <remarks>
            <para>Default value is set to <see cref="P:System.Drawing.Color.White"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.ManipulatorControl.BottomLeftBackgroundColor">
            <summary>
            Background color used for filling bottom left quarter of the control.
            </summary>
            
            <remarks>
            <para>Default value is set to <see cref="P:System.Drawing.Color.LightGray"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.ManipulatorControl.BottomRightBackgroundColor">
            <summary>
            Background color used for filling bottom right quarter of the control.
            </summary>
            
            <remarks>
            <para>Default value is set to <see cref="P:System.Drawing.Color.LightGray"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.ManipulatorControl.ManipulatorColor">
            <summary>
            Color used for filling manipulator.
            </summary>
            
            <remarks>
            <para>Default value is set to <see cref="P:System.Drawing.Color.LightSeaGreen"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Controls.ManipulatorControl.ManipulatorPosition">
            <summary>
            Current manipulator's position.
            </summary>
            
            <remarks><para>The property equals to current manipulator's position. Both X and Y values
            are in the [-1, 1] range and represented in
            <a href="http://en.wikipedia.org/wiki/Cartesian_coordinate_system">Cartesian coordinate system</a>.</para>
            </remarks>
            
        </member>
        <member name="E:AForge.Controls.ManipulatorControl.PositionChanged">
            <summary>
            Event used for notification about manipulator's position changes.
            </summary>
        </member>
        <member name="T:AForge.Controls.ManipulatorControl.PositionEventArgs">
             <summary>
             Class, which summarizes arguments of manipulator's position change event.
             </summary>
            
             <remarks><para>Properties of this class allow to get:
             <list type="bullet">
             <item>X/Y coordinates of manipulator in
             <a href="http://en.wikipedia.org/wiki/Cartesian_coordinate_system">Cartesian coordinate system</a>,
             where X axis is directed from center of the control to the right and Y axis is directed from
             center to the top. Both coordinates are measured in [-1, 1] range.</item>
             <item>Theta and R coordinates of manipulator in
             <a href="http://en.wikipedia.org/wiki/Polar_coordinate_system">Polar coordinate system</a>.</item>
             </list>
             </para></remarks>
            
        </member>
        <member name="M:AForge.Controls.ManipulatorControl.PositionEventArgs.#ctor(System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Controls.ManipulatorControl.PositionEventArgs"/> class.
            </summary>
            
            <param name="x">X coordinate of manipulator, [-1, 1].</param>
            <param name="y">Y coordinate of manipulator, [-1, 1].</param>
            
        </member>
        <member name="P:AForge.Controls.ManipulatorControl.PositionEventArgs.X">
            <summary>
            X coordinate of manipulator, [-1, 1].
            </summary>
        </member>
        <member name="P:AForge.Controls.ManipulatorControl.PositionEventArgs.Y">
            <summary>
            Y coordinate of manipulator, [-1, 1].
            </summary>
        </member>
        <member name="P:AForge.Controls.ManipulatorControl.PositionEventArgs.Theta">
            <summary>
            Theta coordinate of manipulator in Polar coordinate system, [0, 359].
            </summary>
        </member>
        <member name="P:AForge.Controls.ManipulatorControl.PositionEventArgs.R">
            <summary>
            R (radius) coordinate of manipulator in Polar coordinate system, [0, 1].
            </summary>
        </member>
        <member name="T:AForge.Controls.ManipulatorControl.PositionChangedHandler">
            <summary>
            Delegate used for notification about manipulator's position changes.
            </summary>
            
            <param name="sender">Event sender - object sending the event.</param>
            <param name="eventArgs">Event arguments - current manipulator's position.</param>
            
        </member>
        <member name="T:AForge.Controls.HistogramEventArgs">
            <summary>
            Arguments of histogram events.
            </summary>
        </member>
        <member name="M:AForge.Controls.HistogramEventArgs.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Controls.HistogramEventArgs"/> class.
            </summary>
            
            <param name="pos">Histogram's index under mouse pointer.</param>
            
        </member>
        <member name="M:AForge.Controls.HistogramEventArgs.#ctor(System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Controls.HistogramEventArgs"/> class.
            </summary>
            
            <param name="min">Min histogram's index in selection.</param>
            <param name="max">Max histogram's index in selection.</param>
            
        </member>
        <member name="P:AForge.Controls.HistogramEventArgs.Min">
            <summary>
            Min histogram's index in selection.
            </summary>
        </member>
        <member name="P:AForge.Controls.HistogramEventArgs.Max">
            <summary>
            Max histogram's index in selection.
            </summary>
        </member>
        <member name="P:AForge.Controls.HistogramEventArgs.Position">
            <summary>
            Histogram's index under mouse pointer.
            </summary>
        </member>
        <member name="T:AForge.Controls.HistogramEventHandler">
            <summary>
            Delegate for histogram events handlers.
            </summary>
            
            <param name="sender">Sender object.</param>
            <param name="e">Event arguments.</param>
            
        </member>
        <member name="T:AForge.Controls.Histogram">
            <summary>
            Histogram control.
            </summary>
            
            <remarks><para>The control displays histograms represented with integer arrays,
            where each array's element keeps occurrence number of the corresponding element.
            </para>
            
            <para>Sample usage:</para>
            <code>
            // create array with histogram values
            int[] histogramValues = new int[] { 3, 8, 53, 57, 79, 69, ... };
            // set values to histogram control
            histogram.Values = histogramValues;
            </code>
            
            <para><b>Sample control's look:</b></para>
            <img src="img/controls/histogram.jpg" width="324" height="77" />
            </remarks>
            
        </member>
        <member name="M:AForge.Controls.Histogram.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Controls.Histogram"/> class.
            </summary>
            
        </member>
        <member name="M:AForge.Controls.Histogram.Dispose(System.Boolean)">
            <summary>
            Dispose the object.
            </summary>
            
            <param name="disposing">Indicates if disposing was initiated manually.</param>
            
        </member>
        <member name="M:AForge.Controls.Histogram.OnPaint(System.Windows.Forms.PaintEventArgs)">
            <summary>
            Paint the control.
            </summary>
            
            <param name="pe">Data for Paint event.</param>
            
        </member>
        <member name="P:AForge.Controls.Histogram.Color">
            <summary>
            Histogram's color.
            </summary>
            
        </member>
        <member name="P:AForge.Controls.Histogram.AllowSelection">
            <summary>
            Allow mouse selection or not.
            </summary>
            
            <remarks>In the case if mouse selection is allowed, the control will
            fire <see cref="E:AForge.Controls.Histogram.SelectionChanged"/> and <see cref="E:AForge.Controls.Histogram.PositionChanged"/> events
            and provide information about the selection.</remarks>
            
        </member>
        <member name="P:AForge.Controls.Histogram.IsLogarithmicView">
            <summary>
            Logarithmic view or not.
            </summary>
            
            <remarks><para>In the case if logarihmic view is selected, then the control
            will display base 10 logarithm of values.</para>
            
            <para>By default the property is set to <b>false</b> - none logarithmic view.</para></remarks>
            
        </member>
        <member name="P:AForge.Controls.Histogram.IsVertical">
             <summary>
             Vertical view or not.
             </summary>
            
             <remarks><para>The property determines if histogram should be displayed vertically or
             not (horizontally).</para>
             
             <para>By default the property is set to <b>false</b> - horizontal view.</para></remarks>
            
        </member>
        <member name="P:AForge.Controls.Histogram.Values">
            <summary>
            Histogram values.
            </summary>
            
            <remarks>Non-negative histogram values.</remarks>
            
            <exception cref="T:System.ArgumentException">Histogram values should be non-negative.</exception>
            
        </member>
        <member name="E:AForge.Controls.Histogram.PositionChanged">
            <summary>
            Mouse position changed event.
            </summary>
            
            <remarks>The event is fired only if the <see cref="P:AForge.Controls.Histogram.AllowSelection"/> property is set
            to true. The passed to event handler <see cref="T:AForge.Controls.HistogramEventArgs"/> class is initialized
            with <see cref="P:AForge.Controls.HistogramEventArgs.Position"/> property only, which is histogram value's
            index pointed by mouse.</remarks>
            
        </member>
        <member name="E:AForge.Controls.Histogram.SelectionChanged">
            <summary>
            Mouse selection changed event.
            </summary>
            
            <remarks>The event is fired only if the <see cref="P:AForge.Controls.Histogram.AllowSelection"/> property is set
            to true. The passed to event handler <see cref="T:AForge.Controls.HistogramEventArgs"/> class is initialized
            with <see cref="P:AForge.Controls.HistogramEventArgs.Min"/> and <see cref="P:AForge.Controls.HistogramEventArgs.Max"/> properties
            only, which represent selection range - min and max indexes.</remarks>
            
        </member>
        <member name="T:AForge.Controls.Chart">
            <summary>
            Chart control.
            </summary>
            
            <remarks><para>The chart control allows to display multiple charts at time
            of different types: dots, lines, connected dots.</para>
            
            <para>Sample usage:</para>
            <code>
            // create data series array
            double[,] testValues = new double[10, 2];
            // fill data series
            for ( int i = 0; i &lt; 10; i++ )
            {
                testValues[i, 0] = i; // X values
                testValues[i, 1] = Math.Sin( i / 18.0 * Math.PI ); // Y values
            }
            // add new data series to the chart
            chart.AddDataSeries( "Test", Color.DarkGreen, Chart.SeriesType.ConnectedDots, 3 );
            // set X range to display
            chart.RangeX = new AForge.Range( 0, 9 );
            // update the chart
            chart.UpdateDataSeries( "Test", testValues );
            </code>
            </remarks>
            
        </member>
        <member name="F:AForge.Controls.Chart.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:AForge.Controls.Chart.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Controls.Chart"/> class.
            </summary>
            
        </member>
        <member name="M:AForge.Controls.Chart.Dispose(System.Boolean)">
            <summary>
            Dispose the object.
            </summary>
        </member>
        <member name="M:AForge.Controls.Chart.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:AForge.Controls.Chart.AddDataSeries(System.String,System.Drawing.Color,AForge.Controls.Chart.SeriesType,System.Int32)">
            <summary>
            Add data series to the chart.
            </summary>
            
            <param name="name">Data series name.</param>
            <param name="color">Data series color.</param>
            <param name="type">Data series type.</param>
            <param name="width">Width (depends on the data series type, see remarks).</param>
            
            <remarks><para>Adds new empty data series to the collection of data series. To update this
            series the <see cref="M:AForge.Controls.Chart.UpdateDataSeries(System.String,System.Double[0:,0:])"/> method should be used.</para>
            
            <para>The meaning of the width parameter depends on the data series type:
            <list type="bullet">
            <item><b>Line</b> - width of the line;</item>
            <item><b>Dots</b> - size of dots (rectangular dots with specified width and the same height);</item>
            <item><b>Connected dots</b> - size of dots (dots are connected with one pixel width line).</item>
            </list>
            </para>
            </remarks>
            
        </member>
        <member name="M:AForge.Controls.Chart.AddDataSeries(System.String,System.Drawing.Color,AForge.Controls.Chart.SeriesType,System.Int32,System.Boolean)">
            <summary>
            Add data series to the chart.
            </summary>
            
            <param name="name">Data series name.</param>
            <param name="color">Data series color.</param>
            <param name="type">Data series type.</param>
            <param name="width">Width (depends on the data series type, see remarks).</param>
            <param name="updateYRange">Specifies if <see cref="P:AForge.Controls.Chart.RangeY"/> should be updated.</param>
            
            <remarks><para>Adds new empty data series to the collection of data series.</para>
            
            <para>The <b>updateYRange</b> parameter specifies if the data series may affect displayable
            Y range. If the value is set to false, then displayable Y range is not updated, but used the
            range, which was specified by user (see <see cref="P:AForge.Controls.Chart.RangeY"/> property). In the case if the
            value is set to true, the displayable Y range is recalculated to fully fit the new data
            series.</para>
            </remarks>
            
        </member>
        <member name="M:AForge.Controls.Chart.UpdateDataSeries(System.String,System.Double[0:,0:])">
            <summary>
            Update data series on the chart.
            </summary>
            
            <param name="name">Data series name to update.</param>
            <param name="data">Data series values.</param>
            
        </member>
        <member name="M:AForge.Controls.Chart.RemoveDataSeries(System.String)">
            <summary>
            Remove data series from the chart.
            </summary>
            
            <param name="name">Data series name to remove.</param>
            
        </member>
        <member name="M:AForge.Controls.Chart.RemoveAllDataSeries">
            <summary>
            Remove all data series from the chart.
            </summary>
        </member>
        <member name="M:AForge.Controls.Chart.UpdateYRange">
            <summary>
            Update Y range.
            </summary>
        </member>
        <member name="P:AForge.Controls.Chart.RangeX">
            <summary>
            Chart's X range.
            </summary>
            
            <remarks><para>The value sets the X range of data to be displayed on the chart.</para></remarks>
            
        </member>
        <member name="P:AForge.Controls.Chart.RangeY">
             <summary>
             Chart's Y range.
             </summary>
             
             <remarks>The value sets the Y range of data to be displayed on the chart.</remarks>
            
        </member>
        <member name="T:AForge.Controls.Chart.SeriesType">
            <summary>
            Chart series type enumeration.
            </summary>
        </member>
        <member name="F:AForge.Controls.Chart.SeriesType.Line">
            <summary>
            Line style.
            </summary>
        </member>
        <member name="F:AForge.Controls.Chart.SeriesType.Dots">
            <summary>
            Dots style.
            </summary>
        </member>
        <member name="F:AForge.Controls.Chart.SeriesType.ConnectedDots">
            <summary>
            Connected dots style.
            </summary>
        </member>
    </members>
</doc>
