﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace eDoktor.Taikoban.FaceClient
{
    class EnvironmentSetting
    {
        public static void Set()
        {
            System.Environment.CurrentDirectory = System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);

            #region ログ設定
            eDoktor.Common.Trace.EnableTrace = false;   // ログファイルのパスを設定するまでにエラーが発生するとログファイルのパスがデフォルトに確定してしまうのを防ぐ
            string outputPath = eDoktor.Common.Configuration.AppSetting("ContainingFolderPath");
            if (!System.IO.Directory.Exists(outputPath))
            {
                try
                {
                    System.IO.Directory.CreateDirectory(outputPath);
                    System.IO.FileAttributes uAttribute = System.IO.File.GetAttributes(outputPath);
                    System.IO.File.SetAttributes(outputPath, uAttribute | System.IO.FileAttributes.Hidden);
                }
                catch
                {
                }
            }
            eDoktor.Common.Trace.EnableTrace = eDoktor.Common.Configuration.AppSetting("EnableTrace", true);
            eDoktor.Common.Trace.EnableDebugTrace = eDoktor.Common.Configuration.AppSetting("EnableDebugTrace", true);
            eDoktor.Common.Trace.EnableDetailedExceptionTrace = eDoktor.Common.Configuration.AppSetting("EnableDetailedExceptionTrace", true);
            eDoktor.Common.Trace.ContainingFolderPath = outputPath;
            eDoktor.Common.Trace.TraceTerm = new TimeSpan(eDoktor.Common.Configuration.AppSetting("TraceTermInDays", 10), 0, 0, 0);
            #endregion
        }
    }
}