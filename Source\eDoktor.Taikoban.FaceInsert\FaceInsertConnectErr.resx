﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="BtnCloses.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAaUAAABYCAYAAABVqejdAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAALEoAACxKAXd6dE0AAAfmSURBVHhe7d3PdaNIFMXhDsEhOASH4ABm4RB6Ozt1Bg6h
        Q5gQOoQOoUPoECaDnnf1gKGKCwIESLZ+75xv4RL658W7pwooffnr729348+fP6/hFL6Hn+FXoCiKorYr
        9VX1V/VZ9dtX149vxQ4eJf4ZL+E96B9EURRF3a7Uh9WPX1y/Pood3JO+cFBC/w4URVHU/ZX6s/r0s+vj
        e7KDe4gv9zUwI6IoivpYpb791fX1PdjBLenLhKWzona9U1PJt6BzTa3DkxsAPqLol1qZ6vdP9VP11fa8
        /ZJSH989nOzgFuLD68vPDaMfQSfcbrqWCQCPRn236b/qw3NKff3NvdYW7OA14sM+hzkJrH/AYVNCAMC0
        6MlP6stNf75U6vObr1zZwbXiAypt/9WnHSk9pqkjS3AAcMfUp5t+famnn9zz17KDS8WHUrpOzY7aMHpy
        zwcA3Cf17aZ/T4WTZlab9Hc7uER8EK1HTp070gk1wggAPjD18aafj5Vy4OrrAuzgXPEBtPY4lp66a5gL
        FwDgE1Ffb/q7K+XBVdcK2ME59Mbnj+Dr3T0HAPA5qM9nu7e1Opjs4CV6w3zfQSkl72ofJQDAPtTvm77v
        alUw2cEpeqN8v0FpOse5IwB4INH3dZXe2HLe4mCyg2P0Bvk+g9KVdwQSADwg9f8mB1wtCiY76MQLa5rm
        6h93PADgsSgPMhYGNfu0jh2sxQtqeubWDQkkAEBHuZDxUJTyY9amCXawL15I0zK3Xsg5JABAQbnQ5ENd
        v9zxNTvYFy/kbpYikAAAlvIhuE0Vvrvj++xgK17AnUfSNIybYgEAo5QTTV7UNXl+yQ5KPHEs6a66WxcA
        8BiUFxkbRSlXRlfa7KDEk9zduj/csQAAOMqNjI+iRnf9sYPxBF1tV5emYZxHAgDMptxo8qMuezXeYEDi
        YHdJH8t2AIDFlB8ZI0XZW4oGA3GgmyX9rI8DAGAu5UjGSVGD2VLxh8RBbpbEJqsAgNWUIxknRQ1mS8Uf
        cYDW/upilgQAuJryJGOlqOJahfoJ7oo7ZkkAgKspTzJWijr1j6mfUN+XNGtbCAAA5lCuZLx09bv/eP9A
        3X1bF1fcAQA2o1zJeCmq2yWof6Db4477kgAAm1GuZLwU1e2J1z+wXrrjZykAAJuLfKl3eeiW8NoD3L1J
        LN0BADanfMmYKep8z9LUASzdAQA2p3zJmCnqPBFqD6hvmOWqOwDAbpQzGTddnU8ZjT148YeYAABYSzmT
        cdPVeTLUPlgX55MAALtRzmTc/F8a1wPu/iR+WRYAsBvlTMZNUS96YLDtg3sBAAC21EROv141WO93V2z5
        AADAHiJv6h//O7lQYldwAMDulDcZO129E0oAgJtQ3mTsdEUoAQBuQ3mTsdPVOZTqPYjY8w4AsLvIG0IJ
        AHAfIm9YvgMA3AflTcZOV4QSAOA2lDcZO10RSgCA21DeZOx0ZUOJm2cBALuLvLE3z7LNEADgcE3k9Ou8
        zRAbsgIADqWcybgp6qV9sK63+gUAANhK5Iz/6YrmQX7kDwBwGOVMxk1XxY/88XPoAIDDKGcybroqfg59
        MI2Keuq/AAAAW1C+ZMwUdf7F8/aA5xwrip9EBwBsTvmSMVPUsx7rH/Q7x7tiDzwAwOYiX+o9V7v7Y/sH
        1SedVCzhAQA2o1zJeCmqu7iuf6C7ZpwlPADAZpQrGS9FdffG1gfXS3hchQcA2IxyJeOlq2Jru/rgeh88
        1Wv/GAAA1lCeZKwUdeofUz/BrfWxazgA4GrKk4yVooprF4onSBxQ30irYrYEAFhNOZJxUtTgKu/iD4mD
        3D1LzJYAAKspRzJOijrfm9RX/NGKA91siSvxAACLKT8yRoqy98IOBiQOdrMl/RgT9y0BAGZTbjT5Uddg
        liSDgVY8wV2J98MdCwCAo9zI+Cjq3R0rdlDiSUq3+r4lFct4AICLlBcZG0UpV0ZX3exgK57orpbQNIxf
        pgUAjFJONHlR1+TV3HawL17A7YmnO3I5vwQAGFA+BLfSdvEHZO1gX7yIXrzeFkJFMAEACsqFJh/qmrVt
        nR2sxYvpajw3DePnLQAAHeVCxkNRyg97tV3NDjrxgu78kopgAgCMBZJq9q5AdnBMvLC7kkKlO3VZygOA
        B6T+3+SAq0VXbNvBKXqDfJ9BcY4JAB5M9H2d3nHnkFSLbyGyg5fojfL9BqV1QzZvBYAHoH7f9H1Xq+5p
        tYNz6A3zfW2N3q0LAPj41Oez3dtavcmCHZxLbxzGUlLTOW6yBYBPRH296e+ulAdX7fpjB5eID6AP6G6S
        aks333KuCQA+MPXxpp+PlXLg6omIHVwqPsjUlRcqpaemeoQTAHwg6ttN/x5bFVNp09VN+rsdXCs+1ClM
        ffA2nGbdRAUAuA316aZfX+rpJ/f8tezgNeID6otMzZraUrJetfYIANhO9GTNinStgPu5ibrU5zefYNjB
        LcSHfQtT55r6pX+AZllcGAEAB1LfbfrvnCBSqa+/udfagh3cUnx4pe7ccGpLCawTapo6Ktx0LXyLpT8A
        mCH6pQKn3z/VT9VX1V/nrGj1S31899UtO7gHfZmw9J9AURRF3bbUtw871WIH9xRfTsmtlF46e6IoiqKO
        KfVn9enDV6bs4FHiCyugNJVkBkVRFHXbUh9WP77puX07eCvxz9Cap064teudY3cNUxRFUetKfVX9VX1W
        /faO9iv99uU/vHwmd5xnchEAAAAASUVORK5CYII=
</value>
  </data>
  <data name="BtnConnect.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAaUAAABYCAYAAABVqejdAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAALEoAACxKAXd6dE0AAAfmSURBVHhe7d3PdaNIFMXhDsEhOASH4ABm4RB6Ozt1Bg6h
        Q5gQOoQOoUPoECaDnnf1gKGKCwIESLZ+75xv4RL658W7pwooffnr729348+fP6/hFL6Hn+FXoCiKorYr
        9VX1V/VZ9dtX149vxQ4eJf4ZL+E96B9EURRF3a7Uh9WPX1y/Pood3JO+cFBC/w4URVHU/ZX6s/r0s+vj
        e7KDe4gv9zUwI6IoivpYpb791fX1PdjBLenLhKWzona9U1PJt6BzTa3DkxsAPqLol1qZ6vdP9VP11fa8
        /ZJSH989nOzgFuLD68vPDaMfQSfcbrqWCQCPRn236b/qw3NKff3NvdYW7OA14sM+hzkJrH/AYVNCAMC0
        6MlP6stNf75U6vObr1zZwbXiAypt/9WnHSk9pqkjS3AAcMfUp5t+famnn9zz17KDS8WHUrpOzY7aMHpy
        zwcA3Cf17aZ/T4WTZlab9Hc7uER8EK1HTp070gk1wggAPjD18aafj5Vy4OrrAuzgXPEBtPY4lp66a5gL
        FwDgE1Ffb/q7K+XBVdcK2ME59Mbnj+Dr3T0HAPA5qM9nu7e1Opjs4CV6w3zfQSkl72ofJQDAPtTvm77v
        alUw2cEpeqN8v0FpOse5IwB4INH3dZXe2HLe4mCyg2P0Bvk+g9KVdwQSADwg9f8mB1wtCiY76MQLa5rm
        6h93PADgsSgPMhYGNfu0jh2sxQtqeubWDQkkAEBHuZDxUJTyY9amCXawL15I0zK3Xsg5JABAQbnQ5ENd
        v9zxNTvYFy/kbpYikAAAlvIhuE0Vvrvj++xgK17AnUfSNIybYgEAo5QTTV7UNXl+yQ5KPHEs6a66WxcA
        8BiUFxkbRSlXRlfa7KDEk9zduj/csQAAOMqNjI+iRnf9sYPxBF1tV5emYZxHAgDMptxo8qMuezXeYEDi
        YHdJH8t2AIDFlB8ZI0XZW4oGA3GgmyX9rI8DAGAu5UjGSVGD2VLxh8RBbpbEJqsAgNWUIxknRQ1mS8Uf
        cYDW/upilgQAuJryJGOlqOJahfoJ7oo7ZkkAgKspTzJWijr1j6mfUN+XNGtbCAAA5lCuZLx09bv/eP9A
        3X1bF1fcAQA2o1zJeCmq2yWof6Db4477kgAAm1GuZLwU1e2J1z+wXrrjZykAAJuLfKl3eeiW8NoD3L1J
        LN0BADanfMmYKep8z9LUASzdAQA2p3zJmCnqPBFqD6hvmOWqOwDAbpQzGTddnU8ZjT148YeYAABYSzmT
        cdPVeTLUPlgX55MAALtRzmTc/F8a1wPu/iR+WRYAsBvlTMZNUS96YLDtg3sBAAC21EROv141WO93V2z5
        AADAHiJv6h//O7lQYldwAMDulDcZO129E0oAgJtQ3mTsdEUoAQBuQ3mTsdPVOZTqPYjY8w4AsLvIG0IJ
        AHAfIm9YvgMA3AflTcZOV4QSAOA2lDcZO10RSgCA21DeZOx0ZUOJm2cBALuLvLE3z7LNEADgcE3k9Ou8
        zRAbsgIADqWcybgp6qV9sK63+gUAANhK5Iz/6YrmQX7kDwBwGOVMxk1XxY/88XPoAIDDKGcybroqfg59
        MI2Keuq/AAAAW1C+ZMwUdf7F8/aA5xwrip9EBwBsTvmSMVPUsx7rH/Q7x7tiDzwAwOYiX+o9V7v7Y/sH
        1SedVCzhAQA2o1zJeCmqu7iuf6C7ZpwlPADAZpQrGS9FdffG1gfXS3hchQcA2IxyJeOlq2Jru/rgeh88
        1Wv/GAAA1lCeZKwUdeofUz/BrfWxazgA4GrKk4yVooprF4onSBxQ30irYrYEAFhNOZJxUtTgKu/iD4mD
        3D1LzJYAAKspRzJOijrfm9RX/NGKA91siSvxAACLKT8yRoqy98IOBiQOdrMl/RgT9y0BAGZTbjT5Uddg
        liSDgVY8wV2J98MdCwCAo9zI+Cjq3R0rdlDiSUq3+r4lFct4AICLlBcZG0UpV0ZX3exgK57orpbQNIxf
        pgUAjFJONHlR1+TV3HawL17A7YmnO3I5vwQAGFA+BLfSdvEHZO1gX7yIXrzeFkJFMAEACsqFJh/qmrVt
        nR2sxYvpajw3DePnLQAAHeVCxkNRyg97tV3NDjrxgu78kopgAgCMBZJq9q5AdnBMvLC7kkKlO3VZygOA
        B6T+3+SAq0VXbNvBKXqDfJ9BcY4JAB5M9H2d3nHnkFSLbyGyg5fojfL9BqV1QzZvBYAHoH7f9H1Xq+5p
        tYNz6A3zfW2N3q0LAPj41Oez3dtavcmCHZxLbxzGUlLTOW6yBYBPRH296e+ulAdX7fpjB5eID6AP6G6S
        aks333KuCQA+MPXxpp+PlXLg6omIHVwqPsjUlRcqpaemeoQTAHwg6ttN/x5bFVNp09VN+rsdXCs+1ClM
        ffA2nGbdRAUAuA316aZfX+rpJ/f8tezgNeID6otMzZraUrJetfYIANhO9GTNinStgPu5ibrU5zefYNjB
        LcSHfQtT55r6pX+AZllcGAEAB1LfbfrvnCBSqa+/udfagh3cUnx4pe7ccGpLCawTapo6Ktx0LXyLpT8A
        mCH6pQKn3z/VT9VX1V/nrGj1S31899UtO7gHfZmw9J9AURRF3bbUtw871WIH9xRfTsmtlF46e6IoiqKO
        KfVn9enDV6bs4FHiCyugNJVkBkVRFHXbUh9WP77puX07eCvxz9Cap064teudY3cNUxRFUetKfVX9VX1W
        /faO9iv99uU/vHwmd5xnchEAAAAASUVORK5CYII=
</value>
  </data>
</root>