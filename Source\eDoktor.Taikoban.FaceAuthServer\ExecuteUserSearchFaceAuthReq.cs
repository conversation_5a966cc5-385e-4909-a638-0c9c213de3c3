﻿#define LICENCE_ENABLED

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSDK;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using eDoktor.Taikoban.FaceInterprocess;
using GFRL.FaceRecognition;
using GFRL.FaceSearch;
using GFRL.FaceTemplate;

namespace eDoktor.Taikoban.FaceAuthServer
{
    /// <summary>
    /// ユーザ検索顔認証要求
    /// </summary>
    public class ExecuteUserSearchFaceAuthReq
    {
        /// <summary>
        /// データベース
        /// </summary>
        private DB _db;

        /// <summary>
        /// 設定情報
        /// </summary>
        private SettingsInfo _settingInfo;

        /// <summary>
        /// 認証結果　
        /// </summary>
        private EnumAuthResult _authResult = EnumAuthResult.AuthNG;

        /// <summary>
        /// ユーザ情報
        /// </summary>
        private LoginUserInfo _loginUserInfo;

        /// <summary>
        /// 顔サーチ結果
        /// </summary>
        private FaceSearchResult _faceSearchResult;

        /// <summary>
        /// 顔認証結果
        /// </summary>
        private FaceRetrieveResult _faceRetrieveResult;

        /// <summary>
        /// テンプレートリスト
        /// </summary>
        private List<FaceTemplateDataRegister> _templateDataRegList;

        /// <summary>
        /// ログ内容
        /// </summary>
        private string _logContents = string.Empty;

        /// <summary>
        /// 認証日時
        /// </summary>
        private readonly DateTime _authDateTime = DateTime.Now;

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="database">データベース</param> 
        /// <param name="settingInfo">設定情報</param>
        /// <param name="templateDataRegList">テンプレートリスト</param>
        public ExecuteUserSearchFaceAuthReq(Database database, SettingsInfo settingInfo, List<FaceTemplateDataRegister> templateDataRegList)
        {
            this._db = new DB(database);
            this._settingInfo = settingInfo;
            this._templateDataRegList = templateDataRegList;
            this._loginUserInfo = new LoginUserInfo();
        }

        /// <summary>
        /// ユーザ検索顔認証要求コマンドに対する受信処理です
        /// </summary>
        /// <param name="packet">受信コマンドの内容</param>
        public XElement Execute(FaceInterprocess.Packet packet)
        {
            try
            {
                if (this._settingInfo.auth_mode == EnumAuthMode.Client)
                {
                    // ① 設定．認証モードがクライアントモードの場合、「モード違い」で「異常終了」とする。
                    this._logContents = Messages.Message(MessageId.Message011);
                    Trace.OutputErrorTrace(this._logContents);

                    return this.SetFailElem(EnumErrorId.DifferentMode);
                }

                var isCorrectData = this.GetRequestData(packet, out UserSearchFaceAuthReqData requestData);

                if (isCorrectData == false)
                {
                    this._logContents = "ExecuteCmdQueryUserSearchFaceAuthReq：受信データが不正です。フォーマットエラーを返します。";
                    Trace.OutputErrorTrace(this._logContents);

                    return this.SetFailElem(EnumErrorId.InputDataErr);
                }

#if LICENCE_ENABLED

                // ② 顔認証SDKの顔サーチ処理、サムネイル取得処理
                var sdkFace = new SDKFaceAuth(this._settingInfo);

                // 画像データから顔検出を行い、検出結果から、サムネイル画像を取得する
                this._faceSearchResult = sdkFace.GetThumbnail(requestData.Image);

                if (this._faceSearchResult == null)
                {
                    // 顔サーチ結果がNGの場合、「顔サーチNG」で「異常終了」とする。
                    this._logContents = Messages.Message(MessageId.Message012);
                    Trace.OutputErrorTrace(this._logContents);

                    return this.SetFailElem(EnumErrorId.FaceSearchNG);
                }

                // ③ 顔テンプレート取得処理
                // b. 設定．テンプレートデータ保持有無が「1」の場合、3.テンプレートデータの保持更新処理の(1)で用意しているテンプレートデータを取得する。
                if (this._templateDataRegList.Count <= 0)
                {
                    // 4.共通処理の(2)全職員のテンプレートデータ取得処理
                    var isGetTemplate = this._db.GetAllTemplateData(out this._templateDataRegList);

                    if (isGetTemplate == false)
                    {
                        Trace.OutputDebugTrace("顔テンプレート取得失敗");
                        this._logContents = Messages.Message(MessageId.Message002);

                        return this.SetFailElem(EnumErrorId.DbErr);
                    }

                    if (this._templateDataRegList.Count <= 0)
                    {
                        // 顔テンプレートが取得できなかった場合、「顔テンプレート取得失敗」で「異常終了」とする。
                        this._logContents = Messages.Message(MessageId.Message013);
                        return this.SetFailElem(EnumErrorId.FaceTemplateGetFail);
                    }
                }

                Trace.OutputDebugTrace("顔テンプレート取得成功");

                // ④ ③で取得した顔テンプレートと認証するサムネイルから顔認証SDKで照合を行う。
                var errId = sdkFace.FaceAuth(this._faceSearchResult.FaceThumbnail, this._templateDataRegList, out this._faceRetrieveResult);

                if (errId != EnumErrorId.NoErr)
                {
                    this._logContents = Messages.Message(MessageId.Message001);
                    return this.SetFailElem(errId);
                }

                if (this._faceRetrieveResult.FaceIdentificationResult.HasIdentified == false)
                {
                    // 照合結果が失敗の場合、「認証失敗」で「正常終了」とする。
                    this._logContents = "認証失敗";
                    Trace.OutputDebugTrace(this._logContents);
                    this._authResult = EnumAuthResult.AuthNG;
                    return this.SetSuccessElem();
                }

                this._logContents = "認証成功";
                Trace.OutputDebugTrace(this._logContents);
                this._authResult = EnumAuthResult.AuthOK;


                // ⑤ 顔が一致したユーザーの情報を取得する。
                this._loginUserInfo = this._db.GetUserInfo(this._faceRetrieveResult.FaceIdentificationResult.IdentifiedTemplateID.UserID, out errId);

                if (errId != EnumErrorId.NoErr)
                {
                    // ユーザ取得失敗の場合、「認証失敗」で「正常終了」とする。
                    this._logContents = "ユーザー情報取得失敗(認証成功)";
                    Trace.OutputErrorTrace("ユーザー情報取得失敗");
                    this._authResult = EnumAuthResult.AuthNG;

                    this.SetFailElem(errId);
                }

                Trace.OutputDebugTrace("ユーザー情報取得成功");

#endif // LICENCE_ENABLED

                return this.SetSuccessElem();
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return this.SetFailElem(EnumErrorId.Exception);
            }
        }

        /// <summary>
        /// ログ登録
        /// </summary>
        /// <param name="ipAddress">送信元IPアドレス</param>
        public void RegistLog(string ipAddress)
        {
            Trace.OutputDebugTrace("ログ登録");

            if (this._faceRetrieveResult != null)
            {
                // 認証成功かつユーザの取得ができている場合、成功として登録
                _db.RegistAuthLog(this._authResult == EnumAuthResult.AuthOK, ipAddress, this._authDateTime, this._logContents, this._settingInfo.auth_mode, false, this._faceRetrieveResult, this._faceSearchResult.FaceThumbnail);
            }
            else
            {
                _db.RegistNgAuthLog(ipAddress, 0, this._authDateTime, this._logContents, this._settingInfo.auth_mode, false, this._faceSearchResult);
            }
        }

        /// <summary>
        /// 顔学習
        /// </summary>
        public void UpdateFace()
        {
            // 認証成功時
            if (this._authResult == EnumAuthResult.AuthOK)
            {
                // 顔学習処理を行う。
                var faceLearn = new FaceLearnClass();
                faceLearn.FaceLearn(this._faceRetrieveResult, this._settingInfo, this._loginUserInfo.account_id, this._db);
            }
        }

        /// <summary>
        /// リクエストデータ取得
        /// </summary>
        /// <param name="packet">受信コマンドの内容</param>
        /// <param name="requestData">リクエストデータ</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        private bool GetRequestData(FaceInterprocess.Packet packet, out UserSearchFaceAuthReqData requestData)
        {
            requestData = new UserSearchFaceAuthReqData();

            try
            {
                requestData.ToObject(packet.Data);

                return true;
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return false;
            }
        }

        /// <summary>
        /// 成功時応答データ作成
        /// </summary>
        /// <returns></returns>
        private XElement SetSuccessElem()
        {
            var elementData = new ElementDataCreate();
            elementData.CreateElem(nameof(UserSearchFaceAuthResData.ProcResult), (int)EnumProcResult.Success);
            elementData.CreateElem(nameof(UserSearchFaceAuthResData.ErrorId), (int)EnumErrorId.NoErr);
            elementData.CreateElem(nameof(UserSearchFaceAuthResData.AuthResult), (int)this._authResult);
            elementData.CreateElem(nameof(UserSearchFaceAuthResData.LogonId), this._loginUserInfo.logon_id);
            elementData.CreateElem(nameof(UserSearchFaceAuthResData.UserName), this._loginUserInfo.name);
            elementData.CreateElem(nameof(UserSearchFaceAuthResData.AccountId), this._loginUserInfo.account_id);
            elementData.CreateElem(nameof(UserSearchFaceAuthResData.UserId), this._loginUserInfo.user_id);

            Trace.OutputDebugTrace(elementData.StrElements);

            return elementData.Elements;
        }

        /// <summary>
        /// 失敗時応答データ作成
        /// </summary>
        /// <param name="errorId">エラーID</param>
        /// <returns></returns>
        private XElement SetFailElem(EnumErrorId errorId)
        {
            var elementData = new ElementDataCreate();
            elementData.CreateElem(nameof(UserSearchFaceAuthResData.ProcResult), (int)EnumProcResult.Fail);
            elementData.CreateElem(nameof(UserSearchFaceAuthResData.ErrorId), (int)errorId);
            elementData.CreateElem(nameof(UserSearchFaceAuthResData.AuthResult), (int)EnumAuthResult.AuthNG);
            elementData.CreateElem(nameof(UserSearchFaceAuthResData.LogonId), string.Empty);
            elementData.CreateElem(nameof(UserSearchFaceAuthResData.UserName), string.Empty);
            elementData.CreateElem(nameof(UserSearchFaceAuthResData.AccountId), string.Empty);
            elementData.CreateElem(nameof(UserSearchFaceAuthResData.UserId), string.Empty);

            Trace.OutputDebugTrace(elementData.StrElements);

            return elementData.Elements;
        }
    }
}
