﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using eDoktor.Taikoban.FaceInterprocess;

namespace eDoktor.Taikoban.FaceAuthServer
{
    /// <summary>
    /// 認証ログ登録(クライアント認証モード)要求
    /// </summary>
    public class ExecuteFaceAuthLogRegistReq
    {
        /// <summary>
        /// データベース
        /// </summary>
        DB _db;

        /// <summary>
        /// 設定情報
        /// </summary>
        SettingsInfo _settingInfo;

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="database">データベース</param>
        /// <param name="settingInfo">設定情報</param>
        public ExecuteFaceAuthLogRegistReq(Database database, SettingsInfo settingInfo)
        {
            this._db = new DB(database);
            this._settingInfo = settingInfo;
        }

        /// <summary>
        /// 認証ログ登録(クライアント認証モード)要求コマンドに対する受信処理です  ※ 未実装 ※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
        /// </summary>
        /// <param name="packet">受信コマンドの内容</param>
        public XElement Execute(FaceInterprocess.Packet packet)
        {
            try
            {
                // TODO クライアントモードの処理

                if (this._settingInfo.auth_mode == EnumAuthMode.Server)
                {
                    // ① 設定．認証モードがサーバモードの場合、「モード違い」で「異常終了」とする。
                    Trace.OutputErrorTrace(Messages.Message(MessageId.Message011));

                    return this.SetFailElem(EnumErrorId.DifferentMode);
                }

                var isCorrectData = this.GetRequestData(packet, out FaceTemplateAddUpdateReqData requestData);

                if (isCorrectData == false)
                {
                    Trace.OutputErrorTrace("ExecuteCmdQueryFaceAuthLogRegistReq：受信データが不正です。フォーマットエラーを返します。");

                    return this.SetFailElem(EnumErrorId.InputDataErr);
                }

                return this.SetSuccessElem();
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return this.SetFailElem(EnumErrorId.Exception);
            }
        }

        /// <summary>
        /// リクエストデータ取得
        /// </summary>
        /// <param name="packet">受信コマンドの内容</param>
        /// <param name="requestData">リクエストデータ</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        private bool GetRequestData(eDoktor.Taikoban.FaceInterprocess.Packet packet, out FaceTemplateAddUpdateReqData requestData)
        {
            requestData = new FaceTemplateAddUpdateReqData();

            try
            {
                requestData.ToObject(packet.Data);

                return true;
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return false;
            }
        }

        /// <summary>
        /// 成功時応答データ作成
        /// </summary>
        /// <returns></returns>
        private XElement SetSuccessElem()
        {
            var elementData = new ElementDataCreate();

            Trace.OutputDebugTrace(elementData.StrElements);

            return elementData.Elements;
        }

        /// <summary>
        /// 失敗時応答データ作成
        /// </summary>
        /// <param name="errorId">エラーID</param>
        /// <returns></returns>
        private XElement SetFailElem(EnumErrorId errorId)
        {
            var elementData = new ElementDataCreate();

            Trace.OutputDebugTrace(elementData.StrElements);

            return elementData.Elements;
        }
    }
}
