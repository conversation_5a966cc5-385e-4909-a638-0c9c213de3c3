﻿#define LICENCE_ENABLED

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using eDoktor.Taikoban.FaceInterprocess;
using GFRL.FaceRecognition;
using GFRL.FaceTemplate;

namespace eDoktor.Taikoban.FaceAuthServer
{
    /// <summary>
    /// 本人確認・顔テンプレート登録確認要求
    /// </summary>
    public class ExecuteIdentificationFaceTemplateRegConfirmReq
    {
        /// <summary>
        /// データベース
        /// </summary>
        private DB _db;

        /// <summary>
        /// 設定情報
        /// </summary>
        private SettingsInfo _settingInfo;

        /// <summary>
        /// ユーザ情報
        /// </summary>
        private LoginUserInfo _loginUserInfo;

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="database">データベース</param> 
        /// <param name="settingInfo">設定情報</param>
        public ExecuteIdentificationFaceTemplateRegConfirmReq(Database database, SettingsInfo settingInfo)
        {
            this._db = new DB(database);
            this._settingInfo = settingInfo;
        }

        /// <summary>
        /// 本人確認・顔テンプレート登録確認要求コマンドに対する受信処理です
        /// </summary>
        /// <param name="packet">受信コマンドの内容</param>
        public XElement Execute(FaceInterprocess.Packet packet)
        {
            try
            {
                if (this._settingInfo.auth_mode == EnumAuthMode.Client)
                {
                    // ① 設定．認証モードがクライアントモードの場合、「モード違い」で「異常終了」とする。
                    Trace.OutputErrorTrace(Messages.Message(MessageId.Message011));

                    return this.SetFailElem(EnumErrorId.DifferentMode);
                }

                var isCorrectData = this.GetRequestData(packet, out IdentificationFaceTemplateRegConfirmReqData requestData);

                if (isCorrectData == false)
                {
                    Trace.OutputErrorTrace("ExecuteCmdQueryIdentificationFaceTemplateRegConfirmReq：受信データが不正です。フォーマットエラーを返します。");

                    return this.SetFailElem(EnumErrorId.InputDataErr);
                }

                // 指定の職員IDが存在するかチェック
                var ret = this.GetUserInfo(requestData.LogonId, requestData.Password, out XElement xElement);

                if (ret == false)
                {
                    return xElement;
                }

                // 登録職員あり
                Trace.OutputDebugTrace("アカウント情報あり");
                return this.CheckTemplateRegistState();
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return this.SetFailElem(EnumErrorId.Exception);
            }
        }

        /// <summary>
        /// リクエストデータ取得
        /// </summary>
        /// <param name="packet">受信コマンドの内容</param>
        /// <param name="requestData">リクエストデータ</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        private bool GetRequestData(FaceInterprocess.Packet packet, out IdentificationFaceTemplateRegConfirmReqData requestData)
        {
            requestData = new IdentificationFaceTemplateRegConfirmReqData();

            try
            {
                requestData.ToObject(packet.Data);

                return true;
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return false;
            }
        }

        /// <summary>
        /// 成功時応答データ作成
        /// </summary>
        /// <param name="registState">本人確認結果</param>
        /// <param name="registState">登録状態</param>
        /// <returns></returns>
        private XElement SetSuccessElem(EnumIdentificationResult identificationResult, EnumRegistState registState)
        {
            var elementData = new ElementDataCreate();
            elementData.CreateElem(nameof(IdentificationFaceTemplateRegConfirmResData.ProcResult), (int)EnumProcResult.Success);
            elementData.CreateElem(nameof(IdentificationFaceTemplateRegConfirmResData.ErrorId), (int)EnumErrorId.NoErr);
            elementData.CreateElem(nameof(IdentificationFaceTemplateRegConfirmResData.IdentificationResult), (int)identificationResult);
            elementData.CreateElem(nameof(IdentificationFaceTemplateRegConfirmResData.RegistState), (int)registState);
            elementData.CreateElem(nameof(IdentificationFaceTemplateRegConfirmResData.AccountId), this._loginUserInfo.account_id);
            elementData.CreateElem(nameof(IdentificationFaceTemplateRegConfirmResData.UserName), this._loginUserInfo.name);

            Trace.OutputDebugTrace(elementData.StrElements);

            return elementData.Elements;
        }

        /// <summary>
        /// 失敗時応答データ作成
        /// </summary>
        /// <param name="errorId">エラーID</param>
        /// <returns></returns>
        private XElement SetFailElem(EnumErrorId errorId)
        {
            var elementData = new ElementDataCreate();
            elementData.CreateElem(nameof(IdentificationFaceTemplateRegConfirmResData.ProcResult), (int)EnumProcResult.Fail);
            elementData.CreateElem(nameof(IdentificationFaceTemplateRegConfirmResData.ErrorId), (int)errorId);
            elementData.CreateElem(nameof(IdentificationFaceTemplateRegConfirmResData.IdentificationResult), (int)EnumIdentificationResult.AuthNG);
            elementData.CreateElem(nameof(IdentificationFaceTemplateRegConfirmResData.RegistState), (int)EnumRegistState.NoRegist);
            elementData.CreateElem(nameof(IdentificationFaceTemplateRegConfirmResData.AccountId), string.Empty);
            elementData.CreateElem(nameof(IdentificationFaceTemplateRegConfirmResData.UserName), string.Empty);

            Trace.OutputDebugTrace(elementData.StrElements);

            return elementData.Elements;
        }

        /// <summary>
        /// ユーザ情報取得
        /// </summary>
        /// <param name="logonId">ログオンID</param>
        /// <param name="password">パスワード</param>
        /// <param name="resultElem"></param>
        /// <returns></returns>
        private bool GetUserInfo(string logonId, string password, out XElement resultElem)
        {
            resultElem = null;
            var result = true;

            // パスワードの暗号化有無をチェックし、有の場合は、暗号化する
            string cooperativePassword = null;
            bool cooperativePasswordEncryptionEnabled = eDoktor.Common.Configuration.AppSetting("CooperativePasswordEncryptionEnabled", false);
            bool passwordEncrypt = eDoktor.Common.Configuration.AppSetting("PasswordEncryption", false);

            if (cooperativePasswordEncryptionEnabled)
            {
                cooperativePassword = ExBSecurity.MakeSecretCode(password);
                Trace.OutputDebugTrace("パスワード富士通暗号化");

            }
            else if (passwordEncrypt)
            {
                password = eDoktor.Taikoban.FaceCrypt.Crypto.Encrypt(password);
                Trace.OutputDebugTrace("パスワード暗号化");
            }

            // 指定の職員IDが存在するかチェック
            this._loginUserInfo = this._db.GetUserInfo(logonId, password, cooperativePassword, out EnumErrorId errId, out EnumIdentificationResult identificationResult);

            if (errId != EnumErrorId.NoErr)
            {
                // 異常終了時
                resultElem = this.SetFailElem(errId);
                result = false;
            }
            else if (identificationResult == EnumIdentificationResult.AuthNG)
            {
                // 該当するユーザ情報がない場合
                Trace.OutputDebugTrace("該当職員なしもしくは、パスワードの入力ミス");
                resultElem = this.SetSuccessElem(identificationResult, EnumRegistState.NoRegist);
                result = false;
            }

            return result;
        }

        /// <summary>
        /// 対象ユーザのテンプレート登録状態を確認
        /// </summary>
        /// <returns></returns>
        private XElement CheckTemplateRegistState()
        {
            // 対象のレコードが存在するかチェックする
            var isGetTemplate = this._db.GetTemplateData(this._loginUserInfo.account_id, out List<TemplateInfo> templateDataInfoList);

            if (isGetTemplate == false)
            {
                return this.SetFailElem(EnumErrorId.DbErr);
            }

            var registState = EnumRegistState.NoRegist;

            if (templateDataInfoList.Count >= this._settingInfo.face_template_regist_max_count)
            {
                // 取得したテンプレートリストの数が、設定．顔テンプレート登録上限に達している場合、「登録あり(上限)」で「正常終了」とする。
                Trace.OutputDebugTrace("登録あり（上限）");
                registState = EnumRegistState.RegistMax;
            }
            else if (templateDataInfoList.Count > 0)

            {
                // 取得したテンプレートリストの数が、設定．顔テンプレート登録上限より少ない場合、「登録あり」で「正常終了」とする。
                Trace.OutputDebugTrace("登録あり");
                registState = EnumRegistState.Regist;
            }
            else
            {
                // 顔テンプレートが登録されていない場合、「登録なし」で「正常終了」とする。
                Trace.OutputDebugTrace("登録なし");
                registState = EnumRegistState.NoRegist;
            }

            return this.SetSuccessElem(EnumIdentificationResult.AuthOK, registState);
        }
    }
}
