﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{267CB3E5-5208-4351-B666-ADD746542ACE}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>eDoktor.Taikoban.FaceAuthServer</RootNamespace>
    <AssemblyName>eDoktor.Taikoban.FaceAuthServer</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>..\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>..\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="eDoktor.Common, Version=1.0.0.0, Culture=neutral, PublicKeyToken=0ff7bf58c14a5b51, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\eDoktor.Taikoban.AppExtension\eDoktor.Common.dll</HintPath>
    </Reference>
    <Reference Include="GFRL, Version=2.2.8.0, Culture=neutral, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Library\GFRL.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DB.cs" />
    <Compile Include="ExBSecurity.cs" />
    <Compile Include="ExecuteFaceAuthLogRegistReq.cs" />
    <Compile Include="ExecuteLivenessReq.cs" />
    <Compile Include="ExecuteUserSearchFaceAuthReq.cs" />
    <Compile Include="ExecuteIdentificationFaceTemplateRegConfirmReq.cs" />
    <Compile Include="ExecuteFaceTemplateAddUpdateReq.cs" />
    <Compile Include="ExecuteFaceTemplateRegClientReq.cs" />
    <Compile Include="ExecuteFaceTemplateGetReq.cs" />
    <Compile Include="ExecuteFaceSettingGetReq.cs" />
    <Compile Include="ExecuteFaceAuthtReq.cs" />
    <Compile Include="ExecuteFaceTemplateRegConfirmReq.cs" />
    <Compile Include="ExecuteFaceTemplateDelReq.cs" />
    <Compile Include="ExecuteFaceTemplateRegServerReq.cs" />
    <Compile Include="FaceLearnClass.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Server.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ServerCommonProc.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\eDoktor.Taikoban.FaceAuthCommon\eDoktor.Taikoban.FaceAuthCommon.csproj">
      <Project>{7A2D3A65-364E-43AD-93D9-9C1057E9C4B9}</Project>
      <Name>eDoktor.Taikoban.FaceAuthCommon</Name>
    </ProjectReference>
    <ProjectReference Include="..\eDoktor.Taikoban.FaceAuthSDK\eDoktor.Taikoban.FaceAuthSDK.csproj">
      <Project>{92d59348-6c32-4c23-b019-1b3db2715d00}</Project>
      <Name>eDoktor.Taikoban.FaceAuthSDK</Name>
    </ProjectReference>
    <ProjectReference Include="..\eDoktor.Taikoban.FaceAuthTemplateUpdateJudge\eDoktor.Taikoban.FaceAuthTemplateUpdateJudge.csproj">
      <Project>{9a08b0fa-5509-4b51-804e-56f7355689a4}</Project>
      <Name>eDoktor.Taikoban.FaceAuthTemplateUpdateJudge</Name>
    </ProjectReference>
    <ProjectReference Include="..\eDoktor.Taikoban.FaceCrypt\eDoktor.Taikoban.FaceCrypt.csproj">
      <Project>{c540424b-0963-465a-a8bb-568f4309e0fa}</Project>
      <Name>eDoktor.Taikoban.FaceCrypt</Name>
    </ProjectReference>
    <ProjectReference Include="..\eDoktor.Taikoban.FaceInterprocess\eDoktor.Taikoban.FaceInterprocess.csproj">
      <Project>{6eb7d3c9-23e9-4fab-83db-67272caa8a0e}</Project>
      <Name>eDoktor.Taikoban.FaceInterprocess</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>