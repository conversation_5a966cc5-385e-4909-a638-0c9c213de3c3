﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace eDoktor.Taikoban.FaceInsert
{
    /// <summary>
    /// 一括登録処理結果格納クラス
    /// </summary>
    public class BulkRegistResult
    {
        /// <summary>
        /// 行番号
        /// </summary>
        public int RowNo { get; set; }

        /// <summary>
        /// 職員ID
        /// </summary>
        public string LogonId { get; set; }

        /// <summary>
        /// 職員名
        /// </summary>
        public string LogonName { get; set; }

        /// <summary>
        /// 処理結果
        /// </summary>
        public bool IsResult { get; set; }

        /// <summary>
        /// 処理結果(表示用)
        /// </summary>
        public string Result { get { return this.IsResult ? "成功" : "失敗"; } }

        /// <summary>
        /// エラー内容
        /// </summary>
        public string ErrorMessage { get; set; }
    }
}
