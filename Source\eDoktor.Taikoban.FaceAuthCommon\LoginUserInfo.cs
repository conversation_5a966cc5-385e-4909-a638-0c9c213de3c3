﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace eDoktor.Taikoban.FaceAuthCommon
{
    /// <summary>
    /// ユーザ情報
    /// </summary>
    public class LoginUserInfo
    {
        /// <summary>
        /// コンストラクタ
        /// </summary>
        public LoginUserInfo()
        {
            this.account_id = 0;
            this.user_id = 0;
            this.logon_id = string.Empty;
            this.name = string.Empty;
        }

        /// <summary>
        /// アカウントID
        /// </summary>
        public int account_id { get; set; }

        /// <summary>
        /// ユーザID
        /// </summary>
        public int user_id { get; set; }

        /// <summary>
        /// ログオンID
        /// </summary>
        public string logon_id { get; set; }

        /// <summary>
        /// ユーザ名
        /// </summary>
        public string name { get; set; }
    }
}
