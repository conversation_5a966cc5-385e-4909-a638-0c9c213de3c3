﻿using System;
using System.Collections.Generic;
using System.Linq;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using GFRL;
using GFRL.FaceRecognition;
using GFRL.FaceSearch;
using GFRL.FaceTemplate;

namespace eDoktor.Taikoban.FaceAuthSDK
{
    /// <summary>
    /// 顔認証処理クラス
    /// </summary>
    public class SDKFaceAuth
    {
        /// <summary>
        /// 設定情報
        /// </summary>
        private SettingsInfo _settingInfo;

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="settingInfo">設定情報</param>
        public SDKFaceAuth(SettingsInfo settingInfo)
        {
            this._settingInfo = settingInfo;
        }

        #region 顔認証処理

        /// <summary>
        /// 顔認証処理
        /// </summary>
        /// <param name="thumbnailData">サムネイルデータ</param>
        /// <param name="templateInfoList">認証テンプレートリスト</param>
        /// <param name="faceRetrieveResult">顔認証結果</param>
        /// <returns>エラーID</returns>
        public EnumErrorId FaceAuth(byte[] thumbnailData, List<FaceTemplateDataRegister> templateInfoList, out FaceRetrieveResult faceRetrieveResult)
        {
            FaceRetrieve faceRetriever = null;
            var errId = EnumErrorId.NoErr;
            faceRetrieveResult = null;

            try
            {
                // テンプレートグループ登録
                var templateGroup = this.RegistTemplateGroup(templateInfoList);

                if (templateGroup == null)
                {
                    return EnumErrorId.FaceTemplateGroupRegFail;
                }

                // 初期化済インスタンス取得
                faceRetriever = RetrieverEngineObjectManager.Instance.GetInstance(this._settingInfo);

                if (faceRetriever != null)
                {
                    Trace.OutputTrace("顔照合処理(サムネイル画像入力)");
                    // 顔認証SDKで①のテンプレートグループと引数のテンプレートの照合を行う。
                    var stopwatch = new System.Diagnostics.Stopwatch();
                    stopwatch.Restart();
                    faceRetrieveResult = faceRetriever.RetrieveFaceThumbnail(thumbnailData, templateGroup);
                    stopwatch.Stop();

                    // スコアリストのログを1件のみ出力する為、顔照合結果はそれぞれ分散して出力する。
                    Trace.OutputDebugTrace(faceRetrieveResult.FaceInfo.ToString());
                    Trace.OutputDebugTrace(faceRetrieveResult.FaceIdentificationResult.ToString());
                    Trace.OutputDebugTrace(faceRetrieveResult.ScoreList.FirstOrDefault()?.ToString());

                    Trace.OutputDebugTrace($"   @顔照合処理時間：{stopwatch.ElapsedMilliseconds} [msec]");

                    // 顔照合スコアが認証しきい値以上の顔照合結果の場合、テンプレートIDを取得する
                    if (faceRetrieveResult.FaceIdentificationResult.HasIdentified)
                    {
                        Trace.OutputTrace("照合成功 : {0}", faceRetrieveResult.FaceIdentificationResult.IdentifiedTemplateID);
                    }
                    else
                    {
                        Trace.OutputErrorTrace("照合失敗");
                    }
                }
                else
                {
                    Trace.OutputErrorTrace("初期化済みインスタンスの取得に失敗しました。");
                }
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                // 処理失敗
                errId = EnumErrorId.Exception;
            }
            finally
            {
                if (faceRetriever != null)
                {
                    // 使用インスタンス開放
                    RetrieverEngineObjectManager.Instance.ReleaseInstance(faceRetriever);
                }
            }

            return errId;
        }

        #endregion

        #region 顔サーチ処理

        /// <summary>
        /// 画像データから顔検出を行い、検出結果から、サムネイル画像を取得する
        /// </summary>
        /// <param name="imageData">入力画像</param>
        /// <returns>顔サーチ結果</returns>
        public FaceSearchResult GetThumbnail(byte[] imageData)
        {
            FaceSearch searcherEngine = null;
            FaceSearchResult faceSearchResult = null;

            try
            {
                var inputImageData = this.CreateImageDataInstance(imageData);

                if (inputImageData != null)
                {
                    // 初期化済インスタンス取得
                    searcherEngine = SearcherEngineObjectManager.Instance.GetInstance(this._settingInfo);

                    if (searcherEngine != null)
                    {
                        Trace.OutputTrace("顔サーチ処理を実行する。");

                        //  テンプレート用画像の顔サーチ処理
                        var searchResult = searcherEngine.SearchFace(inputImageData);

                        Trace.OutputDebugTrace(searchResult.ToString());

                        // 顔サーチ結果を返す
                        faceSearchResult = searchResult.SearchResult.FirstOrDefault();
                    }
                    else
                    {
                        Trace.OutputErrorTrace("SearcherEngineObjectManager:初期化済みインスタンスの取得に失敗しました。");
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
            finally
            {
                if (searcherEngine != null)
                {
                    // 使用インスタンス開放
                    SearcherEngineObjectManager.Instance.ReleaseInstance(searcherEngine);
                }
            }

            return faceSearchResult;
        }

        #endregion

        #region テンプレート作成

        /// <summary>
        /// テンプレート作成
        /// </summary>
        /// <param name="thumbnail">サムネイル</param>
        /// <returns></returns>
        public FaceFeatureProperty TemplateCreate(byte[] thumbnail)
        {
            FaceFeatureProperty faceFeature = null;
            FaceTemplateCreate faceTemplateCreate = null;

            try
            {
                // 初期化済インスタンス取得
                faceTemplateCreate = CreatorEngineObjectManager.Instance.GetInstance(this._settingInfo);

                if (faceTemplateCreate != null)
                {
                    Trace.OutputTrace("テンプレート生成（サムネイル画像入力）");

                    //  テンプレート作成処理
                    var templateResult = faceTemplateCreate.CreateTemplateThumbnail(thumbnail);

                    Trace.OutputDebugTrace(templateResult.ToString());
                    faceFeature = templateResult.FaceInfo.FaceFeature;
                }
                else
                {
                    Trace.OutputErrorTrace("初期化済みインスタンスの取得に失敗しました。");
                }

            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
            finally
            {
                if (faceTemplateCreate != null)
                {
                    // 使用インスタンス開放
                    CreatorEngineObjectManager.Instance.ReleaseInstance(faceTemplateCreate);
                }
            }

            return faceFeature;
        }

        #endregion

        #region テンプレートグループ登録

        /// <summary>
        /// テンプレートグループ登録
        /// </summary>
        /// <param name="templateInfoList"></param>
        /// <returns></returns>
        private FaceTemplateGroup RegistTemplateGroup(List<FaceTemplateDataRegister> templateInfoList)
        {
            try
            {
                //  テンプレートグループクラスの初期化
                var templateGroup = new FaceTemplateGroup();
                templateGroup.Initialize(this._settingInfo.face_recog_data_type); // 照合タイプ
                Trace.OutputDebugTrace("FaceRecogDataType : {0}", this._settingInfo.face_recog_data_type);

                //  8.テンプレート登録
                templateGroup.RegisterTemplate(templateInfoList);

                Trace.OutputTrace("テンプレート登録完了");
                return templateGroup;
            }
            catch (Exception ex)
            {
                // テンプレートグループ登録失敗
                Trace.OutputExceptionTrace(ex);
                return null;
            }
        }

        #endregion

        #region 入力画像のインスタンスを生成する

        /// <summary>
        /// 入力画像のインスタンスを生成する
        /// </summary>
        /// <param name="imageData"></param>
        /// <returns></returns>
        private ImageData CreateImageDataInstance(byte[] imageData)
        {
            try
            {
                Trace.OutputTrace("入力画像のインスタンスを生成");
                return new ImageData(imageData, true);
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return null;
            }
        }

        #endregion
    }
}
