﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSettingsInfo;

namespace eDoktor.Taikoban.FaceAuthServer
{
    /// <summary>
    /// サーバー共通処理
    /// </summary>
    public class ServerCommonProc
    {
        // ▼ ADD 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
        /// <summary>
        /// マスク着用と判断するためのマスクスコア閾値（この値以上でマスクあり）
        /// </summary>
        private static readonly float _maskScoreThresholdForWearingMask = eDoktor.Common.Configuration.AppSetting("MaskScoreThresholdForWearingMask", 50);
        // ▲ ADD 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara

        /// <summary>
        /// 顔登録時テンプレートID取得
        /// </summary>
        /// <param name="registMaxCount">テンプレート最大登録数</param>
        /// <param name="templateDataInfoList">現在登録しているテンプレートのリスト</param>
        /// <param name="templateId">登録時に設定するテンプレートID</param>
        /// <returns>処理結果</returns>
        // ▼ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
        //public static bool GetInsertTemplteId(int registMaxCount, List<TemplateInfo> templateDataInfoList, out int templateId)
        public static bool GetInsertTemplteId(int registMaxCount, List<TemplateInfo> templateDataInfoList, float maskScore, out int templateId)
        // ▲ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
        {
            // ▼ ADD 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
            bool withMask = maskScore >= _maskScoreThresholdForWearingMask;
            // ▲ ADD 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara

            if (templateDataInfoList.Count == 0)
            {
                // ▼ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
                //// 1件も登録されていない場合は「1」
                //templateId = 1;
                // 1件も登録されていない場合はマスクなしなら「1」、マスクありなら「101」
                templateId = (withMask) ? 101 : 1;
                // ▲ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
                return true;
            }

            templateId = 0;
            var isResult = false;

            // ▼ ADD 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
            int start = (withMask) ? 101 : 1;
            int maxCount = (withMask) ? 100 + registMaxCount : registMaxCount;
            // ▲ ADD 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara

            // ▼ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
            //for (int i = 1; i <= registMaxCount; i++)
            for (int i = start; i <= maxCount; i++)
            // ▲ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
            {
                if (!templateDataInfoList.Exists(x => x.template_id == i))
                {
                    // 登録最大値までの間で登録のないテンプレートID
                    templateId = i;
                    isResult = true;
                    break;
                }
            }

            return isResult;
        }
    }
}
