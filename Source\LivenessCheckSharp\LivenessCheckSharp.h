﻿#pragma once

#include "LivenessCheck.hpp"
#include <msclr/marshal_cppstd.h>
#include <opencv2/opencv.hpp>
#include <opencv2/imgproc/imgproc_c.h>

#using <system.drawing.dll>

using namespace System;
using namespace System::Drawing;
using namespace msclr::interop;
using namespace cv;
using namespace System::Runtime::InteropServices;

namespace LivenessCheckSharp {


    /// <summary>
    /// ファイルパス格納用構造体
    /// </summary>
    public value struct ExternalFilePathStruct {
    public:
        System::String^ faceDetectModel;
        System::String^ faceDetectParam;
        System::String^ fakeModelH;
        System::String^ fakeModelV;
        System::String^ faceDirHModel;
        System::String^ faceDirVModel;
        System::String^ glassesModel;
        System::String^ maskModel;
        System::String^ eyeDetectModel;
        System::String^ eyeStatusModel;
        System::String^ eyeDirModel;
    };

    /// <summary>
    /// なりすまし判定SDK設定値格納用構造体
    /// </summary>
    public value struct ParamsStruct {
    public:
        int minFaceWidth;
        int maxFaceWidth;
        float faceAreaMinRatio;
        float faceAreaMaxRatio;
        bool edgePosErrMode;
        bool isFakeMode;
        float fakeJudgeTh;
        bool isFaceDirMode;
        bool isGlassesMode;
        bool isMaskMode;
        float maskJudgeTh;
        bool isEyeMode;
        bool isEyeDirMode;
    };

    /// <summary>
    /// 生体判定結果構造体
    /// </summary>
    public value struct BioDataStruct {
    public:
        //基本機能項目
        float faceBrightness;
        bool isValidFace;
        System::String^ msg;

        //フェイク判定
        bool isFakeFace;
        float isFakeLikelihood;

        //顔向き
        short faceDirStatusH;
        short faceDirStatusV;

        //メガネ
        short eyeGlassStatus;

        //マスク
        bool maskStatus;
        float isMaskLikelihood;

        //目の位置
        float eyeRectAvgBrightLeft;
        float eyeRectAvgBrightRight;
        short eyeStatusLeft;
        short eyeStatusRight;

        //目の向き
        short eyeDirStatusLeftH;
        short eyeDirStatusRightH;

        //検出できた顔の数
        int faceCount;
        //顔のエリアが空かどうか
        bool isFaceAreaEmpty;

        //SDKの生体検知結果（BioData）をクリアする
        void bioDataClearWrapper();
    };

    // なりすまし判定SDKラッパークラス
    public ref class LivenessCheckSharpClass
    {
    public:
        /// <summary>
        /// コンストラクタ
        /// </summary>
        LivenessCheckSharpClass();
        ~LivenessCheckSharpClass();
        !LivenessCheckSharpClass();

        /// <summary>
        /// 初期化
        /// </summary>
        bool InitWrapper(ExternalFilePathStruct externalFilePathStruct, ParamsStruct paramsStruct);

        /// <summary>
        /// 生体判定結果取得準備
        /// </summary>
        bool RegisterBioDataWrapper();

        /// <summary>
        /// なりすまし判定処理
        /// </summary>
        bool ProcessWrapper(Bitmap^ bitmap);

        /// <summary>
        /// 生体判定結果取得
        /// </summary>
        void outputBioData(BioDataStruct% bioDataStruct);

        /// <summary>
        /// トークンチェック1
        /// </summary>
        bool checkTokenWrapper(System::String^ token);

        /// <summary>
        /// トークンチェック2
        /// </summary>
        bool validTokenWrapper(System::String^ token);

        /// <summary>
        /// トークン有効期限取得
        /// </summary>
        long getExpDateWrapper(System::String^ token);

        /// <summary>
        /// activationChallengeオブジェクト解放
        /// </summary>
        void releaseActivationChallengeWrapper();

        /// <summary>
        /// livenessCheckオブジェクト解放
        /// </summary>
        void releaseLivenessCheckWrapper();

    private:
        /// <summary>
        /// 判別する画像を格納するMat変数
        /// </summary>
        cv::Mat* mat = new Mat();

        /// <summary>
        /// LivenessCheckオブジェクト
        /// </summary>
        sw::livenesscheck::LivenessCheck* livenessCheck;

        /// <summary>
        /// ActivationChallengeオブジェクト
        /// </summary>
        sw::livenesscheck::ActivationChallenge* activationChallenge;

        /// <summary>
        /// BitmapをMatに変換する
        /// </summary>
        void BitmapToMat(Bitmap^ img, bool toGray);

        /// <summary>
        /// ファイルパスセット
        /// </summary>
        void SetExternalFilePath(ExternalFilePathStruct efp);

        /// <summary>
        /// 設定値セット
        /// </summary>
        void SetParams(ParamsStruct params);
    };

}
