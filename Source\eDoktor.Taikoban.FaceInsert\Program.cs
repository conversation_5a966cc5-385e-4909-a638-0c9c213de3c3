﻿#define DEBUG_MODE // デバッグしたい場合はこちらを有効にすること

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace eDoktor.Taikoban.FaceInsert
{
    static class Program
    {
        /// <summary>
        /// アプリケーションのメイン エントリ ポイントです。
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                EnvironmentSetting.Set();
                eDoktor.Common.Trace.OutputTrace("顔認証テンプレート登録・削除画面開始");
                string errmsg = string.Empty;

                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.Run(new FaceInsertMenu());
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
            finally
            {
                eDoktor.Common.Trace.OutputTrace("顔認証テンプレート登録・削除画面終了");
            }
        }
    }
}
