// Configuration.cs

namespace eDoktor.Taikoban.FaceInterprocess
{
	public static class Configuration
	{
		#region Fields
		private static readonly int DefaultResponseWaitingTimeout = 2000;
        private static readonly int DefaultFaceServerPortNumber = 49601;
        private static readonly int DefaultFaceServerBacklog = 100;
        private static readonly int DefaultFaceServerMaxAcceptableClientCount = 2500;
        private static readonly int DefaultFaceServerKeepAliveTime = 60000;
        private static int _responseWaitingTimeout;
        private static string[] _FaceServerAddressList;
        private static int _FaceServerPortNumber;
        private static int _FaceServerBacklog;
        private static int _FaceServerMaxAcceptableClientCount;
        private static int _FaceServerKeepAliveTime;
		#endregion

		#region Properties
		public static int ResponseWaitingTimeout
		{
            get { return _responseWaitingTimeout; }
		}

        public static string[] FaceServerAddressList
        {
            get { return _FaceServerAddressList; }
        }

        public static int FaceServerPortNumber
        {
            get { return _FaceServerPortNumber; }
        }

        public static int FaceServerBacklog
        {
            get { return _FaceServerBacklog; }
        }

        public static int FaceServerMaxAcceptableClientCount
        {
            get { return _FaceServerMaxAcceptableClientCount; }
        }

        public static int FaceServerKeepAliveTime
        {
            get { return _FaceServerKeepAliveTime; }
        }
		#endregion

		#region Constructors
		static Configuration()
		{
			try
			{
                _responseWaitingTimeout = eDoktor.Common.Configuration.AppSetting("ResponseWaitingTimeout", DefaultResponseWaitingTimeout);
                _FaceServerAddressList = eDoktor.Common.Configuration.AppSettingArray("FaceServerAddressList", eDoktor.Common.Configuration.DefaultSeparator, System.StringSplitOptions.RemoveEmptyEntries);
                _FaceServerPortNumber = eDoktor.Common.Configuration.AppSetting("FaceServerPortNumber", DefaultFaceServerPortNumber);
                _FaceServerBacklog = eDoktor.Common.Configuration.AppSetting("FaceServerBacklog", DefaultFaceServerBacklog);
                _FaceServerMaxAcceptableClientCount = eDoktor.Common.Configuration.AppSetting("FaceServerMaxAcceptableClientCount", DefaultFaceServerMaxAcceptableClientCount);
                _FaceServerKeepAliveTime = eDoktor.Common.Configuration.AppSetting("FaceServerKeepAliveTime", DefaultFaceServerKeepAliveTime);
			}
			catch (System.Exception ex)
			{
				eDoktor.Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion
	}
}
