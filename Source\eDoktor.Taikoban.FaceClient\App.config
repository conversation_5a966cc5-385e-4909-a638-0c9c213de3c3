<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2"/>
    </startup>
	<appSettings>
		<!-- Trace -->
		<add key="EnableTrace" value="true"/>
		<add key="EnableDebugTrace" value="true"/>
		<add key="EnableDetailedExceptionTrace" value="true"/>
		<add key="ContainingFolderPath" value=".\logs\"/>
		<add key="TraceTermInDays" value="30"/>
		<add key="StaffId" value="ytani"/>
		
		<!-- カメラ画像反転フラグ(true時反転) -->
		<add key="IsFlipHorizon" value="true"/>
		<!-- 顔登録がない場合に、登録画面に遷移する（true：顔登録、false：終了） -->
		<add key="IsTransitionRegist" value="true"/>
		<!-- 同意確認画面を表示するか false=表示しない true=表示する -->
		<add key="ViewConsentScreen" value="false"/>
		<add key="DisplayMessageImageFilePath" value="D:\git\Taikoban\Taikoban-FaceAuth-GlorySDK\Source\eDoktor.Taikoban.FaceClient\image\Face_Guide3.png"/>
		<!-- 顔登録完了時の応答値 1=顔認証に遷移 2=初期画面に戻る -->
		<add key="ReturnValueWhenFaceRegisterd" value="0"/>
		<!-- ▼ ADD 認証タイムアウト時のアプリ終了対応 2024/01/26 Whizz Y.Tani -->
		<!-- タイムアウト後のアプリ終了時刻(ミリ秒) -->
		<add key="TimeOutAppCloseTimer" value="10000"/>
		<!-- ▲ ADD 認証タイムアウト時のアプリ終了対応 2024/01/26 Whizz Y.Tani -->
		
		<add key="MessageFaceDetected" value="顔を検出しました"/>
		<add key="MessageMultiFacesDetected" value="複数の顔を顔を検出しました"/>
		<add key="MessageFaceRecognitionFailed" value="顔認証に失敗しました。リトライします"/>
		<add key="MessageLivenessInvalidFace" value="顔の位置を調整してください"/>
		<add key="MessageLivenessCheckErrored" value="顔認証ができませんでした"/>
	</appSettings>
	<runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
