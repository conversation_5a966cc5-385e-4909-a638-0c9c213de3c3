﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using GFRL.FaceRecognition;

namespace eDoktor.Taikoban.FaceAuthTemplateUpdateJudge
{
    /// <summary>
    /// テンプレート更新有無判定
    /// </summary>
    public class TemplateUpdateJudge
    {
        /// <summary>
        /// コンストラクタ
        /// </summary>
        public TemplateUpdateJudge()
        {
        }

        /// <summary>
        /// テンプレート評価値とマスク装着度の閾値チェックを行う。(共通処理G)
        /// </summary>
        /// <param name="templateQuality">テンプレート評価値</param>
        /// <param name="msakScore">マスク装着度</param>
        /// <param name="settingsInfo">設定情報</param>
        /// <returns>true:処理対象 false:処理対象外</returns>
        public bool TemplateThresholdCheck(float templateQuality, float msakScore, SettingsInfo settingsInfo)
        {
            bool ret = true;

            if ((templateQuality < settingsInfo.template_quality_threshold_insert) || msakScore > settingsInfo.mask_score_threshold)
            {
                Trace.OutputErrorTrace("テンプレート評価値もしくは、マスク装着度が設定値の基準オーバーです。");
                Trace.OutputErrorTrace("templateQuality({0}) < settingsInfo.template_quality_threshold_insert({1})", templateQuality, settingsInfo.template_quality_threshold_insert);
                Trace.OutputErrorTrace("      msakScore({0}) > settingsInfo.mask_score_threshold({1})", msakScore, settingsInfo.mask_score_threshold);
                ret = false;
            }

            return ret;
        }

        /// <summary>
        /// 顔テンプレートアップデート対象取得処理を行い、更新対象のテンプレートIDを取得する。(共通処理F)
        /// </summary>
        /// <param name="templateQuality">テンプレート評価値</param>
        /// <param name="templateInfoList">t_face_auth_templates テーブルの情報</param>
        /// <returns>true:取得成功 false:取得失敗</returns>
        public int FaceTemplateUpdateJudge(float templateQuality, List<TemplateInfo> templateInfoList)
        {
            // 最初に登録したレコードの情報を取得する
            DateTime dateTime = templateInfoList[0].modification_datetime;
            int templateId = templateInfoList[0].template_id;

            // テンプレート評価値、顔照合スコア、更新日時などから判定を行う。更新日時が古いものを優先順位低とする。
            for (int i = 0; i < templateInfoList.Count; i++)
            {
                // 前回登録した日時(左辺)のほうが比較したレコード(右辺)より新しい場合、そのレコードが一番古い日時のレコードとみなし、更新対象とする。
                if (dateTime > templateInfoList[i].modification_datetime)
                {
                    dateTime = templateInfoList[i].modification_datetime;
                    templateId = templateInfoList[i].template_id;
                }
            }

            // 対象職員の優先順位が低い顔テンプレートを更新対象として、テンプレートIDを返す。
            return templateId;
        }
    }
}
