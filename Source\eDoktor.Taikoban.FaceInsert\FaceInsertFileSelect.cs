﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using eDoktorTaikoban.FaceClientCommon;

namespace eDoktor.Taikoban.FaceInsert
{
    /// <summary>
    /// ファイル選択画面
    /// </summary>
    public partial class FaceInsertFileSelect : Form
    {
        #region Private Const

        /// <summary>
        /// ファイル選択ダイアログの表示拡張子フィルタ
        /// </summary>
        private const string FILE_EXTENSION_FILTER = "Image File(*.bmp,*.jpg,*.png,*.tif)|*.bmp;*.jpg;*.png;*.tif|Bitmap(*.bmp)|*.bmp|Jpeg(*.jpg)|*.jpg|PNG(*.png)|*.png";

        #endregion

        #region Private Fields

        /// <summary>
        /// 要求送信クラス
        /// </summary>
        RequestSendClass _requestSend = new RequestSendClass();

        /// <summary>
        /// 設定情報
        /// </summary>
        private SettingsInfo _settingInfo;

        #endregion

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="settingInfo">設定情報</param>
        public FaceInsertFileSelect(SettingsInfo settingInfo)
        {
            InitializeComponent();
            this._settingInfo = settingInfo;
        }

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="requestSend">要求送信クラス</param>
        /// <param name="settingInfo">設定情報</param>
        public FaceInsertFileSelect(RequestSendClass requestSend, SettingsInfo settingInfo)
            :this(settingInfo)
        {
            this._requestSend = requestSend;
        }

        /// <summary>
        /// 選択ボタン押下処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnSelect_Click(object sender, EventArgs e)
        {
            //ファイルを開くダイアログボックスの作成  
            var ofd = new OpenFileDialog();

            ofd.InitialDirectory = eDoktor.Common.Configuration.AppSetting("ImageInputFilePath");

            //ファイルフィルタ  
            ofd.Filter = FILE_EXTENSION_FILTER;

            //ダイアログの表示 （Cancelボタンがクリックされた場合は何もしない）
            if (ofd.ShowDialog() != DialogResult.Cancel)
            {
                // ファイルパスをテキストボックスに表示する
                txtFilePath.Text = ofd.FileName;
            }
        }

        /// <summary>
        /// ドロップ操作完了時
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FileSelect_DragDrop(object sender, DragEventArgs e)
        {
            txtFilePath.Text = null;
            string[] files = (string[])e.Data.GetData(DataFormats.FileDrop, false);

            var file = files.FirstOrDefault();

            if (file == null ||
                (!(file.EndsWith(".bmp", StringComparison.OrdinalIgnoreCase)) &&
                 !(file.EndsWith(".jpg", StringComparison.OrdinalIgnoreCase)) &&
                 !(file.EndsWith(".png", StringComparison.OrdinalIgnoreCase)) &&
                 !(file.EndsWith(".tif", StringComparison.OrdinalIgnoreCase))))
            {
                // 画像ファイルではありません。
                MessageBox.Show(Messages.Message(MessageId.Message021));
                return;
            }

            txtFilePath.Text = file;
        }

        /// <summary>
        /// アイテムドラッグ時
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FileSelect_DragEnter(object sender, DragEventArgs e)
        {
            e.Effect = DragDropEffects.All;
        }

        private bool CheckExtension(string filePath)
        {
            // 拡張子の確認
            var extension = Path.GetExtension(filePath).ToLower();

            // ファイルの拡張子が対応しているファイルかどうか調べる
            if ((extension != ".bmp") && (extension != ".jpg") && (extension != ".png") && (extension != ".tif"))
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 登録ボタン押下処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnEnrollment_Click(object sender, EventArgs e)
        {
            try
            {
                // 職員IDが未入力の場合
                if (string.IsNullOrEmpty(this.txtStaffId.Text))
                {
                    // 職員IDを入力してください。
                    MessageBox.Show(Messages.Message(MessageId.Message022));
                    return;
                }

                // ファイルが未選択の場合
                if (string.IsNullOrEmpty(this.txtFilePath.Text) || !File.Exists(this.txtFilePath.Text))
                {
                    // 画像ファイルを選択してください。
                    MessageBox.Show(Messages.Message(MessageId.Message023));
                    return;
                }

                if (this.CheckExtension(this.txtFilePath.Text) == false)
                {
                    // 画像ファイルではありません。
                    MessageBox.Show(Messages.Message(MessageId.Message021));
                    return;
                }

                // 画像の読み込み ※送信電文に乗せるための生データを保持しておく
                var imageData = File.ReadAllBytes(this.txtFilePath.Text);

                // 顔テンプレート登録確認要求を送信する
                var res = this._requestSend.RequestFaceTemplateRegistCheck(txtStaffId.Text, out FaceInterprocess.FaceTemplateRegConfirmResData resData);

                if (!res)
                {
                    // サーバーからの応答がありません。
                    Trace.OutputErrorTrace(Messages.Message(MessageId.Message004));
                    return;
                }

                // 処理結果が正常終了
                if (resData.ProcResult == EnumProcResult.Success)
                {
                    // 登録状態が「登録あり」もしくは、「登録なし」の場合
                    if (resData.RegistState == EnumRegistState.Regist || resData.RegistState == EnumRegistState.NoRegist)
                    {
                        // 登録確認画面遷移
                        using (FaceInsertEnrollment fileEnrollment = new FaceInsertEnrollment(resData.AccountId, this.txtStaffId.Text, resData.UserName, imageData,this._settingInfo))
                        {
                            this.Hide();
                            fileEnrollment.ShowDialog(this);
                            this.Show();
                        }
                    }
                    else if (resData.RegistState == EnumRegistState.RegistMax)
                    {
                        // 登録状態が「上限」の場合
                        MessageBox.Show(Messages.Message(MessageId.Message009));
                    }
                    else
                    {
                        // 入力データ不正
                        MessageBox.Show(Messages.Message(MessageId.Message003));
                    }
                }
                else
                {
                    // 処理結果が異常終了
                    var errMsg = ClientCommonProc.ErrorIdToErrorMessage(resData.ErrorId, this.txtStaffId.Text);

                    if (!string.IsNullOrEmpty(errMsg))
                    {
                        MessageBox.Show(errMsg);
                    }
                }
            }
            catch (KeyNotFoundException ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 戻るボタン押下処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnReturn_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// マウスカーソルがボタン領域に入った時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseEnter(object sender, EventArgs e)
        {
            try
            {
                Button btn = (Button)sender;
                btn.BackgroundImage = eDoktor.Taikoban.FaceInsert.Properties.Resources.btn_2;
                btn.ForeColor = System.Drawing.Color.FromArgb(24, 109, 178);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }

        }

        /// <summary>
        /// マウスカーソルがボタン領域から抜けた時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseLeave(object sender, EventArgs e)
        {
            try
            {
                Button btn = (Button)sender;
                btn.BackgroundImage = eDoktor.Taikoban.FaceInsert.Properties.Resources.btn_1;
                btn.ForeColor = System.Drawing.SystemColors.Window;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// ボタン押下時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            try
            {
                Button btn = (Button)sender;
                btn.BackgroundImage = eDoktor.Taikoban.FaceInsert.Properties.Resources.btn_3;
                btn.ForeColor = System.Drawing.SystemColors.Window;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }
    }
}
