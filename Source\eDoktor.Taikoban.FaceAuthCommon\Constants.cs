﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace eDoktor.Taikoban.FaceAuthCommon
{
    /// <summary>
   /// メッセージID
   /// </summary>
    public static class MessageId
    {
        /// <summary>
        /// 処理に失敗しました。
        /// </summary>
        public static readonly string Message001 = "001";
        /// <summary>
        /// DBでエラーが発生しました。
        /// </summary>
        public static readonly string Message002 = "002";
        /// <summary>
        /// データが不正です。
        /// </summary>
        public static readonly string Message003 = "003";
        /// <summary>
        /// サーバからの応答がありませんでした。
        /// </summary>
        public static readonly string Message004 = "004";
        /// <summary>
        /// 顔認証ライセンスが不正です。
        /// </summary>
        public static readonly string Message005 = "005";
        /// <summary>
        /// 初期化に失敗しました。
        /// </summary>
        public static readonly string Message006 = "006";
        /// <summary>
        /// 使用できるカメラがありません。
        /// </summary>
        public static readonly string Message007 = "007";
        /// <summary>
        /// 対象職員(%AccountId%)の顔情報が登録されていません。
        /// </summary>
        public static readonly string Message008 = "008";
        /// <summary>
        /// 登録数上限です。
        /// </summary>
        public static readonly string Message009 = "009";
        /// <summary>
        /// 対象職員(%AccountId%)が存在しません。
        /// </summary>
        public static readonly string Message010 = "010";
        /// <summary>
        /// モードの設定が不正です。
        /// </summary>
        public static readonly string Message011 = "011";
        /// <summary>
        /// 顔が認識できませんでした。
        /// </summary>
        public static readonly string Message012 = "012";
        /// <summary>
        /// 顔情報の取得に失敗しました。
        /// </summary>
        public static readonly string Message013 = "013";
        /// <summary>
        /// 顔認証の処理に失敗しました。
        /// </summary>
        public static readonly string Message014 = "014";
        /// <summary>
        /// 顔情報の作成に失敗しました。
        /// </summary>
        public static readonly string Message015 = "015";
        /// <summary>
        /// IDもしくはパスワードが間違っています。
        /// </summary>
        public static readonly string Message016 = "016";
        /// <summary>
        /// 顔認証に失敗しました。
        /// </summary>
        public static readonly string Message017 = "017";
        /// <summary>
        /// 登録が完了しました。職員ID：%AccountId% 職員名：%UserName%
        /// </summary>
        public static readonly string Message018 = "018";
        /// <summary>
        /// 削除が完了しました。職員ID：%AccountId% 職員名：%UserName%
        /// </summary>
        public static readonly string Message019 = "019";
        /// <summary>
        /// 登録に失敗しました。職員ID：%AccountId% 職員名：%UserName%
        /// </summary>
        public static readonly string Message020 = "020";
        /// <summary>
        /// 画像ファイルではありません。
        /// </summary>
        public static readonly string Message021 = "021";
        /// <summary>
        /// 職員IDを入力してください。
        /// </summary>
        public static readonly string Message022 = "022";
        /// <summary>
        /// 画像ファイルを選択してください。
        /// </summary>
        public static readonly string Message023 = "023";
        /// <summary>
        /// ログインIDを入力してください。
        /// </summary>
        public static readonly string Message024 = "024";
        /// <summary>
        /// パスワードを入力してください。
        /// </summary>
        public static readonly string Message025 = "025";
        /// <summary>
        /// CSVファイルではありません。
        /// </summary>
        public static readonly string Message026 = "026";
        /// <summary>
        /// CSVファイルを選択してください。
        /// </summary>
        public static readonly string Message027 = "027";
        /// <summary>
        /// 出力するデータがありません。
        /// </summary>
        public static readonly string Message028 = "028";
        /// <summary>
        /// ファイルが開かれています。
        /// </summary>
        public static readonly string Message029 = "029";
        /// <summary>
        /// このユーザでログインします。
        /// </summary>
        public static readonly string Message030 = "030";
        /// <summary>
        /// なりすまし画像です
        /// </summary>
        public static readonly string Message031 = "031";
        /// <summary>
        /// トークンファイルを確認してください
        /// </summary>
        public static readonly string Message032 = "032";
    }

}
