﻿namespace eDoktor.Taikoban.FaceDetection
{
    partial class MessageDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.error_message_text = new System.Windows.Forms.Label();
            this.picOK = new System.Windows.Forms.PictureBox();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            ((System.ComponentModel.ISupportInitialize)(this.picOK)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            this.SuspendLayout();
            // 
            // error_message_text
            // 
            this.error_message_text.AutoSize = true;
            this.error_message_text.Font = new System.Drawing.Font("BIZ UDPゴシック", 15.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
            this.error_message_text.ForeColor = System.Drawing.SystemColors.Window;
            this.error_message_text.Location = new System.Drawing.Point(20, 150);
            this.error_message_text.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.error_message_text.Name = "error_message_text";
            this.error_message_text.Size = new System.Drawing.Size(0, 21);
            this.error_message_text.TabIndex = 0;
            // 
            // picOK
            // 
            this.picOK.BackColor = System.Drawing.Color.Transparent;
            this.picOK.Image = global::eDoktor.Taikoban.FaceDetection.Properties.Resources.liveness_ok_off;
            this.picOK.Location = new System.Drawing.Point(30, 220);
            this.picOK.Name = "picOK";
            this.picOK.Size = new System.Drawing.Size(180, 40);
            this.picOK.TabIndex = 3;
            this.picOK.TabStop = false;
            this.picOK.Click += new System.EventHandler(this.ok_button_Click);
            this.picOK.MouseDown += new System.Windows.Forms.MouseEventHandler(this.picOK_MouseDown);
            this.picOK.MouseEnter += new System.EventHandler(this.picOK_MouseEnter);
            this.picOK.MouseLeave += new System.EventHandler(this.picOK_MouseLeave);
            this.picOK.MouseUp += new System.Windows.Forms.MouseEventHandler(this.picOK_MouseUp);
            // 
            // pictureBox1
            // 
            this.pictureBox1.Image = global::eDoktor.Taikoban.FaceDetection.Properties.Resources.Vector_Layer_3;
            this.pictureBox1.Location = new System.Drawing.Point(65, 12);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(110, 110);
            this.pictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
            this.pictureBox1.TabIndex = 2;
            this.pictureBox1.TabStop = false;
            // 
            // MessageDialog
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(187)))), ((int)(((byte)(93)))), ((int)(((byte)(93)))));
            this.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.ClientSize = new System.Drawing.Size(240, 278);
            this.Controls.Add(this.picOK);
            this.Controls.Add(this.pictureBox1);
            this.Controls.Add(this.error_message_text);
            this.DoubleBuffered = true;
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.Margin = new System.Windows.Forms.Padding(2);
            this.Name = "MessageDialog";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "生体判定エラー";
            this.TopMost = true;
            ((System.ComponentModel.ISupportInitialize)(this.picOK)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        public System.Windows.Forms.Label error_message_text;
        private System.Windows.Forms.PictureBox pictureBox1;
        private System.Windows.Forms.PictureBox picOK;
    }
}