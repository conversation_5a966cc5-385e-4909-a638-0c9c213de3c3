﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\OpenCvSharp4.runtime.win.4.5.2.20210404\build\net\OpenCvSharp4.runtime.win.props" Condition="Exists('..\packages\OpenCvSharp4.runtime.win.4.5.2.20210404\build\net\OpenCvSharp4.runtime.win.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{E521FEDE-A3DD-4AF7-970B-04B4372B5293}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>eDoktor.Taikoban.FaceClient</RootNamespace>
    <AssemblyName>eDoktor.Taikoban.FaceClient</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>3</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>..\x64\Release_CL_timeout3\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AForge, Version=2.2.5.0, Culture=neutral, PublicKeyToken=c1db6ff4eaa06aeb, processorArchitecture=MSIL">
      <HintPath>..\packages\AForge.2.2.5\lib\AForge.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Controls, Version=2.2.5.0, Culture=neutral, PublicKeyToken=a8ac264d1dc6b9d9, processorArchitecture=MSIL">
      <HintPath>..\packages\AForge.Controls.2.2.5\lib\AForge.Controls.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Imaging, Version=2.2.5.0, Culture=neutral, PublicKeyToken=ba8ddea9676ca48b, processorArchitecture=MSIL">
      <HintPath>..\packages\AForge.Imaging.2.2.5\lib\AForge.Imaging.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Math, Version=2.2.5.0, Culture=neutral, PublicKeyToken=abba2e25397ee8c9, processorArchitecture=MSIL">
      <HintPath>..\packages\AForge.Math.2.2.5\lib\AForge.Math.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Video, Version=2.2.5.0, Culture=neutral, PublicKeyToken=cbfb6e07d173c401, processorArchitecture=MSIL">
      <HintPath>..\packages\AForge.Video.2.2.5\lib\AForge.Video.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Video.DirectShow, Version=2.2.5.0, Culture=neutral, PublicKeyToken=61ea4348d43881b7, processorArchitecture=MSIL">
      <HintPath>..\packages\AForge.Video.DirectShow.2.2.5\lib\AForge.Video.DirectShow.dll</HintPath>
    </Reference>
    <Reference Include="eDoktor.Common, Version=1.0.0.0, Culture=neutral, PublicKeyToken=0ff7bf58c14a5b51, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\eDoktor.Taikoban.AppExtension\eDoktor.Common.dll</HintPath>
    </Reference>
    <Reference Include="eDoktor.Taikoban.BioAuthMessage, Version=1.0.0.0, Culture=neutral, PublicKeyToken=0ff7bf58c14a5b51, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\eDoktor.Taikoban.AppExtension\eDoktor.Taikoban.BioAuthMessage.dll</HintPath>
    </Reference>
    <Reference Include="GFRL">
      <HintPath>..\Library\GFRL.dll</HintPath>
    </Reference>
    <Reference Include="OpenCvSharp, Version=1.0.0.0, Culture=neutral, PublicKeyToken=6adad1e807fea099, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\OpenCvSharp4.4.5.2.20210404\lib\net461\OpenCvSharp.dll</HintPath>
    </Reference>
    <Reference Include="OpenCvSharp.Extensions, Version=1.0.0.0, Culture=neutral, PublicKeyToken=6adad1e807fea099, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\OpenCvSharp4.4.5.2.20210404\lib\net461\OpenCvSharp.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing.Common, Version=4.0.0.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Drawing.Common.5.0.2\lib\net461\System.Drawing.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.4\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=5.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.5.0.0\lib\net45\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Arguments.cs" />
    <Compile Include="BtnImageCahnge.cs" />
    <Compile Include="EnvironmentSetting.cs" />
    <Compile Include="FaceClientFormMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FaceClientFormMain.Designer.cs">
      <DependentUpon>FaceClientFormMain.cs</DependentUpon>
    </Compile>
    <Compile Include="FaceClientErr.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FaceClientErr.Designer.cs">
      <DependentUpon>FaceClientErr.cs</DependentUpon>
    </Compile>
    <Compile Include="FaceClientUserConfirm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FaceClientUserConfirm.Designer.cs">
      <DependentUpon>FaceClientUserConfirm.cs</DependentUpon>
    </Compile>
    <Compile Include="FaceInsertEnrollment.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FaceInsertEnrollment.Designer.cs">
      <DependentUpon>FaceInsertEnrollment.cs</DependentUpon>
    </Compile>
    <Compile Include="FaceInsertMessage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FaceInsertMessage.Designer.cs">
      <DependentUpon>FaceInsertMessage.cs</DependentUpon>
    </Compile>
    <Compile Include="FaceInsertPhotoGraphy.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FaceInsertPhotoGraphy.Designer.cs">
      <DependentUpon>FaceInsertPhotoGraphy.cs</DependentUpon>
    </Compile>
    <Compile Include="NativeMethods.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="FaceClientFormMain.resx">
      <DependentUpon>FaceClientFormMain.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FaceClientErr.resx">
      <DependentUpon>FaceClientErr.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FaceClientUserConfirm.resx">
      <DependentUpon>FaceClientUserConfirm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FaceInsertEnrollment.resx">
      <DependentUpon>FaceInsertEnrollment.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FaceInsertMessage.resx">
      <DependentUpon>FaceInsertMessage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FaceInsertPhotoGraphy.resx">
      <DependentUpon>FaceInsertPhotoGraphy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\eDoktor.Taikoban.FaceAuthCommon\eDoktor.Taikoban.FaceAuthCommon.csproj">
      <Project>{7A2D3A65-364E-43AD-93D9-9C1057E9C4B9}</Project>
      <Name>eDoktor.Taikoban.FaceAuthCommon</Name>
    </ProjectReference>
    <ProjectReference Include="..\eDoktor.Taikoban.FaceDetection\eDoktor.Taikoban.FaceDetection.csproj">
      <Project>{c772955c-661c-45fb-9a10-47a91cbe9209}</Project>
      <Name>eDoktor.Taikoban.FaceDetection</Name>
    </ProjectReference>
    <ProjectReference Include="..\eDoktor.Taikoban.FaceImageCntl\eDoktor.Taikoban.FaceImageCntl.csproj">
      <Project>{bd2b8ace-0230-46b3-84da-45d919c55fc9}</Project>
      <Name>eDoktor.Taikoban.FaceImageCntl</Name>
    </ProjectReference>
    <ProjectReference Include="..\eDoktor.Taikoban.FaceInterprocess\eDoktor.Taikoban.FaceInterprocess.csproj">
      <Project>{6eb7d3c9-23e9-4fab-83db-67272caa8a0e}</Project>
      <Name>eDoktor.Taikoban.FaceInterprocess</Name>
    </ProjectReference>
    <ProjectReference Include="..\eDoktorTaikoban.FaceClientCommon\eDoktorTaikoban.FaceClientCommon.csproj">
      <Project>{69CAF41B-94A8-4D86-9FA0-9E01FF4BFC6A}</Project>
      <Name>eDoktorTaikoban.FaceClientCommon</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <Content Include="image\btn_1.png" />
    <Content Include="image\btn_2.png" />
    <Content Include="image\btn_3.png" />
    <Content Include="image\Cancel_button_hover.png" />
    <Content Include="image\Cancel_button_off.png" />
    <Content Include="image\Cancel_button_on.png" />
    <Content Include="image\Close_button_hover.png" />
    <Content Include="image\Close_button_off.png" />
    <Content Include="image\Close_button_on.png" />
    <Content Include="image\err_main.png" />
    <None Include="image\facestamp_rogo_white.png" />
    <None Include="image\err_main2.png" />
    <None Include="image\Face_Guide.png" />
    <None Include="image\Face_Guide2.png" />
    <None Include="image\Face_Guide3.png" />
    <None Include="image\Face_Guide4.png" />
    <Content Include="image\Face_White.png" />
    <None Include="image\Face_White2.png" />
    <Content Include="image\main.png" />
    <None Include="image\main2.png" />
    <None Include="image\main3.png" />
    <Content Include="image\mainD.png" />
    <None Include="image\mainD2.png" />
    <None Include="image\mainD3.png" />
    <Content Include="image\No_button_hover.png" />
    <Content Include="image\No_button_off.png" />
    <Content Include="image\No_button_select.png" />
    <Content Include="image\Yes_button_hover.png" />
    <Content Include="image\Yes_button_off.png" />
    <Content Include="image\Yes_button_select.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>このプロジェクトは、このコンピューター上にない NuGet パッケージを参照しています。それらのパッケージをダウンロードするには、[NuGet パッケージの復元] を使用します。詳細については、http://go.microsoft.com/fwlink/?LinkID=322105 を参照してください。見つからないファイルは {0} です。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\OpenCvSharp4.runtime.win.4.5.2.20210404\build\net\OpenCvSharp4.runtime.win.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\OpenCvSharp4.runtime.win.4.5.2.20210404\build\net\OpenCvSharp4.runtime.win.props'))" />
  </Target>
</Project>