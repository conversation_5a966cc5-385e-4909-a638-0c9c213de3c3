﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Resources;
using System.Reflection;
using System.Security.Permissions;
using System.Windows.Forms;
using eDoktor.Taikoban.FaceImageCntl;
using System.IO;
using eDoktor.Taikoban.FaceAuthCommon;

namespace eDoktor.Taikoban.FaceClient
{
    /// <summary>
    /// 同意画面
    /// </summary>
    public partial class FaceInsertMessage : Form
    {
        /// <summary>
        /// 登録画面に遷移する際のメッセージ文画像
        /// </summary>
        private string _displayMessageImageFilePath = string.Empty;

        /// <summary>
        /// コンストラクタ
        /// </summary>
        public FaceInsertMessage(string displayMessageImageFilePath)
        {
            this._displayMessageImageFilePath = displayMessageImageFilePath;
            InitializeComponent();
        }

        /// <summary>
        /// 同意フラグ
        /// </summary>
        public bool IsOk { get; private set; } = false;

        /// <summary>
        /// Formロード処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void FaceInsertMessage_Load(object sender, EventArgs e)
        {
            var imageData = File.ReadAllBytes(this._displayMessageImageFilePath);
            Bitmap bmp = null;

            using (var ms = new MemoryStream(imageData))
            {
                bmp = new Bitmap(ms);
                ms.Close();
            }

            // 画面の表示領域に入るように調整する高さと幅を計算
            var height = this.picMessage.Height;
            var width = this.picMessage.Width;

            var location = this.picMessage.Location;

            // 画面の表示領域に入るように調整
            ClientCommonProc.PictureBoxResize(bmp.Size, ref width, ref height, ref location);
            this.picMessage.Location = location;
            this.picMessage.Width = width;
            this.picMessage.Height = height;
            this.picMessage.Image = bmp;
        }

        /// <summary>
        /// 同意するボタンクリック時
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnYes_Click(object sender, EventArgs e)
        {
            this.IsOk = true;
            this.Close();
        }

        /// <summary>
        /// 同意しないクリック時
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnNo_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// ボタン上からカーソルが外れた時
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn_MouseLeave(object sender, EventArgs e)
        {

            try
            {
                Button btn = (Button)sender;
                btn.BackgroundImage = eDoktor.Taikoban.FaceClient.Properties.Resources.btn_1;
                btn.ForeColor = System.Drawing.SystemColors.Window;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// ボタン上にカーソルを当てた時
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn_MouseEnter(object sender, EventArgs e)
        {

            try
            {
                Button btn = (Button)sender;
                btn.BackgroundImage = eDoktor.Taikoban.FaceClient.Properties.Resources.btn_2;
                btn.ForeColor = System.Drawing.Color.FromArgb(24, 109, 178);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// ボタンクリック時
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn_MouseDown(object sender, MouseEventArgs e)
        {

            try
            {
                Button btn = (Button)sender;
                btn.BackgroundImage = eDoktor.Taikoban.FaceClient.Properties.Resources.btn_3;
                btn.ForeColor = System.Drawing.SystemColors.Window;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }
    }
}

