﻿// RequestAndResponseData.cs
using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSettingsInfo;

namespace eDoktor.Taikoban.FaceInterprocess
{
    #region 設定取得要求

    /// <summary>
    /// 設定取得要求
    /// </summary>
    [System.Serializable]
    public class FaceSettingGetReqData : ReqResDataBass
    {

        #region Fields
        private string _staffId = string.Empty;
        #endregion

        #region Properties
        /// <summary>職員ID</summary>
        public string StaffId
        {
            get { return _staffId; }
            set { _staffId = value; }
        }
        #endregion

        #region Constructors
        public FaceSettingGetReqData()
        {
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 設定要求は時にパラメータがないため、受信のみとする。

            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);
        }
        #endregion
    }

    #endregion

    #region 設定取得応答

    /// <summary>
    /// 設定取得応答
    /// </summary>
    public class FaceSettingGetResData : ReqResDataBass
    {
        #region Fields

        /// <summary>
        /// Setting 設定情報
        /// </summary>
        public static readonly string SETTING = "Setting";

        /// <summary>
        /// Messages メッセージ情報
        /// </summary>
        public static readonly string MESSAGES = "Messages";

        /// <summary>
        /// Message メッセージ情報
        /// </summary>
        public static readonly string MESSAGE = "Message";

        /// <summary>
        /// Id メッセージID属性名
        /// </summary>
        public static readonly string MESSAGE_ID = "Id";

        /// <summary>
        /// Value メッセージ内容属性名
        /// </summary>
        public static readonly string MESSAGE_VALUE = "Value";

        /// <summary>
        /// デバイスリスト区切文字
        /// </summary>
        private const string DEVICE_LIST_DELIMITER = "|";

        /// <summary>
        /// 処理結果
        /// </summary>
        private EnumProcResult _procResult = 0;
        /// <summary>
        /// エラーID
        /// </summary>
        private EnumErrorId _errorId = 0;
        /// <summary>
        /// 設定情報
        /// </summary>
        private SettingsInfo _settingInfo = new SettingsInfo();
        /// <summary>
        /// 使用デバイス
        /// </summary>
        private List<string> _deviceWhiteList = new List<string>();
        /// <summary>
        /// 不使用デバイス
        /// </summary>
        private List<string> _deviceBlackList = new List<string>(); // 不使用デバイス設定

        #endregion

        #region Properties
        /// <summary>処理結果</summary>
        public EnumProcResult ProcResult
        {
            get { return _procResult; }
            set { _procResult = value; }
        }
        /// <summary>エラーID</summary>
        public EnumErrorId ErrorId
        {
            get { return _errorId; }
            set { _errorId = value; }
        }
        /// <summary>認証モード</summary>
        public EnumAuthMode AuthMode
        {
            get { return this._settingInfo.auth_mode; }
        }
        /// <summary>リトライ回数</summary>
        public int RetryCount
        {
            get { return this._settingInfo.retry_count; }
        }
        /// <summary>顔テンプレート登録上限</summary>
        public int FaceTemplateRegistMaxCount
        {
            get { return this._settingInfo.face_template_regist_max_count; }
        }
        /// <summary>顔サーチ待機時間</summary>
        public int FaceSearchWaitTime
        {
            get { return this._settingInfo.face_search_wait_time; }
        }
        /// <summary>起動時待機時間</summary>
        public int AuthStartWaitTime
        {
            get { return this._settingInfo.auth_start_wait_time; }
        }
        /// <summary>接続タイムアウト</summary>
        public int Timeout
        {
            get { return this._settingInfo.time_out; }
        }
        /// <summary>マスク装着度閾値</summary>
        public float MaskScoreThreshold
        {
            get { return this._settingInfo.mask_score_threshold; }
        }
        /// <summary>テンプレート評価値閾値(認証時)</summary>
        public float TemplateQualityThresholdAuth
        {
            get { return this._settingInfo.template_quality_threshold_auth; }
        }
        /// <summary>テンプレート評価値閾値(登録時)</summary>
        public float TemplateQualityThresholdInsert
        {
            get { return this._settingInfo.template_quality_threshold_insert; }
        }
        /// <summary>顔認証閾値</summary>
        public float FaceAuthThreshold
        {
            get { return this._settingInfo.face_auth_threshold; }
        }
        /// <summary>顔照合タイプ</summary>
        public GFRL.FaceRecognition.FaceRecogDataType FaceRecogDataType
        {
            get { return this._settingInfo.face_recog_data_type; }
        }
        /// <summary>性別年齢推定有無</summary>
        public bool IsEstimateGenderAge
        {
            get { return this._settingInfo.is_estimate_gender_age; }
        }
        /// <summary>顔サーチ時照合適合度閾値</summary>
        public float FaceRecogQualityThreshold
        {
            get { return this._settingInfo.face_recog_quality_threshold; }
        }
        /// <summary>最大検出顔数</summary>
        public int FaceSearchCountMax
        {
            get { return this._settingInfo.face_search_count_max; }
        }
        /// <summary>最小目間画素数</summary>
        public int FaceSearchScaleMin
        {
            get { return this._settingInfo.face_search_scale_min; }
        }
        /// <summary>最大目間画素数</summary>
        public int FaceSearchScaleMax
        {
            get { return this._settingInfo.face_search_scale_max; }
        }
        /// <summary>顔サーチ入力画像幅</summary>
        public int InputImageWidth
        {
            get { return this._settingInfo.input_image_width; }
        }
        /// <summary>顔サーチ入力画像高さ</summary>
        public int InputImageHeight
        {
            get { return this._settingInfo.input_image_height; }
        }
        /// <summary>ユーザ認証時、テンプレートを保持しておくか</summary>
        public bool IsHoldTemplateData
        {
            get { return this._settingInfo.is_hold_template_data; }
        }
        /// <summary>別スレッドでのテンプレート更新間隔</summary>
        public int TemplateGetWaitTime
        {
            get { return this._settingInfo.template_get_wait_time; }
        }
        /// <summary>検索ユーザ確認ダイアログ表示有無(ユーザ検索認証時)</summary>
        public bool IsDisplayAuthUserDialog
        {
            get { return this._settingInfo.is_display_auth_user_daialog; }
        }
        /// <summary>検索ユーザ確認ダイアログ表示時の認証に使用した画像の表示有無</summary>
        public bool IsDisplayAuthImage
        {
            get { return this._settingInfo.is_display_auth_image; }
        }
        /// <summary>まばたき検知機能を使用するかどうか</summary>
        public bool IsUseEyeBlink
        {
            get { return this._settingInfo.is_use_eye_blink; }
        }
        /// <summary>まばたき検知に顔検出を使用するかどうか</summary>
        public bool IsUseFaceForBlinking
        {
            get { return this._settingInfo.is_use_face_for_blinking; }
        }
        /// <summary>まばたき検知開始のための顔の最大面積</summary>
        public int FaceSizeMax
        {
            get { return this._settingInfo.face_size_max; }
        }
        /// <summary>まばたき検知開始のための顔の最小面積</summary>
        public int FaceSizeMin
        {
            get { return this._settingInfo.face_size_min; }
        }
        /// <summary>まばたき検知開始のための目の最大面積</summary>
        public int EyeSizeMax
        {
            get { return this._settingInfo.eye_size_max; }
        }
        /// <summary>まばたき検知開始のための目の最小面積</summary>
        public int EyeSizeMin
        {
            get { return this._settingInfo.eye_size_min; }
        }
        /// <summary>まばたき検知開始のための待機時間</summary>
        public float BlinkWaitingTime
        {
            get { return this._settingInfo.blink_waiting_time; }
        }
        /// <summary>まばたきをしたとみなす回数</summary>
        public int BlinkCount
        {
            get { return this._settingInfo.blink_count; }
        }
        /// <summary>まばたきをしている時間</summary>
        public float BlinkingTime
        {
            get { return this._settingInfo.blinking_time; }
        }
        /// <summary>まばたき検知を行う範囲のX座標</summary>
        public int BlinkAreaX
        {
            get { return this._settingInfo.blink_area_x; }
        }
        /// <summary>まばたき検知を行う範囲のY座標</summary>
        public int BlinkAreaY
        {
            get { return this._settingInfo.blink_area_y; }
        }
        /// <summary>まばたき検知を行う範囲の横幅</summary>
        public int BlinkAreaWidth
        {
            get { return this._settingInfo.blink_area_width; }
        }
        /// <summary>まばたき検知を行う範囲の縦幅</summary>
        public int BlinkAreaHeight
        {
            get { return this._settingInfo.blink_area_height; }
        }
        /// <summary>まばたきメッセージを表示するかどうか</summary>
        public bool IsDisplayBlinkMessage
        {
            get { return this._settingInfo.is_display_blink_message; }
        }
        /// <summary>まばたきメッセージ</summary>
        public string BlinkMessage
        {
            get { return this._settingInfo.blink_message; }
        }
        /// <summary>まばたきメッセージを表示するかどうか</summary>
        public bool IsDisplayFaceGuide
        {
            get { return this._settingInfo.is_display_face_guide; }
        }

        //--------------------------------------なりすまし判定設定値--------------------------------------

        /// <summary>
        /// トークンファイルパス
        /// </summary>
        public string TokenFilePath
        {
            get { return this._settingInfo.token_file_path; }
        }

        /// <summary>
        /// faceDetectModelファイルパス
        /// </summary>
        public string FaceDetectModelFilePath
        {
            get { return this._settingInfo.faceDetectModel_file_path; }
        }

        /// <summary>
        /// faceDetectParamファイルパス
        /// </summary>
        public string FaceDetectParamFilePath
        {
            get { return this._settingInfo.faceDetectParam_file_path; }
        }

        /// <summary>
        /// fakeModelHファイルパス
        /// </summary>
        public string FakeModelHFilePath
        {
            get { return this._settingInfo.fakeModelH_file_path; }
        }

        /// <summary>
        /// fakeModelVファイルパス
        /// </summary>
        public string FakeModelVFilePath
        {
            get { return this._settingInfo.fakeModelV_file_path; }
        }

        /// <summary>
        /// faceDirHModelファイルパス
        /// </summary>
        public string FaceDirHmodelFilePath
        {
            get { return this._settingInfo.faceDirHModel_file_path; }
        }

        /// <summary>
        /// faceDirVModelファイルパス
        /// </summary>
        public string FaceDirVmodelFilePath
        {
            get { return this._settingInfo.faceDirVModel_file_path; }
        }

        /// <summary>
        /// glassesModelファイルパス
        /// </summary>
        public string GlassesModelFilePath
        {
            get { return this._settingInfo.glassesModel_file_path; }
        }

        /// <summary>
        /// maskModelファイルパス
        /// </summary>
        public string MaskModelFilePath
        {
            get { return this._settingInfo.maskModel_file_path; }
        }

        /// <summary>
        /// eyeDetectModelファイルパス
        /// </summary>
        public string EyeDetectModelFilePath
        {
            get { return this._settingInfo.eyeDetectModel_file_path; }
        }

        /// <summary>
        /// eyeStatusModelファイルパス
        /// </summary>
        public string EyeStatusModelFilePath
        {
            get { return this._settingInfo.eyeStatusModel_file_path; }
        }

        /// <summary>
        /// eyeDirModelファイルパス
        /// </summary>
        public string EyeDirModelFilePath
        {
            get { return this._settingInfo.eyeDirModel_file_path; }
        }

        /// <summary>
        /// 顔位置検出用：顔位置検出を行う最小顔横幅サイズ(px)を指定します。
        /// </summary>
        public int MinFaceWidthParam
        {
            get { return this._settingInfo.minFaceWidth_param; }
        }

        /// <summary>
        /// 顔位置検出用：顔位置検出を行う最大顔横幅サイズ(px)を指定します。
        /// </summary>
        public int MaxFaceWidthParam
        {
            get { return this._settingInfo.maxFaceWidth_param; }
        }

        /// <summary>
        /// 顔位置検出用：顔接近度を判定するため、「顔位置検出サイズ面積 ÷ 入力画像サイズ面積」により算出される顔サイズ許容する下限・上限割合を指定します。0.05〜1.00値の範囲で指定します。
        /// </summary>
        public float FaceAreaMinRatioParam
        {
            get { return this._settingInfo.faceAreaMinRatio_param; }
        }

        /// <summary>
        /// 顔位置検出用：顔接近度を判定するため、「顔位置検出サイズ面積 ÷ 入力画像サイズ面積」により算出される顔サイズ許容する下限・上限割合を指定します。0.05〜1.00値の範囲で指定します。
        /// </summary>
        public float FaceAreaMaxRatioParam
        {
            get { return this._settingInfo.faceAreaMaxRatio_param; }
        }

        /// <summary>
        /// 顔位置検出用：検出された顔位置が上下左右端に位置する場合に、以降処理を停止するかどうかを真偽値で指定します。
        /// </summary>
        public bool EdgePosErrModeParam
        {
            get { return this._settingInfo.edgePosErrMode_param; }
        }

        /// <summary>
        /// フェイク判定用：フェイク判定を行うかどうかを真偽値で指定します。
        /// </summary>
        public bool IsFakeModeParam
        {
            get { return this._settingInfo.isFakeMode_param; }
        }

        /// <summary>
        /// フェイク判定用：Biodata::isFakeLikelihood値から、フェイク顔と判定するための閾値となります。0.0〜1.0値の範囲で指定します。
        /// </summary>
        public float FakeJudgeThParam
        {
            get { return this._settingInfo.fakeJudgeTh_param; }
        }

        /// <summary>
        /// 顔向き判定用：顔向き判定を行うかどうかを真偽値で指定します。
        /// </summary>
        public bool IsFaceDirModeParam
        {
            get { return this._settingInfo.isFaceDirMode_param; }
        }


        /// <summary>
        /// メガネ判定用：メガネ判定を行うかどうかを真偽値で指定します。
        /// </summary>
        public bool IsGlassesModeParam
        {
            get { return this._settingInfo.isGlassesMode_param; }
        }


        /// <summary>
        /// マスク判定用：マスク判定を行うかどうかを真偽値で指定します。
        /// </summary>
        public bool IsMaskModeParam
        {
            get { return this._settingInfo.isMaskMode_param; }
        }

        /// <summary>
        /// マスク判定用：Biodata::isMaskLikelihood値から、マスク装着状態と判定するための閾値となります。0.0〜1.0の値の範囲で指定します。
        /// </summary>
        public float MaskJudgeThParam
        {
            get { return this._settingInfo.maskJudgeTh_param; }
        }

        /// <summary>
        /// 目の位置検出用：目の位置検出・目の開閉判定を行うかどうかを真偽値で指定します。
        /// </summary>
        public bool IsEyeModeParam
        {
            get { return this._settingInfo.isEyeMode_param; }
        }


        /// <summary>
        /// 目の向き判定用：目の向き判定を行うかどうかを真偽値で指定します。
        /// </summary>
        public bool IsEyeDirModeParam
        {
            get { return this._settingInfo.isEyeDirMode_param; }
        }

        //なりすまし判定方法

        /// <summary>
        /// なりすまし判定フラグ
        /// </summary>
        public bool IsLivenessCheck
        {
            get { return this._settingInfo.is_livenessCheck; }
        }

        /// <summary>
        /// なりすまし判定方法：判定時間
        /// </summary>
        public int JudgeTime
        {
            get { return this._settingInfo.judge_time; }
        }

        /// <summary>
        /// なりすまし判定方法：判定割合
        /// </summary>
        public int JudgeRatio
        {
            get { return this._settingInfo.judge_ratio; }
        }

        /// <summary>
        /// なりすまし判定エラーメッセージ
        /// </summary>
        public string LivenessCheckErrorMessage
        {
            get { return this._settingInfo.liveness_check_error_message; }
        }

        /// <summary>
        /// なりすまし判定タイムアウトエラーメッセージ
        /// </summary>
        public string LivenessCheckTimeoutErrorMessage
        {
            get { return this._settingInfo.liveness_check_timeout_error_message; }
        }

        /// <summary>
        /// なりすまし判定タイムアウト
        /// </summary>
        public int LivenessCheckTimeout
        {
            get { return this._settingInfo.liveness_check_timeout; }
        }

        //--------------------------------------なりすまし判定設定値ここまで--------------------------------------

        /// <summary>使用デバイス設定</summary>
        public List<string> DeviceWhiteList
        {
            get { return _deviceWhiteList; }
            set { _deviceWhiteList = value; }
        }
        /// <summary>不使用デバイス設定</summary>
        public List<string> DeviceBlackList
        {
            get { return _deviceBlackList; }
            set { _deviceBlackList = value; }
        }

        /// <summary>設定情報</summary>
        public SettingsInfo SettingInfo
        {
            get { return _settingInfo; }
        }
        #endregion

        #region Constructors
        public FaceSettingGetResData()
        {
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);

            // XMLから処理結果を取り出す
            var strProcResult = base.GetDocumentValue(nameof(this.ProcResult));

            if (!int.TryParse(strProcResult, out int procResult)
                || !Enum.IsDefined(typeof(EnumProcResult), procResult))
            {
                Trace.OutputErrorTrace("処理結果の値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                return;
            }

            ProcResult = (EnumProcResult)procResult;

            // XMLからエラーIDを取り出す
            var strErrorId = base.GetDocumentValue(nameof(this.ErrorId));

            if (!int.TryParse(strErrorId, out int errorId)
                || !Enum.IsDefined(typeof(EnumErrorId), errorId))
            {
                Trace.OutputErrorTrace("エラーIDの値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                return;
            }

            ErrorId = (EnumErrorId)errorId;

            if (ProcResult == EnumProcResult.Success)
            {
                // XMLから設定情報を取り出す
                var result = this.GetSetting();

                if (result == false)
                {
                    Trace.OutputErrorTrace("Settingの値が不正です。");
                    ProcResult = EnumProcResult.Fail;
                    ErrorId = EnumErrorId.Exception;
                    return;
                }

                // メッセージを設定
                foreach (XmlNode node in base.GetNodeList(MESSAGE))
                {
                    Messages.SetMessageList(node.Attributes[MESSAGE_ID].Value, node.Attributes[MESSAGE_VALUE].Value);
                }
            }
        }

        /// <summary>
        /// 設定情報取得
        /// </summary>
        /// <returns>処理結果</returns>
        private bool GetSetting()
        {
            try
            {
                // XMLから設定情報を取り出す
                var settingNode = base.GetNode(SETTING);

                if (settingNode == null)
                {
                    return false;
                }
 
                this._settingInfo.auth_mode = (EnumAuthMode)Convert.ToInt32(base.GetNodeValue(settingNode, nameof(AuthMode)));
                this._settingInfo.retry_count = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(RetryCount)));
                this._settingInfo.face_template_regist_max_count = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(FaceTemplateRegistMaxCount)));
                this._settingInfo.face_search_wait_time = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(FaceSearchWaitTime)));
                this._settingInfo.auth_start_wait_time = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(AuthStartWaitTime)));
                this._settingInfo.time_out = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(Timeout)));
                this._settingInfo.mask_score_threshold = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(MaskScoreThreshold)));
                this._settingInfo.template_quality_threshold_auth = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(TemplateQualityThresholdAuth)));
                this._settingInfo.template_quality_threshold_insert = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(TemplateQualityThresholdInsert)));
                this._settingInfo.face_auth_threshold = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(FaceAuthThreshold)));
                this._settingInfo.face_recog_data_type = (GFRL.FaceRecognition.FaceRecogDataType)Convert.ToInt32(base.GetNodeValue(settingNode, nameof(FaceRecogDataType)));
                this._settingInfo.is_estimate_gender_age = Convert.ToBoolean(base.GetNodeValue(settingNode, nameof(IsEstimateGenderAge)));
                this._settingInfo.face_recog_quality_threshold = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(FaceRecogQualityThreshold)));
                this._settingInfo.face_search_count_max = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(FaceSearchCountMax)));
                this._settingInfo.face_search_scale_min = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(FaceSearchScaleMin)));
                this._settingInfo.face_search_scale_max = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(FaceSearchScaleMax)));
                this._settingInfo.input_image_width = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(InputImageWidth)));
                this._settingInfo.input_image_height = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(InputImageHeight)));
                this._settingInfo.is_hold_template_data = Convert.ToBoolean(base.GetNodeValue(settingNode, nameof(IsHoldTemplateData)));
                this._settingInfo.template_get_wait_time = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(TemplateGetWaitTime)));
                this._settingInfo.is_display_auth_user_daialog = Convert.ToBoolean(base.GetNodeValue(settingNode, nameof(IsDisplayAuthUserDialog)));
                this._settingInfo.is_display_auth_image= Convert.ToBoolean(base.GetNodeValue(settingNode, nameof(IsDisplayAuthImage)));
                this._settingInfo.is_use_eye_blink = Convert.ToBoolean(base.GetNodeValue(settingNode, nameof(IsUseEyeBlink)));
                this._settingInfo.is_use_face_for_blinking = Convert.ToBoolean(base.GetNodeValue(settingNode, nameof(IsUseFaceForBlinking)));
                this._settingInfo.face_size_max = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(FaceSizeMax)));
                this._settingInfo.face_size_min = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(FaceSizeMin)));
                this._settingInfo.eye_size_max = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(EyeSizeMax)));
                this._settingInfo.eye_size_min = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(EyeSizeMin)));
                this._settingInfo.blink_waiting_time = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(BlinkWaitingTime)));
                this._settingInfo.blink_count = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(BlinkCount)));
                this._settingInfo.blinking_time = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(BlinkingTime)));
                this._settingInfo.blink_area_x = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(BlinkAreaX)));
                this._settingInfo.blink_area_y = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(BlinkAreaY)));
                this._settingInfo.blink_area_width = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(BlinkAreaWidth)));
                this._settingInfo.blink_area_height = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(BlinkAreaHeight)));
                this._settingInfo.is_display_blink_message = Convert.ToBoolean(base.GetNodeValue(settingNode, nameof(IsDisplayBlinkMessage)));
                this._settingInfo.blink_message = Convert.ToString(base.GetNodeValue(settingNode, nameof(BlinkMessage)));
                this._settingInfo.is_display_face_guide = Convert.ToBoolean(base.GetNodeValue(settingNode, nameof(IsDisplayFaceGuide)));
                //なりすまし判定設定値
                this._settingInfo.token_file_path = Convert.ToString(base.GetNodeValue(settingNode, nameof(TokenFilePath)));
                this._settingInfo.faceDetectModel_file_path = Convert.ToString(base.GetNodeValue(settingNode, nameof(FaceDetectModelFilePath)));
                this._settingInfo.faceDetectParam_file_path = Convert.ToString(base.GetNodeValue(settingNode, nameof(FaceDetectParamFilePath)));
                this._settingInfo.fakeModelH_file_path = Convert.ToString(base.GetNodeValue(settingNode, nameof(FakeModelHFilePath)));
                this._settingInfo.fakeModelV_file_path = Convert.ToString(base.GetNodeValue(settingNode, nameof(FakeModelVFilePath)));
                this._settingInfo.faceDirHModel_file_path = Convert.ToString(base.GetNodeValue(settingNode, nameof(FaceDirHmodelFilePath)));
                this._settingInfo.faceDirVModel_file_path = Convert.ToString(base.GetNodeValue(settingNode, nameof(FaceDirVmodelFilePath)));
                this._settingInfo.glassesModel_file_path = Convert.ToString(base.GetNodeValue(settingNode, nameof(GlassesModelFilePath)));
                this._settingInfo.maskModel_file_path = Convert.ToString(base.GetNodeValue(settingNode, nameof(MaskModelFilePath)));
                this._settingInfo.eyeDetectModel_file_path = Convert.ToString(base.GetNodeValue(settingNode, nameof(EyeDetectModelFilePath)));
                this._settingInfo.eyeStatusModel_file_path = Convert.ToString(base.GetNodeValue(settingNode, nameof(EyeStatusModelFilePath)));
                this._settingInfo.eyeDirModel_file_path = Convert.ToString(base.GetNodeValue(settingNode, nameof(EyeDirModelFilePath)));
                this._settingInfo.minFaceWidth_param = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(MinFaceWidthParam)));
                this._settingInfo.maxFaceWidth_param = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(MaxFaceWidthParam)));
                this._settingInfo.faceAreaMinRatio_param = (float)Convert.ToDouble(base.GetNodeValue(settingNode, nameof(FaceAreaMinRatioParam)));
                this._settingInfo.faceAreaMaxRatio_param = (float)Convert.ToDouble(base.GetNodeValue(settingNode, nameof(FaceAreaMaxRatioParam)));
                this._settingInfo.edgePosErrMode_param = Convert.ToBoolean(base.GetNodeValue(settingNode, nameof(EdgePosErrModeParam)));
                this._settingInfo.isFakeMode_param = Convert.ToBoolean(base.GetNodeValue(settingNode, nameof(IsFakeModeParam)));
                this._settingInfo.fakeJudgeTh_param = (float)Convert.ToDouble(base.GetNodeValue(settingNode, nameof(FakeJudgeThParam)));
                this._settingInfo.isFaceDirMode_param = Convert.ToBoolean(base.GetNodeValue(settingNode, nameof(IsFaceDirModeParam)));
                this._settingInfo.isGlassesMode_param = Convert.ToBoolean(base.GetNodeValue(settingNode, nameof(IsGlassesModeParam)));
                this._settingInfo.isMaskMode_param = Convert.ToBoolean(base.GetNodeValue(settingNode, nameof(IsMaskModeParam)));
                this._settingInfo.maskJudgeTh_param = (float)Convert.ToDouble(base.GetNodeValue(settingNode, nameof(MaskJudgeThParam)));
                this._settingInfo.isEyeMode_param = Convert.ToBoolean(base.GetNodeValue(settingNode, nameof(IsEyeModeParam)));
                this._settingInfo.isEyeDirMode_param = Convert.ToBoolean(base.GetNodeValue(settingNode, nameof(IsEyeDirModeParam)));
                //なりすまし判定方法
                this._settingInfo.is_livenessCheck = Convert.ToBoolean(base.GetNodeValue(settingNode, nameof(IsLivenessCheck)));
                this._settingInfo.judge_time = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(JudgeTime)));
                this._settingInfo.judge_ratio = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(JudgeRatio)));
                this._settingInfo.liveness_check_error_message = Convert.ToString(base.GetNodeValue(settingNode, nameof(LivenessCheckErrorMessage)));
                this._settingInfo.liveness_check_timeout_error_message = Convert.ToString(base.GetNodeValue(settingNode, nameof(LivenessCheckTimeoutErrorMessage)));
                this._settingInfo.liveness_check_timeout = Convert.ToInt32(base.GetNodeValue(settingNode, nameof(LivenessCheckTimeout)));


                var strDeviceWhiteList = base.GetNodeValue(settingNode, nameof(DeviceWhiteList));
                DeviceWhiteList = strDeviceWhiteList.Split(new string[] { DEVICE_LIST_DELIMITER }, StringSplitOptions.RemoveEmptyEntries).ToList();
                var strDeviceBlackList = base.GetNodeValue(settingNode, nameof(DeviceBlackList));
                DeviceBlackList = strDeviceBlackList.Split(new string[] { DEVICE_LIST_DELIMITER }, StringSplitOptions.RemoveEmptyEntries).ToList();
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return false;
            }

            return true;
        }
        #endregion
    }

    #endregion

    #region 顔テンプレート登録確認要求

    /// <summary>
    /// 顔テンプレート登録確認要求
    /// </summary>
    [System.Serializable]
    public class FaceTemplateRegConfirmReqData : ReqResDataBass
    {
        #region Fields
        private string _logonId = string.Empty;

        #endregion

        #region Properties
        /// <summary>職員ID</summary>
        public string LogonId
        {
            get { return _logonId; }
            set { _logonId = value; }
        }

        #endregion

        #region Constructors
        public FaceTemplateRegConfirmReqData()
        {
        }
        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);

            // XMLから職員IDを取り出す
            LogonId = base.GetDocumentValue(nameof(this.LogonId));

            if (string.IsNullOrWhiteSpace(this.LogonId))
            {
                throw new ArgumentException("職員IDが不正です。");
            }

        }
        #endregion
    }

    #endregion

    #region 顔テンプレート登録確認応答

    /// <summary>
    /// 顔テンプレート登録確認応答
    /// </summary>
    public class FaceTemplateRegConfirmResData : ReqResDataBass
    {
        #region Fields
        private EnumProcResult _procResult = 0;
        private EnumErrorId _errorId = 0;
        private EnumRegistState _registState = 0;
        private int _accountId = 0;
        private string _userName = string.Empty;
        #endregion

        #region Properties
        /// <summary>処理結果</summary>
        public EnumProcResult ProcResult
        {
            get { return _procResult; }
            set { _procResult = value; }
        }
        /// <summary>エラーID</summary>
        public EnumErrorId ErrorId
        {
            get { return _errorId; }
            set { _errorId = value; }
        }
        /// <summary>登録状態</summary>
        public EnumRegistState RegistState
        {
            get { return _registState; }
            set { _registState = value; }
        }
        /// <summary>アカウントID</summary>
        public int AccountId
        {
            get { return _accountId; }
            set { _accountId = value; }
        }
        /// <summary>職員名</summary>
        public string UserName
        {
            get { return _userName; }
            set { _userName = value; }
        }
        #endregion

        #region Constructors
        public FaceTemplateRegConfirmResData()
        {
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);

            // XMLから処理結果を取り出す
            var strProcResult = base.GetDocumentValue(nameof(this.ProcResult));

            if (!int.TryParse(strProcResult, out int procResult)
                || !Enum.IsDefined(typeof(EnumProcResult), procResult))
            {
                Trace.OutputErrorTrace("処理結果の値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                RegistState = EnumRegistState.NoRegist;
                return;
            }

            ProcResult = (EnumProcResult)procResult;

            // XMLからエラーIDを取り出す
            var strErrorId = base.GetDocumentValue(nameof(this.ErrorId));

            if (!int.TryParse(strErrorId, out int errorId)
                || !Enum.IsDefined(typeof(EnumErrorId), errorId))
            {
                Trace.OutputErrorTrace("エラーIDの値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                RegistState = EnumRegistState.NoRegist;
                return;
            }

            ErrorId = (EnumErrorId)errorId;

            // XMLから登録状態を取り出す
            var strRegistState = base.GetDocumentValue(nameof(this.RegistState));

            if (!int.TryParse(strRegistState, out int registState)
                || !Enum.IsDefined(typeof(EnumRegistState), registState))
            {
                Trace.OutputErrorTrace("登録状態の値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                RegistState = EnumRegistState.NoRegist;
                return;
            }

            RegistState = (EnumRegistState)registState;

            if (ProcResult == EnumProcResult.Success)
            {
                // XMLから職員IDを取り出す
                var strAccountId = base.GetDocumentValue(nameof(this.AccountId));

                if (int.TryParse(strAccountId, out int intAccountId)
                    && intAccountId > 0)

                {
                    this.AccountId = intAccountId;
                }
                else
                {
                    Trace.OutputErrorTrace("アカウントIDが不正です。");
                    ProcResult = EnumProcResult.Fail;
                    ErrorId = EnumErrorId.Exception;
                    RegistState = EnumRegistState.NoRegist;
                }

                // XMLから職員名を取り出す
                this.UserName = base.GetDocumentValue(nameof(this.UserName));
            }
        }
        #endregion
    }

    #endregion

    #region 顔テンプレート取得要求

    /// <summary>
    /// 顔テンプレート取得要求
    /// </summary>
    [System.Serializable]
    public class FaceTemplateGetReqData : ReqResDataBass
    {
        #region Fields
        private string _staffId = string.Empty;
        #endregion

        #region Properties
        /// <summary>職員ID</summary>
        public string LogonId
        {
            get { return _staffId; }
            set { _staffId = value; }
        }
        #endregion

        #region Constructors
        public FaceTemplateGetReqData()
        {
        }
        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);
        }
        #endregion
    }

    #endregion

    #region 顔テンプレート取得応答

    /// <summary>
    /// 顔テンプレート取得応答
    /// </summary>
    public class FaceTemplateGetResData : ReqResDataBass
    {
        #region Fields
        private bool _returnCode = false;
        #endregion

        #region Properties
        /// <summary>応答コード</summary>
        public bool ReturnCode
        {
            get { return _returnCode; }
            set { _returnCode = value; }
        }
        #endregion

        #region Constructors
        public FaceTemplateGetResData()
        {
        }
        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);
        }
        #endregion
    }

    #endregion

    #region 顔認証要求

    /// <summary>
    /// 顔認証要求
    /// </summary>
    [System.Serializable]
    public class FaceAuthtReqData : ReqResDataBass
    {
        #region Fields
        private int _account_Id = 0;
        private byte[] _image = null;
        #endregion

        #region Properties
        /// <summary>職員ID</summary>
        public int AccountId
        {
            get { return _account_Id; }
            set { _account_Id = value; }
        }

        /// <summary>画像データ</summary>
        public byte[] Image
        {
            get { return _image; }
            set { _image = value; }
        }
        #endregion

        #region Constructors
        public FaceAuthtReqData()
        {
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);

            // XMLからアカウントIDを取り出す
            var strAccountId = base.GetDocumentValue(nameof(this.AccountId));

            if (int.TryParse(strAccountId, out int intAccountId)
                && intAccountId > 0)
            {
                this.AccountId = intAccountId;
            }
            else
            {
                throw new ArgumentException("職員IDが不正です。");
            }

            // XMLから画像を取り出す
            var strImage = base.GetDocumentValue(nameof(this.Image));

            this.Image = Convert.FromBase64String(strImage);

            if (Image.Length < 1)
            {
                throw new ArgumentException("画像が不正です。");
            }
        }
        #endregion
    }

    #endregion

    #region 顔認証応答

    /// <summary>
    /// 顔認証応答
    /// </summary>
    public class FaceAuthtResData : ReqResDataBass
    {
        #region Fields
        private EnumProcResult _procResult = 0;
        private EnumErrorId _errorId = 0;
        private EnumAuthResult _authResult = 0;
        #endregion

        #region Properties
        /// <summary>処理結果</summary>
        public EnumProcResult ProcResult
        {
            get { return _procResult; }
            set { _procResult = value; }
        }

        /// <summary>エラーID</summary>
        public EnumErrorId ErrorId
        {
            get { return _errorId; }
            set { _errorId = value; }
        }
        /// <summary>認証結果</summary>
        public EnumAuthResult AuthResult
        {
            get { return _authResult; }
            set { _authResult = value; }
        }
        #endregion

        #region Constructors
        public FaceAuthtResData()
        {
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);

            // XMLから処理結果を取り出す
            var strProcResult = base.GetDocumentValue(nameof(this.ProcResult));

            if (!int.TryParse(strProcResult, out int procResult)
                || !Enum.IsDefined(typeof(EnumProcResult), procResult))
            {
                Trace.OutputErrorTrace("処理結果の値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                return;
            }

            ProcResult = (EnumProcResult)procResult;

            // XMLからエラーIDを取り出す
            var strErrorId = base.GetDocumentValue(nameof(this.ErrorId));

            if (!int.TryParse(strErrorId, out int errorId)
                || !Enum.IsDefined(typeof(EnumErrorId), errorId))
            {
                Trace.OutputErrorTrace("エラーIDの値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                return;
            }

            ErrorId = (EnumErrorId)errorId;

            // XMLから認証結果を取り出す
            var strAuthResult = base.GetDocumentValue(nameof(this.AuthResult));

            if (!int.TryParse(strAuthResult, out int authResult)
                || !Enum.IsDefined(typeof(EnumAuthResult), authResult))
            {
                Trace.OutputErrorTrace("認証結果の値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                return;
            }

            AuthResult = (EnumAuthResult)authResult;
        }
        #endregion
    }

    #endregion

    #region 顔テンプレート登録要求(サーバ認証モード)

    /// <summary>
    /// 顔テンプレート登録要求(サーバ認証モード)
    /// </summary>
    [System.Serializable]
    public class FaceTemplateRegServerReqData : ReqResDataBass
    {
        #region Fields
        private int _accountId = 0;
        private byte[] _image = null;
        #endregion

        #region Properties
        /// <summary>職員ID</summary>
        public int AccountId
        {
            get { return _accountId; }
            set { _accountId = value; }
        }

        /// <summary>画像データ</summary>
        public byte[] Image
        {
            get { return _image; }
            set { _image = value; }
        }
        #endregion

        #region Constructors
        public FaceTemplateRegServerReqData()
        {
        }
        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);

            // XMLからアカウントIDを取り出す
            var strAccountId = base.GetDocumentValue(nameof(this.AccountId));

            if (int.TryParse(strAccountId, out int intAccountId)
                && intAccountId > 0)

            {
                this.AccountId = intAccountId;
            }
            else
            {
                throw new ArgumentException("職員IDが不正です。");
            }

            // XMLから画像を取り出す
            var strImage = base.GetDocumentValue(nameof(this.Image));
            this.Image = Convert.FromBase64String(strImage);

            if (Image.Length < 1)
            {
                throw new ArgumentException("画像が不正です。");
            }
        }
        #endregion
    }

    #endregion

    #region 顔テンプレート登録応答(サーバ認証モード)

    /// <summary>
    /// 顔テンプレート登録応答(サーバ認証モード)
    /// </summary>
    public class FaceTemplateRegServerResData : ReqResDataBass
    {
        #region Fields
        private EnumProcResult _procResult = 0;
        private EnumErrorId _errorId = 0;
        #endregion

        #region Properties
        /// <summary>処理結果</summary>
        public EnumProcResult ProcResult
        {
            get { return _procResult; }
            set { _procResult = value; }
        }

        /// <summary>エラーID</summary>
        public EnumErrorId ErrorId
        {
            get { return _errorId; }
            set { _errorId = value; }
        }
        #endregion

        #region Constructors
        public FaceTemplateRegServerResData()
        {
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);

            // XMLから処理結果を取り出す
            var strProcResult = base.GetDocumentValue(nameof(this.ProcResult));

            if (!int.TryParse(strProcResult, out int procResult)
                || !Enum.IsDefined(typeof(EnumProcResult), procResult))
            {
                Trace.OutputErrorTrace("処理結果の値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                return;
            }

            ProcResult = (EnumProcResult)procResult;

            // XMLからエラーIDを取り出す
            var strErrorId = base.GetDocumentValue(nameof(this.ErrorId));

            if (!int.TryParse(strErrorId, out int errorId)
                || !Enum.IsDefined(typeof(EnumErrorId), errorId))
            {
                Trace.OutputErrorTrace("エラーIDの値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                return;
            }

            ErrorId = (EnumErrorId)errorId;
        }
        #endregion
    }

    #endregion

    #region 顔テンプレート登録要求(クライアント認証モード)

    /// <summary>
    /// 顔テンプレート登録要求(クライアント認証モード)
    /// </summary>
    [System.Serializable]
    public class FaceTemplateRegClientReqData : ReqResDataBass
    {
        #region Fields
        private int _recordId = 0;
        private int _collationCount = 0;
        #endregion

        #region Properties
        /// <summary>レコードID</summary>
        public int RecordId
        {
            get { return _recordId; }
            set { _recordId = value; }
        }
        /// <summary>照合回数</summary>
        public int CollationCount
        {
            get { return _collationCount; }
            set { _collationCount = value; }
        }

        #endregion

        #region Constructors
        public FaceTemplateRegClientReqData()
        {
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);
        }
        #endregion
    }

    #endregion

    #region 顔テンプレート登録応答(クライアント認証モード)

    /// <summary>
    /// 顔テンプレート登録応答(クライアント認証モード)
    /// </summary>
    public class FaceTemplateRegClientResData : ReqResDataBass
    {

        #region Fields
        private bool _returnCode = false;
        #endregion

        #region Properties
        /// <summary>応答コード</summary>
        public bool ReturnCode
        {
            get { return _returnCode; }
            set { _returnCode = value; }
        }
        #endregion

        #region Constructors
        public FaceTemplateRegClientResData()
        {
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);
        }
        #endregion
    }

    #endregion

    #region 顔テンプレート削除要求

    /// <summary>
    /// 顔テンプレート削除要求
    /// </summary>
    [System.Serializable]
    public class FaceTemplateDelReqData : ReqResDataBass
    {
        #region Fields
        private int _accountId = 0;
        #endregion

        #region Properties
        /// <summary>レコードID</summary>
        public int AccountId
        {
            get { return _accountId; }
            set { _accountId = value; }
        }

        #endregion

        #region Constructors
        public FaceTemplateDelReqData()
        {
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);

            // XMLからアカウントIDを取り出す
            var strAccountId = base.GetDocumentValue(nameof(this.AccountId));

            if (int.TryParse(strAccountId, out int intAccountId)
                && intAccountId > 0)

            {
                this.AccountId = intAccountId;
            }
            else
            {
                throw new ArgumentException("職員IDが不正です。");
            }
        }
        #endregion
    }

    #endregion

    #region 顔テンプレート削除応答

    /// <summary>
    /// 顔テンプレート削除応答
    /// </summary>
    public class FaceTemplateDelResData : ReqResDataBass
    {
        #region Fields
        private EnumProcResult _procResult = 0;
        private EnumErrorId _errorId = 0;
        #endregion

        #region Properties
        /// <summary>処理結果</summary>
        public EnumProcResult ProcResult
        {
            get { return _procResult; }
            set { _procResult = value; }
        }

        /// <summary>エラーID</summary>
        public EnumErrorId ErrorId
        {
            get { return _errorId; }
            set { _errorId = value; }
        }
        #endregion

        #region Constructors
        public FaceTemplateDelResData()
        {
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);

            // XMLから処理結果を取り出す
            var strProcResult = base.GetDocumentValue(nameof(this.ProcResult));

            if (!int.TryParse(strProcResult, out int procResult)
                || !Enum.IsDefined(typeof(EnumProcResult), procResult))
            {
                Trace.OutputErrorTrace("処理結果の値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                return;
            }

            ProcResult = (EnumProcResult)procResult;

            // XMLからエラーIDを取り出す
            var strErrorId = base.GetDocumentValue(nameof(this.ErrorId));

            if (!int.TryParse(strErrorId, out int errorId)
                || !Enum.IsDefined(typeof(EnumErrorId), errorId))
            {
                Trace.OutputErrorTrace("エラーIDの値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                return;
            }

            ErrorId = (EnumErrorId)errorId;
        }
        #endregion
    }

    #endregion

    #region 顔テンプレート追加更新要求

    /// <summary>
    /// 顔テンプレート追加更新要求
    /// </summary>
    [System.Serializable]
    public class FaceTemplateAddUpdateReqData : ReqResDataBass
    {
        #region Fields
        private int _recordId = 0;
        private int _collationCount = 0;
        #endregion

        #region Properties
        /// <summary>レコードID</summary>
        public int RecordId
        {
            get { return _recordId; }
            set { _recordId = value; }
        }
        /// <summary>照合回数</summary>
        public int CollationCount
        {
            get { return _collationCount; }
            set { _collationCount = value; }
        }

        #endregion

        #region Constructors
        public FaceTemplateAddUpdateReqData()
        {
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);
        }
        #endregion
    }

    #endregion

    #region 顔テンプレート追加更新応答

    /// <summary>
    /// 顔テンプレート追加更新応答
    /// </summary>
    public class FaceTemplateAddUpdateResData : ReqResDataBass
    {
        #region Fields
        private bool _returnCode = false;
        #endregion

        #region Properties
        /// <summary>応答コード</summary>
        public bool ReturnCode
        {
            get { return _returnCode; }
            set { _returnCode = value; }
        }
        #endregion

        #region Constructors
        public FaceTemplateAddUpdateResData()
        {
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);
        }
        #endregion
    }

    #endregion

    #region 本人確認・顔テンプレート登録確認要求

    /// <summary>
    /// 本人確認・顔テンプレート登録確認要求
    /// </summary>
    [System.Serializable]
    public class IdentificationFaceTemplateRegConfirmReqData : ReqResDataBass
    {
        #region Fields
        private string _logonId = string.Empty;
        private string _password = string.Empty;
        #endregion

        #region Properties
        /// <summary>職員ID</summary>
        public string LogonId
        {
            get { return _logonId; }
            set { _logonId = value; }
        }
        /// <summary>パスワード</summary>
        public string Password
        {
            get { return _password; }
            set { _password = value; }
        }

        #endregion

        #region Constructors
        public IdentificationFaceTemplateRegConfirmReqData()
        {
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);

            // XMLから職員IDを取り出す
            this.LogonId = base.GetDocumentValue(nameof(this.LogonId));

            if (string.IsNullOrWhiteSpace(this.LogonId) == true)
            {
                throw new ArgumentException("職員IDが不正です。");
            }

            // XMLからパスワードを取り出す
            this.Password = base.GetDocumentValue(nameof(this.Password));

            if (string.IsNullOrWhiteSpace(this.Password))
            {
                throw new ArgumentException("パスワードが不正です。");
            }
        }
        #endregion
    }

    #endregion

    #region 本人確認・顔テンプレート登録確認応答

    /// <summary>
    /// 本人確認・顔テンプレート登録確認応答
    /// </summary>
    public class IdentificationFaceTemplateRegConfirmResData : ReqResDataBass
    {
        #region Fields
        private EnumProcResult _procResult = 0;
        private EnumErrorId _errorId = 0;
        private EnumIdentificationResult _identificationResult = 0;
        private EnumRegistState _registState = 0;
        private int _accountId = 0;
        private string _userName = string.Empty;
        #endregion

        #region Properties
        /// <summary>処理結果</summary>
        public EnumProcResult ProcResult
        {
            get { return _procResult; }
            set { _procResult = value; }
        }

        /// <summary>エラーID</summary>
        public EnumErrorId ErrorId
        {
            get { return _errorId; }
            set { _errorId = value; }
        }
        /// <summary>本人認証結果</summary>
        public EnumIdentificationResult IdentificationResult
        {
            get { return _identificationResult; }
            set { _identificationResult = value; }
        }
        /// <summary>登録状態</summary>
        public EnumRegistState RegistState
        {
            get { return _registState; }
            set { _registState = value; }
        }
        /// <summary>職員ID</summary>
        public int AccountId
        {
            get { return _accountId; }
            set { _accountId = value; }
        }
        /// <summary>職員名</summary>
        public string UserName
        {
            get { return _userName; }
            set { _userName = value; }
        }
        #endregion

        #region Constructors
        public IdentificationFaceTemplateRegConfirmResData()
        {
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);

            // XMLから処理結果を取り出す
            var strProcResult = base.GetDocumentValue(nameof(this.ProcResult));

            if (!int.TryParse(strProcResult, out int procResult)
                || !Enum.IsDefined(typeof(EnumProcResult), procResult))
            {
                Trace.OutputErrorTrace("処理結果の値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                IdentificationResult = EnumIdentificationResult.AuthNG;
                RegistState = EnumRegistState.NoRegist;
                return;
            }

            ProcResult = (EnumProcResult)procResult;

            // XMLからエラーIDを取り出す
            var strErrorId = base.GetDocumentValue(nameof(this.ErrorId));

            if (!int.TryParse(strErrorId, out int errorId)
                || !Enum.IsDefined(typeof(EnumErrorId), errorId))
            {
                Trace.OutputErrorTrace("エラーIDの値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                IdentificationResult = EnumIdentificationResult.AuthNG;
                RegistState = EnumRegistState.NoRegist;
                return;
            }

            ErrorId = (EnumErrorId)errorId;

            // XMLから本人認証結果を取り出す
            var strIdentificationResult = base.GetDocumentValue(nameof(this.IdentificationResult));

            if (!int.TryParse(strIdentificationResult, out int identificationResult)
                || !Enum.IsDefined(typeof(EnumIdentificationResult), identificationResult))
            {
                Trace.OutputErrorTrace("本人認証結果の値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                IdentificationResult = EnumIdentificationResult.AuthNG;
                RegistState = EnumRegistState.NoRegist;
                return;
            }

            IdentificationResult = (EnumIdentificationResult)identificationResult;

            // XMLから登録状態を取り出す
            var strRegistState = base.GetDocumentValue(nameof(this.RegistState));

            if (!int.TryParse(strRegistState, out int registState)
                || !Enum.IsDefined(typeof(EnumRegistState), registState))
            {
                Trace.OutputErrorTrace("登録状態の値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                IdentificationResult = EnumIdentificationResult.AuthNG;
                RegistState = EnumRegistState.NoRegist;
                return;
            }

            RegistState = (EnumRegistState)registState;

            if (ProcResult == EnumProcResult.Success
                && IdentificationResult == EnumIdentificationResult.AuthOK)
            {
                // XMLからアカウントIDを取り出す
                var strAccountId = base.GetDocumentValue(nameof(this.AccountId));

                if (int.TryParse(strAccountId, out int intAccountId))

                {
                    this.AccountId = intAccountId;
                }
                else
                {
                    Trace.OutputErrorTrace("職員IDが不正です。");
                    ProcResult = EnumProcResult.Fail;
                    ErrorId = EnumErrorId.Exception;
                    IdentificationResult = EnumIdentificationResult.AuthNG;
                    RegistState = EnumRegistState.NoRegist;
                }

                // XMLから職員名を取り出す
                this.UserName = base.GetDocumentValue(nameof(this.UserName));
            }
        }
        #endregion
    }

    #endregion

    #region ユーザ検索顔認証要求

    /// <summary>
    /// ユーザ検索顔認証要求
    /// </summary>
    [System.Serializable]
    public class UserSearchFaceAuthReqData : ReqResDataBass
    {
        #region Fields
        private byte[] _image = null;
        #endregion

        #region Properties
        /// <summary>画像データ</summary>
        public byte[] Image
        {
            get { return _image; }
            set { _image = value; }
        }
        #endregion

        #region Constructors
        public UserSearchFaceAuthReqData()
        {
        }
        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);

            // XMLから画像を取り出す
            var strImage = base.GetDocumentValue(nameof(this.Image));
            this.Image = Convert.FromBase64String(strImage);

            if (Image.Length < 1)
            {
                throw new ArgumentException("画像データが不正です。");
            }
        }
        #endregion
    }

    #endregion

    #region ユーザ検索顔認証応答

    /// <summary>
    /// ユーザ検索顔認証応答
    /// </summary>
    public class UserSearchFaceAuthResData : ReqResDataBass
    {
        #region Fields
        private EnumProcResult _procResult = 0;
        private EnumErrorId _errorId = 0;
        private EnumAuthResult _authResult = 0;
        private LoginUserInfo _userInfo;
        #endregion

        #region Properties
        /// <summary>処理結果</summary>
        public EnumProcResult ProcResult
        {
            get { return _procResult; }
            set { _procResult = value; }
        }
        /// <summary>エラーID</summary>
        public EnumErrorId ErrorId
        {
            get { return _errorId; }
            set { _errorId = value; }
        }
        /// <summary>認証結果</summary>
        public EnumAuthResult AuthResult
        {
            get { return _authResult; }
            set { _authResult = value; }
        }
        /// <summary>職員ID</summary>
        public string LogonId
        {
            get { return this._userInfo.logon_id; }
        }
        /// <summary>職員名</summary>
        public string UserName
        {
            get { return this._userInfo.name; }
        }
        /// <summary>アカウントID</summary>
        public int AccountId
        {
            get { return this._userInfo.account_id; }
        }
        /// <summary>ユーザID</summary>
        public int UserId
        {
            get { return this._userInfo.user_id; }
        }
        public LoginUserInfo UserInfo
        {
            get { return _userInfo; }
        }
        #endregion

        #region Constructors
        public UserSearchFaceAuthResData()
        {
            this._userInfo = new LoginUserInfo();
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);

            // XMLから処理結果を取り出す
            var strProcResult = base.GetDocumentValue(nameof(this.ProcResult));

            if (!int.TryParse(strProcResult, out int procResult)
                || !Enum.IsDefined(typeof(EnumProcResult), procResult))
            {
                Trace.OutputErrorTrace("処理結果の値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                AuthResult = EnumAuthResult.AuthNG;
                return;
            }

            ProcResult = (EnumProcResult)procResult;

            // XMLからエラーIDを取り出す
            var strErrorId = base.GetDocumentValue(nameof(this.ErrorId));

            if (!int.TryParse(strErrorId, out int errorId)
                || !Enum.IsDefined(typeof(EnumErrorId), errorId))
            {
                Trace.OutputErrorTrace("エラーIDの値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                AuthResult = EnumAuthResult.AuthNG;
                return;
            }

            ErrorId = (EnumErrorId)errorId;

            // XMLから本人認証結果を取り出す
            var strAuthResult = base.GetDocumentValue(nameof(this.AuthResult));

            if (!int.TryParse(strAuthResult, out int authResult)
                || !Enum.IsDefined(typeof(EnumAuthResult), authResult))
            {
                Trace.OutputErrorTrace("認証結果の値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                AuthResult = EnumAuthResult.AuthNG;
                return;
            }

            AuthResult = (EnumAuthResult)authResult;

            if (ProcResult == EnumProcResult.Success)
            {
                // XMLから職員IDを取り出す
                this._userInfo.logon_id = base.GetDocumentValue(nameof(this.LogonId));

                // XMLから職員名を取り出す
                this._userInfo.name = base.GetDocumentValue(nameof(this.UserName));

                // XMLからアカウントIDを取り出す
                var strAccountRecordId = base.GetDocumentValue(nameof(this.AccountId));

                if (!int.TryParse(strAccountRecordId, out int accountRecordId)
                    || accountRecordId <= 0)
                {
                    Trace.OutputErrorTrace("アカウントIDの値が不正です。");
                    ProcResult = EnumProcResult.Fail;
                    ErrorId = EnumErrorId.Exception;
                    AuthResult = EnumAuthResult.AuthNG;
                    return;
                }

                this._userInfo.account_id = accountRecordId;

                // XMLからユーザIDを取り出す
                var strUserRecordId = base.GetDocumentValue(nameof(this.UserId));

                if (!int.TryParse(strUserRecordId, out int userRecordId)
                    || userRecordId <= 0)
                {
                    Trace.OutputErrorTrace("ユーザIDの値が不正です。");
                    ProcResult = EnumProcResult.Fail;
                    ErrorId = EnumErrorId.Exception;
                    AuthResult = EnumAuthResult.AuthNG;
                    return;
                }

                this._userInfo.user_id = userRecordId;
            }
        }
        #endregion
    }

    #endregion

    #region 顔認証ログ登録(クライアント認証モード)要求

    /// <summary>
    /// 顔認証ログ登録(クライアント認証モード)要求
    /// </summary>
    [System.Serializable]
    public class FaceAuthLogRegistReqData : ReqResDataBass
    {
        #region Fields
        private int _recordId = 0;
        private int _collationCount = 0;
        #endregion

        #region Properties
        /// <summary>レコードID</summary>
        public int RecordId
        {
            get { return _recordId; }
            set { _recordId = value; }
        }
        /// <summary>照合回数</summary>
        public int CollationCount
        {
            get { return _collationCount; }
            set { _collationCount = value; }
        }

        #endregion

        #region Constructors
        public FaceAuthLogRegistReqData()
        {
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);
        }
        #endregion
    }

    #endregion

    #region 顔認証ログ登録(クライアント認証モード)応答

    /// <summary>
    /// 顔認証ログ登録(クライアント認証モード)応答
    /// </summary>
    public class FaceAuthLogRegistResData : ReqResDataBass
    {
        #region Fields
        private bool _returnCode = false;
        #endregion

        #region Properties
        /// <summary>応答コード</summary>
        public bool ReturnCode
        {
            get { return _returnCode; }
            set { _returnCode = value; }
        }
        #endregion

        #region Constructors
        public FaceAuthLogRegistResData()
        {
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);
        }
        #endregion
    }

    #endregion

    #region なりすまし画像登録要求

    /// <summary>
    /// なりすまし画像登録要求
    /// </summary>
    [System.Serializable]
    public class LivenessReqData : ReqResDataBass
    {
        #region Fields
        private int _account_Id = 0;
        private byte[] _image = null;
        #endregion

        #region Properties
        /// <summary>職員ID</summary>
        public int AccountId
        {
            get { return _account_Id; }
            set { _account_Id = value; }
        }

        /// <summary>画像データ</summary>
        public byte[] Image
        {
            get { return _image; }
            set { _image = value; }
        }
        #endregion

        #region Constructors
        public LivenessReqData()
        {
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);

            // XMLからアカウントIDを取り出す
            var strAccountId = base.GetDocumentValue(nameof(this.AccountId));

            if (int.TryParse(strAccountId, out int intAccountId)
                && intAccountId > 0)
            {
                this.AccountId = intAccountId;
            }
            else
            {
                throw new ArgumentException("職員IDが不正です。");
            }

            // XMLから画像を取り出す
            var strImage = base.GetDocumentValue(nameof(this.Image));

            this.Image = Convert.FromBase64String(strImage);

            if (Image.Length < 1)
            {
                throw new ArgumentException("画像が不正です。");
            }
        }
        #endregion
    }

    #endregion

    #region なりすまし画像登録応答

    /// <summary>
    /// なりすまし画像登録応答
    /// </summary>
    public class LivenessResData : ReqResDataBass
    {
        #region Fields
        private EnumProcResult _procResult = 0;
        private EnumErrorId _errorId = 0;
        private EnumAuthResult _authResult = 0;
        #endregion

        #region Properties
        /// <summary>処理結果</summary>
        public EnumProcResult ProcResult
        {
            get { return _procResult; }
            set { _procResult = value; }
        }

        /// <summary>エラーID</summary>
        public EnumErrorId ErrorId
        {
            get { return _errorId; }
            set { _errorId = value; }
        }
        /// <summary>認証結果</summary>
        public EnumAuthResult AuthResult
        {
            get { return _authResult; }
            set { _authResult = value; }
        }
        #endregion

        #region Constructors
        public LivenessResData()
        {
        }

        #endregion

        #region Public メソッド
        /// <summary>
        /// クラス内で保持しているデータをCSVに変換する
        /// </summary>
        /// <returns>CSVデータ</returns>
        public string ToCsv()
        {
            string csv = string.Empty;

            return csv;
        }

        /// <summary>
        /// 受信データからこのクラスのデータを作成する
        /// </summary>
        /// <param name="rawData">受信データ</param>
        public void ToObject(string rawData)
        {
            // 受信データからXmlデータを取得する
            base.GetXmlDocumentValue(rawData);

            // XMLから処理結果を取り出す
            var strProcResult = base.GetDocumentValue(nameof(this.ProcResult));

            if (!int.TryParse(strProcResult, out int procResult)
                || !Enum.IsDefined(typeof(EnumProcResult), procResult))
            {
                Trace.OutputErrorTrace("処理結果の値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                return;
            }

            ProcResult = (EnumProcResult)procResult;

            // XMLからエラーIDを取り出す
            var strErrorId = base.GetDocumentValue(nameof(this.ErrorId));

            if (!int.TryParse(strErrorId, out int errorId)
                || !Enum.IsDefined(typeof(EnumErrorId), errorId))
            {
                Trace.OutputErrorTrace("エラーIDの値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                return;
            }

            ErrorId = (EnumErrorId)errorId;

            // XMLから認証結果を取り出す
            var strAuthResult = base.GetDocumentValue(nameof(this.AuthResult));

            if (!int.TryParse(strAuthResult, out int authResult)
                || !Enum.IsDefined(typeof(EnumAuthResult), authResult))
            {
                Trace.OutputErrorTrace("認証結果の値が不正です。");
                ProcResult = EnumProcResult.Fail;
                ErrorId = EnumErrorId.Exception;
                return;
            }

            AuthResult = (EnumAuthResult)authResult;
        }
        #endregion
    }

    #endregion


    #region リクエスト・レスポンスベースクラス

    /// <summary>
    /// リクエスト・レスポンスベースクラス
    /// </summary>
    public class ReqResDataBass
    {
        /// <summary>
        /// "Contents" 属性名
        /// </summary>
        public static readonly string CONTENTS = "Contents";

        /// <summary>
        /// value 属性名
        /// </summary>
        public static readonly string VALUE = "value";

        /// <summary>
        /// XMLドキュメント
        /// </summary>
        private XmlDocument xmlDoc;

        /// <summary>
        /// XMLドキュメント
        /// </summary>
        private XmlDocument XmlDocumentValue { get { return xmlDoc ?? new XmlDocument(); } }

        /// <summary>
        /// 受信データからXmlデータを取得
        /// </summary>
        /// <param name="rawData"></param>
        protected void GetXmlDocumentValue(string rawData)
        {
            var recvDataTemp = eDoktor.Taikoban.FaceCrypt.Crypto.Decrypt(rawData);
            var temp = System.Text.Encoding.UTF8.GetBytes(recvDataTemp);
            var recvData = Util.ConvertEncodeling(temp, System.Text.Encoding.UTF8);

            this.xmlDoc = new XmlDocument();
            this.xmlDoc.LoadXml(recvData);
        }

        /// <summary>
        /// XMLドキュメントから指定した名前の一件目のノードの指定した名称の属性の値を取得する
        /// </summary>
        /// <param name="nodeName">ノードのタグ名</param>
        /// <returns></returns>
        protected string GetDocumentValue(string nodeName)
        {
            var value = string.Empty;
            var xmlNode = this.GetNode(nodeName);

            if (xmlNode != null)
            {
                value = xmlNode.Attributes[VALUE]?.Value ?? string.Empty;
            }

            Trace.OutputDebugTrace("{0}:{1}", nodeName, value);

            return value;
        }

        /// <summary>
        /// 対象のノードの指定した名称の属性の値を取得する
        /// </summary>
        /// <param name="node">XMLノード</param>
        /// <param name="nodeAttrName">取得する設定の名前(属性名)</param>
        /// <returns>設定値</returns>
        protected string GetNodeValue(XmlNode node, string nodeAttrName)
        {
            var value = node.Attributes[nodeAttrName]?.Value ?? string.Empty;
            Trace.OutputDebugTrace("{0}:{1}", nodeAttrName, value);

            return value;
        }

        /// <summary>
        /// 指定したの名前のノードリストを取得する
        /// </summary>
        /// <param name="nodeName">取得するノードのタグ名</param>
        /// <returns>XMLノード</returns>
        protected XmlNodeList GetNodeList(string nodeName)
        {
            // XMLからタグ名を指定してノードリストを取り出す
            var xmlNodeList = this.XmlDocumentValue.GetElementsByTagName(nodeName);

            return xmlNodeList;
        }

        /// <summary>
        /// 指定したの名前の一件目のノードを取得する
        /// </summary>
        /// <param name="nodeName">取得するノードのタグ名</param>
        /// <returns>XMLノード</returns>
        protected XmlNode GetNode(string nodeName)
        {
            XmlNode value = null;
            // XMLからタグ名を指定してノードリストを取り出す
            var xmlNodeList = this.GetNodeList(nodeName);

            if (xmlNodeList.Count > 0)
            {
                // ノードリストが取得できている場合、1件目のノードを取得する
                value = xmlNodeList[0];
            }

            return value;
        }
    }

    #endregion
}

