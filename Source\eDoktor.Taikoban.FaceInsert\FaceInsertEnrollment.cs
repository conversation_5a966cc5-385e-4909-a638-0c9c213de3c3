﻿//#define TRY_TEST_MODE // テスト中の処理を確認したい場合は、有効にすること

using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using eDoktorTaikoban.FaceClientCommon;

namespace eDoktor.Taikoban.FaceInsert
{
    /// <summary>
    /// 登録確認画面(ファイル選択画面と撮影登録画面の共有画面)
    /// </summary>
    public partial class FaceInsertEnrollment : Form
    {
        #region Private Fields

        /// <summary>
        /// ユーザ情報
        /// </summary>
        private int _accountId;

        /// <summary>
        /// 取得した画像データ
        /// </summary>
        private Bitmap _bitmapImageData;

        /// <summary>
        /// 取得した画像データ
        /// </summary>
        private byte[] _imageDataArray;

        /// <summary>
        /// 取得した画像データ
        /// </summary>
        private byte[] ImageDataArray { get { return this._imageDataArray ?? this.GetImageData(); } }

        /// <summary>
        /// 設定情報
        /// </summary>
        private SettingsInfo _settingInfo;

        /// <summary>
        /// 顔認識状態表示用タイマー
        /// </summary>
        private System.Windows.Forms.Timer _faceDetectionStateTimer = null;

        /// <summary>
        /// なりすまし判定処理クラス
        /// </summary>
        private FaceDetection.LivenessCheckDetection _livenessCheckDetection = null;

        /// <summary>
        /// ボタンアクションクラス
        /// </summary>
        BtnImageCahnge _btnImageCahnge = new BtnImageCahnge();
        #endregion

        /// <summary>
        /// 登録成功フラグ
        /// </summary>
        public bool IsSuccess { get; private set; } = false;

        #region Constructors

        /// <summary>
        /// コンストラクタ（ファイル選択画面からの遷移時）
        /// </summary>
        /// <param name="accountId">アカウントID</param>
        /// <param name="logonId">ログオンID</param>
        /// <param name="userName">ユーザ名</param>
        /// <param name="imageData">画像データ</param>
        /// <param name="settingInfo">設定情報</param>
        public FaceInsertEnrollment(int accountId, string logonId, string userName, byte[] imageData, SettingsInfo settingInfo)
        : this(accountId, logonId, userName, settingInfo)
        {
            this._imageDataArray = imageData;

            // ファイル選択画面から遷移した際は、キャンバスに画像を表示する
            this.SetImage(imageData);
        }

        /// <summary>
        /// コンストラクタ（撮影登録画面からの遷移時）
        /// </summary>
        /// <param name="accountId">アカウントID</param>
        /// <param name="logonId">ログオンID</param>
        /// <param name="userName">ユーザ名</param>
        /// <param name="bitmap">撮影画像</param>
        /// <param name="settingInfo">設定情報</param>
        /// <param name="isFlipHorizon">カメラ画像反転フラグ(true時反転)</param>
        public FaceInsertEnrollment(int accountId, string logonId, string userName, Bitmap bitmap, SettingsInfo settingInfo, bool isFlipHorizon, Timer faceDetectionStateTimer, FaceDetection.LivenessCheckDetection livenessCheckDetection)
        : this(accountId, logonId, userName, settingInfo)
        {
            this._imageDataArray = null;
            this._bitmapImageData = (Bitmap)bitmap.Clone();

            if(this._faceDetectionStateTimer == null)
            {
                this._faceDetectionStateTimer = faceDetectionStateTimer;
            }
            if(this._livenessCheckDetection == null)
            {
                this._livenessCheckDetection = livenessCheckDetection;
            }

            if (isFlipHorizon)
            {
                bitmap.RotateFlip(RotateFlipType.RotateNoneFlipX);
            }

            // 撮影時の画像を設定する
            this.SetImage(bitmap);
        }

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="accountId">アカウントID</param>
        /// <param name="logonId">ログオンID</param>
        /// <param name="userName">ユーザ名</param>
        /// <param name="settingInfo">設定情報</param>
        private FaceInsertEnrollment(int accountId, string logonId, string userName, SettingsInfo settingInfo)
        {
            InitializeComponent();

            // 職員IDと職員名を取得する
            this.lbStaffId.Text = logonId;
            this.lbStaffName.Text = userName;
            this._accountId = accountId;
            this._settingInfo = settingInfo;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// フォームロード
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FileEnrollment_Load(object sender, EventArgs e)
        {

        }

        /// <summary>
        /// 画像を設定する
        /// </summary>
        /// <param name="bitmap">ビットマップデータ</param>
        public void SetImage(Bitmap bitmap)
        {
            lock (this)
            {
                Bitmap old = this.pictureBox1.Image == null ? null : (Bitmap)this.pictureBox1.Image;

                // 画面の表示領域に入るように調整する高さと幅を計算
                var height = this.pictureBox1.Height;
                var width = this.pictureBox1.Width;

                var location = this.pictureBox1.Location;

                // 画面の表示領域に入るように調整
                ClientCommonProc.PictureBoxResize(bitmap.Size, ref width, ref height, ref location);
                this.pictureBox1.Location = location;
                this.pictureBox1.Width = width;
                this.pictureBox1.Height = height;
                this.pictureBox1.Image = bitmap;

                if (old != null)
                {
                    old.Dispose();
                }
            }
        }

        /// <summary>
        /// 画像を設定する
        /// </summary>
        /// <param name="imageData">画像データ</param>
        private void SetImage(byte[] imageData)
        {
            Bitmap bmp = null;

            using (var ms = new MemoryStream(imageData))
            {
                bmp = new Bitmap(ms);
                ms.Close();
            }

            this._bitmapImageData = (Bitmap)bmp.Clone();
            var width = bmp.Width;
            var height = bmp.Height;

#if TRY_TEST_MODE
            int resizeWidth;
            int resizeHeight;

            if (width > height)
            {
                // 選択された画像をリサイズする
                resizeWidth = 640;
                //            resizeHeight = (int)(bmp.Height * ((double)resizeWidth / (double)bmp.Width));
                resizeHeight = 480;
            }
            else
            {
                // 選択された画像をリサイズする
                resizeWidth = 480;
//                resizeHeight = (int)(bmp.Height * ((double)resizeWidth / (double)bmp.Width));
                resizeHeight = 640;
            }

            Bitmap resizeBmp = new Bitmap(resizeWidth, resizeHeight);

            Graphics g = Graphics.FromImage(resizeBmp);
            g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
            g.DrawImage(bmp, 0, 0, resizeWidth, resizeHeight);
            g.Dispose();

            this.SetImage(resizeBmp);
#else
            this.SetImage(bmp);
#endif // TRY_TEST_MODE

        }

        /// <summary>
        /// 画面に表示しているイメージから画像データを取得する
        /// </summary>
        /// <returns></returns>
        private byte[] GetImageData()
        {
            // リサイズした画像データを取得する
            using (var memoryStream = new MemoryStream())
            {
                this._bitmapImageData.Save(memoryStream, System.Drawing.Imaging.ImageFormat.Png);

                // 画像データを取得する
                return memoryStream.GetBuffer();
            }
        }

        /// <summary>
        /// 登録ボタン押下処理(共通処理)
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnEnrollment_Click(object sender, EventArgs e)
        {
            if (this._settingInfo.auth_mode == EnumAuthMode.Server)
            {
                // サーバモード時
                this.ServerModeFaceInsertProc();
            }
            else
            {
                // TODO クライアントモードの処理
                // クライアントモード時
                // モードの設定が不正です
                MessageBox.Show(Messages.Message(MessageId.Message011));
            }

            this.Close();
        }

        /// <summary>
        /// キャンセルボタン押下処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            if(this._livenessCheckDetection != null && this._faceDetectionStateTimer != null)
            {
                this._livenessCheckDetection.ReStart();
                this._faceDetectionStateTimer.Enabled = true;
            }
            this.Close();
        }

        /// <summary>
        /// サーバモード時登録処理
        /// </summary>
        private void ServerModeFaceInsertProc()
        {
            // base64で暗号化する。
            var imageDataStr = Convert.ToBase64String(this.ImageDataArray);

            var requestSend = new RequestSendClass();

            // 顔テンプレート登録要求(サーバー認証モード)を送信する
            var res = requestSend.RequestFaceTemplateRegistServerMode(this._accountId, imageDataStr, out FaceInterprocess.FaceTemplateRegServerResData resData);

            if (!res)
            {
                // サーバーからの応答がありません。
                Trace.OutputErrorTrace(Messages.Message(MessageId.Message004));
                MessageBox.Show(Messages.Message(MessageId.Message004));
                return;
            }

            if (resData.ProcResult == EnumProcResult.Success)
            {
                // 処理結果が正常終了
                this.IsSuccess = true;
                // 登録が完了しました。
                MessageBox.Show(Messages.Message(MessageId.Message018, this.lbStaffId.Text, this.lbStaffName.Text));
            }
            else
            {
                // 処理結果が異常終了
                var errMsg = ClientCommonProc.ErrorIdToErrorMessage(resData.ErrorId, this.lbStaffId.Text);

                if (string.IsNullOrEmpty(errMsg))
                {
                    errMsg = "処理に失敗しました。";
                }

                MessageBox.Show(errMsg);
            }

            if (this._livenessCheckDetection != null && this._faceDetectionStateTimer != null)
            {
                this._livenessCheckDetection.ReStart();
                this._faceDetectionStateTimer.Enabled = true;
            }
        }

        /// <summary>
        /// マウスカーソルがボタン領域に入った時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseEnter(object sender, EventArgs e)
        {
            _btnImageCahnge.btn_MouseEnter(sender, e);
        }

        /// <summary>
        /// マウスカーソルがボタン領域から抜けた時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseLeave(object sender, EventArgs e)
        {
            _btnImageCahnge.btn_MouseLeave(sender, e);
        }

        /// <summary>
        /// ボタン押下時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            _btnImageCahnge.btn_MouseDown(sender, e);
        }

        #endregion
    }
}
