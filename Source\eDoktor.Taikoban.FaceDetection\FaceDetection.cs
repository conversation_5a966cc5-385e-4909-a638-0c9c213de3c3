﻿#define MEASUREMENT_LOG     // 計測ログ
//#define USE_CASCADE_ALT3    // カスケードファイルとして alt3を使用する(検出条件が厳しいみたいだ)
//                            // USE_CASCADE_ALT3を使用する時にはカスケードファイルを実行ファイルにコピーしてください。
using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Drawing;
using System.Threading;
using OpenCvSharp;
using OpenCvSharp.Extensions;
using OpenCvSharp.Face;
using eDoktor.Taikoban.FaceAuthSettingsInfo;

namespace eDoktor.Taikoban.FaceDetection
{
    /// <summary>
    /// 顔検出クラス
    /// </summary>
    public class FaceDetection
    {

        #region プロパティー
        /// <summary>
        /// 顔検出用画像を格納するキュー
        /// </summary>
        public ConcurrentQueue<Bitmap> ImageQueue
        {
            private set { _imageQueue = value; }
            get { return _imageQueue; }
        }

        /// <summary>
        /// 顔検出間隔(ms) 
        /// </summary>
        public int FaceCheckInterval
        {
            set
            {
                if (value < CST_DEFAULT_EXECUTION_INTERVAL)
                {
                    _faceCheckInterval = CST_DEFAULT_EXECUTION_INTERVAL;
                }
                else
                {
                    _faceCheckInterval = value;
                }
            }
            get 
            {
                return _faceCheckInterval; 
            }
        }

        /// <summary>
        /// 検出した顔の矩形情報
        /// </summary>
        public Rect[] FaceRectangles
        {
            private set
            {
                lock(_lockObj)
                {
                    _faceRect = value;
                }
            }
            get
            {
                lock(_lockObj)
                {
                    Rect[] tmpRect = (Rect[])_faceRect.Clone();
                    return tmpRect;
                }
            } 
        }

        /// <summary>
        /// 検出した目の矩形情報
        /// </summary>
        public Rect[] EyesRectangles
        {
            private set
            {
                lock (_lockObjEyes)
                {
                    _eyesRect = value;
                }
            }
            get
            {
                lock (_lockObjEyes)
                {
                    Rect[] tmpRect = (Rect[])_eyesRect.Clone();
                    return tmpRect;
                }
            }
        }

        /// <summary>
        /// まばたきをしたかどうかフラグ（FaceClientFormMainで判断用）
        /// </summary>
        public bool BlinkFlg
        {
            get
            {
                lock (_lockObjEyes)
                {
                    return _blinkFlg;
                }
            }
        }
        /// <summary>
        /// まばたき回数
        /// </summary>
        public int BlinkCount
        {
            get
            {
                lock (_lockObjEyes)
                {
                    return _blinkCount;
                }
            }
        }
        /// <summary>
        /// 目の個数
        /// </summary>
        public int EyeCount
        {
            get
            {
                lock (_lockObjEyes)
                {
                    return _eyeCount;
                }
            }
        }
        /// <summary>
        /// まばたき検知開始フラグ
        /// </summary>
        public bool IsReady
        {
            get
            {
                lock (_lockObjEyes)
                {
                    return _isReady;
                }
            }
        }
        /// <summary>
        /// 目（顔）の大きさ（カメラからの遠さ）
        /// </summary>
        public int Size
        {
            get
            {
                lock (_lockObjEyes)
                {
                    return _size;
                }
            }
        }
        /// <summary>
        /// まばたき開始待機用タイマー
        /// </summary>
        public Stopwatch WaitingTimer
        {
            get
            {
                lock (_lockObjEyes)
                {
                    return _waitingTimer;
                }
            }
        }
        #endregion

        #region コンスタント
#if USE_CASCADE_ALT3
        /// <summary>
        /// カスケード分類機XMLファイル
        /// </summary>
        private const string CASCADE_XML = "haarcascade_frontalface_alt_tree.xml";
        private const string CASCADE_EYES_XML = "haarcascade_eye_tree_eyeglasses.xml";
#else
        /// <summary>
        /// カスケード分類機XMLファイル(eDoktorTaikoban.FaceClientCommonにファイルあり)
        /// </summary>
        private const string CASCADE_XML = "haarcascade_frontalface_default.xml";
        private const string CASCADE_EYES_XML = "haarcascade_eye_tree_eyeglasses.xml";
#endif
        /// <summary>
        /// 省略時顔検出実行間隔(単位はms)
        /// </summary>
        private const int CST_DEFAULT_EXECUTION_INTERVAL = 100;
        #endregion

        #region フィールド

        /// <summary>
        /// まばたきをしたかどうかフラグ（FaceClientFormMainで判断用）
        /// </summary>
        private bool _blinkFlg = false;
        /// <summary>
        /// まばたき回数
        /// </summary>
        private int _blinkCount = 0;
        /// <summary>
        /// 目の個数
        /// </summary>
        private int _eyeCount = 0;
        /// <summary>
        /// まばたき検知開始フラグ
        /// </summary>
        private bool _isReady = false;
        /// <summary>
        /// 目（顔）の大きさ（カメラからの遠さ）
        /// </summary>
        private int _size = 0;
        /// <summary>
        /// まばたき開始待機用タイマー
        /// </summary>
        private Stopwatch _waitingTimer = new System.Diagnostics.Stopwatch();

        private object _lockObj = new object();
        private object _lockObjEyes = new object();
        /// <summary>
        /// カスケード分類子
        /// </summary>
        private CascadeClassifier _cascadeClassifier;
        private CascadeClassifier _cascadeClassifierEyes;
        /// <summary>
        /// スレッド実行中フラグ(true:実行中 false:非実行)
        /// </summary>
        private bool _executing = false;
        /// <summary>
        /// 顔識別処理実行中フラグ(true:実行中 false:
        /// </summary>
        private bool _busy = false;
        private bool _executable = false;
        /// <summary>
        /// 顔検出に使用するビットマップを格納するコンカレントQueue
        /// </summary>
        private ConcurrentQueue<Bitmap> _imageQueue = null;
        private int _faceCheckInterval = 0;
        private System.Threading.Timer _faceDetectionTimer = null;
        private Rect[] _faceRect;
        private Rect[] _eyesRect;
        /// <summary>
        /// まばたき時間用タイマー
        /// </summary>
        private Stopwatch _blinkingTimer = new System.Diagnostics.Stopwatch();
        /// <summary>
        /// まばたきローカルフラグ（ループの中で判断するために使用）
        /// </summary>
        private bool _blinkFlgForLoop = false;
        /// <summary>
        /// 設定情報
        /// </summary>
        private SettingsInfo _settingsInfo = new SettingsInfo();
        #endregion

        #region コンストラクタ
        public FaceDetection(SettingsInfo settingsInfo)
        {
            try
            {
                _executing = false;
                _imageQueue = new ConcurrentQueue<Bitmap>();
                ImageQueue = _imageQueue;
                FaceCheckInterval = CST_DEFAULT_EXECUTION_INTERVAL;
                TimerCallback timerDelegate = new TimerCallback(ExecFaceDetection);
                _faceDetectionTimer = new System.Threading.Timer(timerDelegate);
                _cascadeClassifier = new CascadeClassifier(CASCADE_XML);
                _cascadeClassifierEyes = new CascadeClassifier(CASCADE_EYES_XML);
                _executable = true;
                _busy = false;
                FaceRectangles = new Rect[0];
                EyesRectangles = new Rect[0];
                _settingsInfo = settingsInfo;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
                _executable = false;
            }
        }
#endregion
        /// <summary>
        /// 顔検出を開始する。
        /// </summary>
        /// <param name="interval">顔検出間隔</param>
        /// <returns>true:開始しました false:開始せず</returns>
        public bool Start(int interval)
        {
            FaceCheckInterval = interval;
            return Start();
        }

        /// <summary>
        /// 顔検出を開始する。
        /// </summary>
        /// <returns>true:開始しました false:開始せず</returns>
        public bool Start()
        {
            if (_executable != true)
            {
                return false;
            }
            try
            {
                _faceDetectionTimer.Change(37, FaceCheckInterval);
                _executing = true;
                eDoktor.Common.Trace.OutputTrace("顔検出処理を開始しました。");
                return true;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
                _executing = false;
                return false;
            }
        }

        /// <summary>
        /// 顔検出を休止する。
        /// </summary>
        /// <returns>true:開始しました false:開始せず</returns>
        public bool Stop()
        {
            try
            {
                if (_executable != true)
                {
                    return false;
                }
                if (_executing == true)
                {
                    _faceDetectionTimer.Change(Timeout.Infinite, Timeout.Infinite);
                    eDoktor.Common.Trace.OutputTrace("顔検出処理を休止しました。");
                    _executing = false;
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
                return false;
            }
        }

        /// <summary>
        /// 顔検出を再開する。
        /// </summary>
        /// <returns></returns>
        public bool ReStart()
        {
            try
            {
                if (_executable != true)
                {
                    return false;
                }
                if (_executing != true)
                {
                    _faceDetectionTimer.Change(37, FaceCheckInterval);
                    eDoktor.Common.Trace.OutputTrace("顔検出処理を再開しました。");
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
                return false;
            }
        }

        /// <summary>
        /// 顔検出処理を終了します。
        /// </summary>
        public void End()
        {
            try
            {
                if (_faceDetectionTimer != null)
                {
                    _faceDetectionTimer.Change(Timeout.Infinite, Timeout.Infinite);
                    _faceDetectionTimer.Dispose();
                    _executing = false;
                    _busy = false;
                    _faceDetectionTimer = null;
                }
                if (_cascadeClassifier != null)
                {
                    _cascadeClassifier.Dispose();
                    _cascadeClassifier = null;
                }
                eDoktor.Common.Trace.OutputTrace("顔検出処理を終了しました。");
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
            _executable = false;
        }

        /// <summary>
        /// 顔検出実行中かどうかを返す。
        /// </summary>
        /// <returns>true:実行中 false:未実行</returns>
        public bool IsExecuting()
        {
            return _executing;
        }

        /// <summary>
        /// 顔検出処理を実行します。
        /// </summary>
        /// <param name="o"></param>
        private void ExecFaceDetection(object o)
        {
            if (_cascadeClassifier == null)
            {
                return;
            }
            if (_executable != true || _busy == true)
            {
                return;
            }

            if (_imageQueue.Count <= 0)
            {
                return;
            }
            try
            {

                Bitmap tmpBitmap = null;
                _busy = true;
                while (true)
                {
                    eDoktor.Common.Trace.OutputTrace("------------------------------------ループ処理開始");
                    if (_imageQueue.Count <= 0)
                    {
                        eDoktor.Common.Trace.OutputTrace("------------------------------------_imageQueueがないためBreak");
                        break;
                    }

                    if (_imageQueue.TryDequeue(out tmpBitmap) != true)
                    {
                        eDoktor.Common.Trace.OutputTrace("------------------------------------_imageQueueから画像を取り出せないためBreak");
                        break;
                    }

                    // キューから取出したビットマップで顔検出を行う。
                    //取得先のMat作成
#if MEASUREMENT_LOG
                    var sw = new System.Diagnostics.Stopwatch();
                    sw.Start();
#endif
                    using (var frameMat = BitmapConverter.ToMat(tmpBitmap))
                    {
                        // 処理高速化のため、色をグレーにする
                        var grayMat = new Mat();
                        Cv2.CvtColor(frameMat, grayMat, ColorConversionCodes.BGRA2GRAY);

                        // 顔検出を行う
                        var rects = _cascadeClassifier.DetectMultiScale(grayMat, 1.1, 5, HaarDetectionTypes.ScaleImage, new OpenCvSharp.Size(30, 30));

                        //まばたき検出を行うかどうか
                        if (_settingsInfo.is_use_eye_blink)
                        {
                            //まばたき検出に顔を使用するかどうか
                            if (_settingsInfo.is_use_face_for_blinking)
                            {
                                //まばたき検出に顔検出を使用するパターン

                                //顔が検出できるかどうか
                                if (rects.Length > 0)
                                {
                                    //顔が検出できるとき

                                    //顔の面積を格納
                                    _size = rects[0].Width * rects[0].Height;

                                    // 目検出を行う
                                    var eyesWithFace = _cascadeClassifierEyes.DetectMultiScale(
                                        grayMat.Clone(new Rect(rects[0].X, rects[0].Y, rects[0].Width, rects[0].Height)), 
                                        1.1, 
                                        5, 
                                        HaarDetectionTypes.ScaleImage, 
                                        new OpenCvSharp.Size(5, 5)
                                        );
                                    //まばたき検出処理
                                    DetectEyeBlink(eyesWithFace, _settingsInfo.face_size_min, _settingsInfo.face_size_max);

                                }
                                else
                                {
                                    //顔の検出ができないとき
                                    //まばたき検出に使用するプロパティを初期化する
                                    _waitingTimer.Reset();
                                    _blinkCount = 0;
                                    _isReady = false;
                                    _blinkFlgForLoop = false;
                                    _size = 0;
                                    _eyeCount = 0;
                                }

                            }
                            else
                            {
                                //まばたき検出に顔検出を使用しないパターン

                                // 座標指定して切り取り
                                Mat cutMat = grayMat.Clone(
                                    new Rect(
                                        Convert.ToInt32(tmpBitmap.Width * _settingsInfo.blink_area_x * 0.01), 
                                        Convert.ToInt32(tmpBitmap.Height * _settingsInfo.blink_area_y * 0.01), 
                                        Convert.ToInt32(tmpBitmap.Width * _settingsInfo.blink_area_width * 0.01), 
                                        Convert.ToInt32(tmpBitmap.Height * _settingsInfo.blink_area_height * 0.01)
                                        )
                                    );
                                eDoktor.Common.Trace.OutputTrace($"** 画面の横幅： {tmpBitmap.Width}");
                                eDoktor.Common.Trace.OutputTrace($"** 画面の縦幅： {tmpBitmap.Height}");
                                eDoktor.Common.Trace.OutputTrace($"** 検出範囲の横幅： {tmpBitmap.Width * _settingsInfo.blink_area_width * 0.01}");
                                eDoktor.Common.Trace.OutputTrace($"** 検出範囲の縦幅： {tmpBitmap.Height * _settingsInfo.blink_area_height * 0.01}");

                                // 目検出を行う
                                var eyesWithoutFace = _cascadeClassifierEyes.DetectMultiScale(cutMat, 1.1, 5, HaarDetectionTypes.ScaleImage, new OpenCvSharp.Size(5, 5));
                                //まばたき検出処理
                                DetectEyeBlink(eyesWithoutFace, _settingsInfo.eye_size_min, _settingsInfo.eye_size_max);

                            }
                        }

                        //まばたき検出の状況をログに出力する
                        eDoktor.Common.Trace.OutputTrace($"** 顔（目）の面積： {_size}");
                        eDoktor.Common.Trace.OutputTrace($"** 検知待機時間： {_waitingTimer.ElapsedMilliseconds}");
                        eDoktor.Common.Trace.OutputTrace($"** まばたき時間： {_blinkingTimer.ElapsedMilliseconds}");
                        eDoktor.Common.Trace.OutputTrace($"** まばたき検出可能状況： {_isReady}");
                        eDoktor.Common.Trace.OutputTrace($"** まばたき回数： {_blinkCount}");
                        eDoktor.Common.Trace.OutputTrace($"** 顔検出ループ内用まばたきフラグ： {_blinkFlgForLoop}");
                        eDoktor.Common.Trace.OutputTrace($"** まばたき判断用まばたきフラグ： {_blinkFlg}");

                        FaceRectangles = rects;
                        DumpFaceRectangle(rects);
#if MEASUREMENT_LOG

                        sw.Stop();
                        eDoktor.Common.Trace.OutputTrace($"顔検出処理経過時間(ms) = {sw.ElapsedMilliseconds}");
#endif
                    }
                    if (tmpBitmap != null)
                    {
                        tmpBitmap.Dispose();
                        
                    }

                    eDoktor.Common.Trace.OutputTrace("------------------------------------ループ処理ここまで");
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
            finally
            {
                _busy = false;
            }
        }

        private void DumpFaceRectangle(Rect[] rects)
        {
            eDoktor.Common.Trace.OutputTrace($"** 顔検出個数 ==> {rects.Length}");
            if (rects.Length > 0)
            {
                int cnt = 1;
                foreach (Rect rect in rects)
                {
                    eDoktor.Common.Trace.OutputTrace($"**** {cnt}番目の顔 幅={rect.Width} 高さ={rect.Height} X={rect.X} Y={rect.Y}  ");
                    cnt++;
                }
            }
        }

        /// <summary>
        /// まばたき検出処理
        /// </summary>
        /// <param name="eyes">検出済みの目の矩形情報</param>
        /// <param name="minSize">まばたき検知を行う目（顔）の最小面積（カメラとの距離）</param>
        /// <param name="maxSize">まばたき検知を行う目（顔）の最大面積（カメラとの距離）</param>
        private void DetectEyeBlink(Rect[] eyes, int minSize, int maxSize)
        {
            //目が検出できるかどうか
            if (eyes.Length > 0)
            {
                //目が検出できるとき

                //顔を検出に使用しない場合は目の面積をセットする
                if (!_settingsInfo.is_use_face_for_blinking)
                {
                    _size = eyes[0].Width * eyes[0].Height;
                }
                
                //目の矩形情報をセット
                EyesRectangles = eyes;
                _eyeCount = eyes.Length;

                //まばたき検出開始タイマーをスタート
                _waitingTimer.Start();

                //まばたき検出開始条件をチェック
                if (_waitingTimer.ElapsedMilliseconds > _settingsInfo.blink_waiting_time && _size > minSize && _size < maxSize)
                {
                    //検出開始条件を満たすとき
                    _blinkingTimer.Reset();
                    _isReady = true;
                    eDoktor.Common.Trace.OutputTrace("まばたき検出可能");
                    //まばたきフラグがtrueならまばたき回数を増やす
                    if (_blinkFlgForLoop)
                    {
                        _blinkCount++;
                        _blinkFlgForLoop = false;
                    }
                }
                else
                {
                    //検出開始条件を満たさないとき
                    eDoktor.Common.Trace.OutputTrace("目は検出できているが検出開始条件を満たしていない");
                    _blinkCount = 0;
                    _isReady = false;
                    _blinkFlgForLoop = false;
                }
            }
            else
            {
                //目が検出できないとき
                _blinkingTimer.Start();
                _eyeCount = 0;
                //まばたき検出開始条件をチェック
                if (_waitingTimer.ElapsedMilliseconds > _settingsInfo.blink_waiting_time && _blinkingTimer.ElapsedMilliseconds < _settingsInfo.blinking_time && _size > minSize && _size < maxSize)
                {
                    //検出開始条件を満たすとき
                    _blinkFlgForLoop = true;
                    _isReady = true;
                    eDoktor.Common.Trace.OutputTrace("まばたき");
                }
                else
                {
                    //検出開始条件を満たさないとき
                    _waitingTimer.Reset();
                    _blinkCount = 0;
                    _isReady = false;
                    _blinkFlgForLoop = false;
                    _size = 0;
                    eDoktor.Common.Trace.OutputTrace("目が検出できない");
                }
            }

            //まばたき回数が設定値を超えたらまばたきフラグをtrueにする
            if (_blinkCount >= _settingsInfo.blink_count)
            {
                _blinkFlg = true;
            }

        }
    }
}
