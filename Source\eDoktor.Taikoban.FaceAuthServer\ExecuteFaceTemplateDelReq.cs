﻿#define LICENCE_ENABLED

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceInterprocess;
using GFRL.FaceRecognition;

namespace eDoktor.Taikoban.FaceAuthServer
{
    /// <summary>
    /// 顔テンプレート削除要求
    /// </summary>
    public class ExecuteFaceTemplateDelReq
    {
        /// <summary>
        /// データベース
        /// </summary>
        DB _db;

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="database">データベース</param>
        public ExecuteFaceTemplateDelReq(Database database)
        {
            this._db = new DB(database);
        }

        /// <summary>
        /// 顔テンプレート削除要求コマンドに対する受信処理です
        /// </summary>
        /// <param name="packet">受信コマンドの内容</param>
        public XElement Execute(FaceInterprocess.Packet packet)
        {
            try
            {
                var isCorrectData = this.GetRequestData(packet, out FaceTemplateDelReqData requestData);

                if (isCorrectData == false)
                {
                    Trace.OutputErrorTrace("ExecuteCmdQueryFaceSettingGetReq：受信データが不正です。フォーマットエラーを返します。");

                    return this.SetFailElem(EnumErrorId.InputDataErr);
                }

                // DBから削除する
                bool ret = this._db.DeleteTemplateData(requestData.AccountId);

                if (ret)
                {
                    Trace.OutputDebugTrace("削除成功");
                    return this.SetSuccessElem();
                }
                else
                {
                    Trace.OutputDebugTrace("削除失敗");
                    return this.SetFailElem(EnumErrorId.DbErr);
                }
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return this.SetFailElem(EnumErrorId.Exception);
            }
        }

        /// <summary>
        /// リクエストデータ取得
        /// </summary>
        /// <param name="packet">受信コマンドの内容</param>
        /// <param name="requestData">リクエストデータ</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        private bool GetRequestData(eDoktor.Taikoban.FaceInterprocess.Packet packet, out FaceTemplateDelReqData requestData)
        {
            requestData = new FaceTemplateDelReqData();

            try
            {
                requestData.ToObject(packet.Data);

                return true;
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return false;
            }
        }

        /// <summary>
        /// 成功時応答データ作成
        /// </summary>
        /// <returns></returns>
        private XElement SetSuccessElem()
        {
            var elementData = new ElementDataCreate();
            elementData.CreateElem(nameof(FaceAuthtResData.ProcResult), (int)EnumProcResult.Success);
            elementData.CreateElem(nameof(FaceAuthtResData.ErrorId), (int)EnumErrorId.NoErr);

            Trace.OutputDebugTrace(elementData.StrElements);

            return elementData.Elements;
        }

        /// <summary>
        /// 失敗時応答データ作成
        /// </summary>
        /// <param name="errorId">エラーID</param>
        /// <returns></returns>
        private XElement SetFailElem(EnumErrorId errorId)
        {
            var elementData = new ElementDataCreate();
            elementData.CreateElem(nameof(FaceAuthtResData.ProcResult), (int)EnumProcResult.Fail);
            elementData.CreateElem(nameof(FaceAuthtResData.ErrorId), (int)errorId);

            Trace.OutputDebugTrace(elementData.StrElements);

            return elementData.Elements;
        }
    }
}
