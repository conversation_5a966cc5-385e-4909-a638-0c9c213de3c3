﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace eDoktor.Taikoban.FaceAuthServerConsole
{
    class Program
    {
        static void Main(string[] args)
        {
            eDoktor.Taikoban.FaceAuthServer.Server remoteServer = null;

            try
            {
                System.Environment.CurrentDirectory = System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);

                // ログ設定
                eDoktor.Common.Trace.EnableTrace = false;   // ログファイルのパスを設定するまでにエラーが発生するとログファイルのパスがデフォルトに確定してしまうのを防ぐ

                bool enableTrace = eDoktor.Common.Configuration.AppSetting("EnableTrace", false);
                bool enableDebugTrace = eDoktor.Common.Configuration.AppSetting("EnableDebugTrace", false);
                bool enableDetailedExceptionTrace = eDoktor.Common.Configuration.AppSetting("EnableDetailedExceptionTrace", false);
                string containingFolderPath = eDoktor.Common.Configuration.AppSetting("ContainingFolderPath");

                if (!string.IsNullOrEmpty(containingFolderPath) && !System.IO.Directory.Exists(containingFolderPath))
                {
                    try
                    {
                        System.IO.Directory.CreateDirectory(containingFolderPath);
                        System.IO.FileAttributes uAttribute = System.IO.File.GetAttributes(containingFolderPath);
                        System.IO.File.SetAttributes(containingFolderPath, uAttribute | System.IO.FileAttributes.Hidden);
                    }
                    catch
                    {
                    }
                }
                eDoktor.Common.Trace.EnableTrace = enableTrace;
                eDoktor.Common.Trace.EnableDebugTrace = enableDebugTrace;
                eDoktor.Common.Trace.EnableDetailedExceptionTrace = enableDetailedExceptionTrace;
                eDoktor.Common.Trace.ContainingFolderPath = containingFolderPath;

                
                remoteServer = new eDoktor.Taikoban.FaceAuthServer.Server(null);
                remoteServer.Start();

                Console.WriteLine("FaceAuthSever has been started. Press Enter to stop.");
                Console.ReadLine();
            }
            catch (Exception e)
            {
                eDoktor.Common.Trace.OutputCriticalTrace(e.Message);
                Console.WriteLine(e.Message);
            }
            finally
            {
                if (remoteServer != null)
                {
                    remoteServer.Stop();
                }

                Console.WriteLine("Press Enter to Exit.");
                Console.ReadLine();
            }
        }
    }
}
