﻿
namespace eDoktor.Taikoban.FaceAuthCommon
{
    /// <summary>
    /// Enum 処理結果
    /// </summary>
    public enum EnumProcResult
    {
        /// <summary>
        /// 正常終了
        /// </summary>
        Success = 0,
        /// <summary>
        /// 異常終了
        /// </summary>
        Fail = 1,
    }

    /// <summary>
    /// Enum エラーID
    /// </summary>
    public enum EnumErrorId
    {
        /// <summary>
        /// エラーなし
        /// </summary>
        NoErr = 0,
        /// <summary>
        /// 処理失敗
        /// </summary>                
        Exception = 1,
        /// <summary>
        /// 入力データ不正
        /// </summary>
        InputDataErr = 2,
        /// <summary>
        /// DBエラー
        /// </summary>
        DbErr = 3,
        /// <summary>
        /// 該当職員なし
        /// </summary>
        NoStaff = 4,
        /// <summary>
        /// モード違い
        /// </summary>
        DifferentMode = 5,
        /// <summary>
        /// 顔サーチNG
        /// </summary>
        FaceSearchNG = 6,
        /// <summary>
        /// 顔テンプレート取得失敗
        /// </summary>
        FaceTemplateGetFail = 7,
        /// <summary>
        /// テンプレートグループ登録失敗
        /// </summary>
        FaceTemplateGroupRegFail = 8,
        /// <summary>
        /// 顔テンプレート作成失敗
        /// </summary>
        FaceTemplateCreateFail = 10,
        /// <summary>
        /// 保持数オーバー
        /// </summary>
        HoldOver = 11,
        /// <summary>
        /// 更新対象外
        /// </summary>
        NoUpdate = 12,
    }

    /// <summary>
    /// Enum 登録状態
    /// </summary>
    public enum EnumRegistState
    {
        /// <summary>
        /// 登録あり
        /// </summary>
        Regist = 0,
        /// <summary>
        /// 登録あり（上限）
        /// </summary>
        RegistMax = 1,
        /// <summary>
        /// 登録なし
        /// </summary>
        NoRegist = 2,
    }

    /// <summary>
    /// Enum 認証結果
    /// </summary>
    public enum EnumAuthResult
    {
        /// <summary>
        /// 認証OK
        /// </summary>
        AuthOK = 0,
        /// <summary>
        /// 認証NG
        /// </summary>
        AuthNG = 1,
    }

    /// <summary>
    /// Enum 認証モード
    /// </summary>
    public enum EnumAuthMode
    {
        /// <summary>
        /// サーバ認証モード
        /// </summary>
        Server = 0,
        /// <summary>
        /// クライアント認証モード
        /// </summary>
        Client = 1,
    }

    /// <summary>
    /// Enum 本人確認結果
    /// </summary>
    public enum EnumIdentificationResult
    {
        /// <summary>
        /// 認証OK
        /// </summary>
        AuthOK = 0,
        /// <summary>
        /// 認証NG
        /// </summary>
        AuthNG = 1,
    }

    /// <summary>
    /// Enum ユーザ認証モード
    /// </summary>
    public enum EnumUserAuthMode
    {
        /// <summary>
        /// ユーザ検索認証モード
        /// </summary>
        UserSearchAuthMode = 0,
        /// <summary>
        /// ユーザ指定認証モード
        /// </summary>
        UserSpecifyAuthMode = 1,
    }

}
