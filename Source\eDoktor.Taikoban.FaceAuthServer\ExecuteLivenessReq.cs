﻿#define LICENCE_ENABLED

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSDK;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using eDoktor.Taikoban.FaceInterprocess;
using GFRL.FaceRecognition;
using GFRL.FaceSearch;
using GFRL.FaceTemplate;

namespace eDoktor.Taikoban.FaceAuthServer
{
    /// <summary>
    /// なりすまし画像登録要求
    /// </summary>
    public class ExecuteLivenessReq
    {
        /// <summary>
        /// データベース
        /// </summary>
        private DB _db;

        /// <summary>
        /// 設定情報
        /// </summary>
        private SettingsInfo _settingInfo;

        /// <summary>
        /// 認証結果　
        /// </summary>
        private EnumAuthResult _authResult = EnumAuthResult.AuthNG;

        /// <summary>
        /// 顔サーチ結果
        /// </summary>
        private FaceSearchResult _faceSearchResult;

        /// <summary>
        /// 顔認証結果
        /// </summary>
        private FaceRetrieveResult _faceRetrieveResult = null;

        /// <summary>
        /// アカウントID
        /// </summary>
        private int _accountId;

        /// <summary>
        /// ログ内容
        /// </summary>
        private string _logContents = string.Empty;

        /// <summary>
        /// 認証日時
        /// </summary>
        private readonly DateTime _authDateTime = DateTime.Now;

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="database">データベース</param> 
        /// <param name="settingInfo">設定情報</param>
        public ExecuteLivenessReq(Database database, SettingsInfo settingInfo)
        {
            this._db = new DB(database);
            this._settingInfo = settingInfo;
        }

        /// <summary>
        /// なりすまし画像登録要求コマンドに対する受信処理です
        /// </summary>
        /// <param name="packet">受信コマンドの内容</param>
        public XElement Execute(FaceInterprocess.Packet packet)
        {
            try
            {
                //if (this._settingInfo.auth_mode == EnumAuthMode.Client)
                //{
                //    // ① 設定．認証モードがクライアントモードの場合、「モード違い」で「異常終了」とする。
                //    this._logContents = Messages.Message(MessageId.Message011);
                //    Trace.OutputErrorTrace(this._logContents);

                //    return this.SetFailElem(EnumErrorId.DifferentMode);
                //}

                var isCorrectData = this.GetRequestData(packet, out LivenessReqData requestData);

                if (isCorrectData == false)
                {
                    this._logContents = "ExecuteCmdQueryFaceAuthtReq：受信データが不正です。フォーマットエラーを返します。";
                    Trace.OutputErrorTrace(this._logContents);

                    return this.SetFailElem(EnumErrorId.InputDataErr);
                }

#if LICENCE_ENABLED

                this._accountId = requestData.AccountId;

                // ② 顔認証SDKの顔サーチ処理、サムネイル取得処理
                var sdkFace = new SDKFaceAuth(this._settingInfo);

                // 画像データから顔検出を行い、検出結果から、サムネイル画像を取得する
                this._faceSearchResult = sdkFace.GetThumbnail(requestData.Image);

                if (this._faceSearchResult == null)
                {
                    // 顔サーチ結果がNGの場合、「顔サーチNG」で「異常終了」とする。
                    this._logContents = Messages.Message(MessageId.Message012);
                    Trace.OutputErrorTrace(this._logContents);

                    return this.SetFailElem(EnumErrorId.FaceSearchNG);
                }

                //なりすまし画像ログメッセージをセット
                this._logContents = Messages.Message(MessageId.Message031);

                //var errId = EnumErrorId.NoErr;

                //// ③ DBから職員IDに紐付く顔テンプレートをすべて取得する。

                //var isGetTemplate = _db.GetFaceAuthTemplateData(this._accountId, out List<FaceTemplateDataRegister> templateDataList);

                //if (isGetTemplate == false)
                //{
                //    this._logContents = Messages.Message(MessageId.Message002);
                //    return this.SetFailElem(EnumErrorId.DbErr);
                //}

                //if (templateDataList.Count <= 0)
                //{
                //    // 顔テンプレートが取得できなかった場合、「顔テンプレート取得失敗」で「異常終了」とする。
                //    this._logContents = Messages.Message(MessageId.Message013);
                //    return this.SetFailElem(EnumErrorId.FaceTemplateGetFail);
                //}

                //Trace.OutputDebugTrace("テンプレート情報あり");

                //// ④ ③で取得した顔テンプレートと認証するサムネイルから顔認証SDKで照合を行う。
                //errId = sdkFace.FaceAuth(this._faceSearchResult.FaceThumbnail, templateDataList, out this._faceRetrieveResult);

                //if (errId != EnumErrorId.NoErr)
                //{
                //    this._logContents = Messages.Message(MessageId.Message001);
                //    this._faceRetrieveResult = null;
                //    return this.SetFailElem(errId);
                //}

                //this._authResult = EnumAuthResult.AuthNG;

                //if (this._faceRetrieveResult.FaceIdentificationResult.HasIdentified)
                //{
                //    this._logContents = "認証成功";
                //    Trace.OutputDebugTrace(this._logContents);
                //    this._authResult = EnumAuthResult.AuthOK;
                //}
                //else
                //{
                //    // 照合結果が失敗の場合、「認証失敗」で「正常終了」とする。
                //    this._logContents = "認証失敗";
                //    Trace.OutputDebugTrace(this._logContents);
                //    this._authResult = EnumAuthResult.AuthNG;
                //}

                return this.SetSuccessElem();

#endif // LICENCE_ENABLED
            }
            catch (Exception ex)
            {
                this._logContents = Messages.Message(MessageId.Message001);
                Trace.OutputExceptionTrace(ex);
                return this.SetFailElem(EnumErrorId.Exception);
            }
        }

        /// <summary>
        /// ログ登録
        /// </summary>
        /// <param name="ipAddress">送信元IPアドレス</param>
        public void RegistLog(string ipAddress)
        {
            Trace.OutputDebugTrace("ログ登録");

            if (this._faceRetrieveResult != null)
            {
                _db.RegistAuthLog(false, ipAddress, this._authDateTime, this._logContents, this._settingInfo.auth_mode, true, this._faceRetrieveResult, this._faceSearchResult.FaceThumbnail);
            }
            else
            {
                _db.RegistNgAuthLog(ipAddress, this._accountId, this._authDateTime, this._logContents, this._settingInfo.auth_mode, true, this._faceSearchResult);
            }
        }

        /// <summary>
        /// 顔学習
        /// </summary>
        public void UpdateFace()
        {
            // 認証成功時
            if (this._authResult == EnumAuthResult.AuthOK)
            {
                // 顔学習処理を行う。
                var faceLearn = new FaceLearnClass();
                faceLearn.FaceLearn(this._faceRetrieveResult, this._settingInfo, this._accountId, this._db);
            }
        }

        /// <summary>
        /// リクエストデータ取得
        /// </summary>
        /// <param name="packet">受信コマンドの内容</param>
        /// <param name="requestData">リクエストデータ</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        private bool GetRequestData(FaceInterprocess.Packet packet, out LivenessReqData requestData)
        {
            requestData = new LivenessReqData();

            try
            {
                requestData.ToObject(packet.Data);

                return true;
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return false;
            }
        }

        /// <summary>
        /// 成功時応答データ作成
        /// </summary>
        /// <returns></returns>
        private XElement SetSuccessElem()
        {
            var elementData = new ElementDataCreate();
            elementData.CreateElem(nameof(FaceAuthtResData.ProcResult), (int)EnumProcResult.Success);
            elementData.CreateElem(nameof(FaceAuthtResData.ErrorId), (int)EnumErrorId.NoErr);
            elementData.CreateElem(nameof(FaceAuthtResData.AuthResult), (int)this._authResult);

            Trace.OutputDebugTrace(elementData.StrElements);

            return elementData.Elements;
        }

        /// <summary>
        /// 失敗時応答データ作成
        /// </summary>
        /// <param name="errorId">エラーID</param>
        /// <returns></returns>
        private XElement SetFailElem(EnumErrorId errorId)
        {
            var elementData = new ElementDataCreate();
            elementData.CreateElem(nameof(FaceAuthtResData.ProcResult), (int)EnumProcResult.Fail);
            elementData.CreateElem(nameof(FaceAuthtResData.ErrorId), (int)errorId);
            elementData.CreateElem(nameof(FaceAuthtResData.AuthResult), (int)EnumAuthResult.AuthNG);

            Trace.OutputDebugTrace(elementData.StrElements);

            return elementData.Elements;
        }
    }
}
