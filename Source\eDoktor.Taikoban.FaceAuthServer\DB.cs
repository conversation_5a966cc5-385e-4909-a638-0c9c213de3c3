﻿using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Text;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using GFRL.FaceRecognition;
using GFRL.FaceSearch;
using GFRL.FaceTemplate;

namespace eDoktor.Taikoban.FaceAuthServer
{
    /// <summary>
    /// データベースクラス
    /// </summary>
    public class DB
    {
        /// <summary>
        /// DBの認証ログへのサムネイルデータ登録有無
        /// </summary>
        private readonly bool _isRegistThumbnailLog = eDoktor.Common.Configuration.AppSetting("IsRegistThumbnailLog", false);

        // ▼ ADD 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
        /// <summary>
        /// マスク着用と判断するためのマスクスコア閾値（この値以上でマスクあり）
        /// </summary>
        private readonly float _maskScoreThresholdForWearingMask = eDoktor.Common.Configuration.AppSetting("MaskScoreThresholdForWearingMask", 50);
        // ▲ ADD 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara

        /// <summary>
        /// データベース
        /// </summary>
        private Database _db = null;

        //なりすまし判定デフォルト設定値
        public const int MIN_FACE_WIDTH = 80;
        public const int MAX_FACE_WIDTH = 1000;
        public const float FACE_AREA_MIN_RATIO = 0.05F;
        public const float FACE_AREA_MAX_RATIO = 0.5F;
        public const bool EDGE_POS_ERR_MODE = true;
        public const bool IS_FAKE_MODE = true;
        public const float FAKE_JUDGE_TH = 0.15F;
        public const bool IS_FACE_DIR_MODE = false;
        public const bool IS_GLASSES_MODE = false;
        public const bool IS_MASK_MODE = false;
        public const float MASK_JUDGE_TH = 0.3F;
        public const bool IS_EYE_MODE = false;
        public const bool IS_EYE_DIR_MODE = false;
        //なりすまし判定方法デフォルト設定値
        public const bool IS_LIVENESS_CHECK = true;
        public const int JUDGE_TIME = 1000;
        public const int JUDGE_RATIO = 60;
        public const int LIVENESS_CHECK_TIMEOUT = 10000;

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="db">データベース</param>
        public DB(Database db)
        {
            this._db = db;
        }

        #region 設定情報取得

        /// <summary>
        /// 設定値を取得する
        /// </summary>
        /// <returns>設定情報</returns>
        public FaceAuthSettingsInfo.SettingsInfo GetFaceAuthSettings()
        {
            FaceAuthSettingsInfo.SettingsInfo settingInfo_buf = null;

            try
            {
                StringBuilder query = new StringBuilder();

                //設定値を取得する
                query.AppendFormat("SELECT * FROM t_face_auth_settings, t_face_auth_liveness_settings");

                int affectedRows = this._db.ExecuteQuery(
                                    query.ToString(),
                                    delegate (DbDataReader reader)
                                    {
                                        var settingInfo = new FaceAuthSettingsInfo.SettingsInfo();

                                        settingInfo.auth_mode = (EnumAuthMode)Database.GetInt32(reader, nameof(settingInfo.auth_mode), (int)EnumAuthMode.Server);
                                        settingInfo.retry_count = Database.GetInt32(reader, nameof(settingInfo.retry_count), 0);
                                        settingInfo.face_template_regist_max_count = Database.GetInt32(reader, nameof(settingInfo.face_template_regist_max_count), 1);
                                        settingInfo.face_search_wait_time = Database.GetInt32(reader, nameof(settingInfo.face_search_wait_time), 0);
                                        settingInfo.auth_start_wait_time = Database.GetInt32(reader, nameof(settingInfo.auth_start_wait_time), 0);
                                        settingInfo.time_out = Database.GetInt32(reader, nameof(settingInfo.time_out), 0);
                                        settingInfo.mask_score_threshold = (float)Database.GetDouble(reader, nameof(settingInfo.mask_score_threshold), 100);
                                        settingInfo.template_quality_threshold_auth = (float)Database.GetDouble(reader, nameof(settingInfo.template_quality_threshold_auth), 50);
                                        settingInfo.template_quality_threshold_insert = (float)Database.GetDouble(reader, nameof(settingInfo.template_quality_threshold_insert), 50);
                                        settingInfo.face_auth_threshold = (float)Database.GetDouble(reader, nameof(settingInfo.face_auth_threshold), 85);
                                        settingInfo.face_recog_data_type = (FaceRecogDataType)Database.GetInt32(reader, nameof(settingInfo.face_recog_data_type), (int)FaceRecogDataType.Standard);
                                        settingInfo.is_estimate_gender_age = Database.GetBoolean(reader, nameof(settingInfo.is_estimate_gender_age), false);
                                        settingInfo.face_recog_quality_threshold = (float)Database.GetDouble(reader, nameof(settingInfo.face_recog_quality_threshold), 2);
                                        settingInfo.face_search_count_max = Database.GetInt32(reader, nameof(settingInfo.face_search_count_max), 1);
                                        settingInfo.face_search_scale_min = Database.GetInt32(reader, nameof(settingInfo.face_search_scale_min), 25);
                                        settingInfo.face_search_scale_max = Database.GetInt32(reader, nameof(settingInfo.face_search_scale_max), 100);
                                        settingInfo.input_image_width = Database.GetInt32(reader, nameof(settingInfo.input_image_width), 0);
                                        settingInfo.input_image_height = Database.GetInt32(reader, nameof(settingInfo.input_image_height), 0);
                                        settingInfo.is_hold_template_data = Database.GetBoolean(reader, nameof(settingInfo.is_hold_template_data), false);
                                        settingInfo.template_get_wait_time = Database.GetInt32(reader, nameof(settingInfo.template_get_wait_time), 60);
                                        settingInfo.is_display_auth_user_daialog = Database.GetBoolean(reader, nameof(settingInfo.is_display_auth_user_daialog), false);
                                        settingInfo.is_display_auth_image = Database.GetBoolean(reader, nameof(settingInfo.is_display_auth_image), false);
                                        settingInfo.is_use_eye_blink = Database.GetBoolean(reader, nameof(settingInfo.is_use_eye_blink), false);
                                        settingInfo.is_use_face_for_blinking = Database.GetBoolean(reader, nameof(settingInfo.is_use_face_for_blinking), false);
                                        settingInfo.face_size_max = Database.GetInt32(reader, nameof(settingInfo.face_size_max), 0);
                                        settingInfo.face_size_min = Database.GetInt32(reader, nameof(settingInfo.face_size_min), 0);
                                        settingInfo.eye_size_max = Database.GetInt32(reader, nameof(settingInfo.eye_size_max), 0);
                                        settingInfo.eye_size_min = Database.GetInt32(reader, nameof(settingInfo.eye_size_min), 0);
                                        settingInfo.blink_waiting_time = Database.GetInt32(reader, nameof(settingInfo.blink_waiting_time), 0);
                                        settingInfo.blink_count = Database.GetInt32(reader, nameof(settingInfo.blink_count), 0);
                                        settingInfo.blinking_time = Database.GetInt32(reader, nameof(settingInfo.blinking_time), 0);
                                        settingInfo.blink_area_x = Database.GetInt32(reader, nameof(settingInfo.blink_area_x), 0);
                                        settingInfo.blink_area_y = Database.GetInt32(reader, nameof(settingInfo.blink_area_y), 0);
                                        settingInfo.blink_area_width = Database.GetInt32(reader, nameof(settingInfo.blink_area_width), 0);
                                        settingInfo.blink_area_height = Database.GetInt32(reader, nameof(settingInfo.blink_area_height), 0);
                                        settingInfo.is_display_blink_message = Database.GetBoolean(reader, nameof(settingInfo.is_display_blink_message), false);
                                        settingInfo.blink_message = Database.GetString(reader, nameof(settingInfo.blink_message), "");
                                        settingInfo.is_display_face_guide = Database.GetBoolean(reader, nameof(settingInfo.is_display_face_guide), false);

                                        //なりすまし判定設定値
                                        settingInfo.token_file_path = (Database.GetString(reader, nameof(settingInfo.token_file_path), string.Empty));
                                        settingInfo.faceDetectModel_file_path = (Database.GetString(reader, nameof(settingInfo.faceDetectModel_file_path), string.Empty));
                                        settingInfo.faceDetectParam_file_path = (Database.GetString(reader, nameof(settingInfo.faceDetectParam_file_path), string.Empty));
                                        settingInfo.fakeModelH_file_path = (Database.GetString(reader, nameof(settingInfo.fakeModelH_file_path), string.Empty));
                                        settingInfo.fakeModelV_file_path = (Database.GetString(reader, nameof(settingInfo.fakeModelV_file_path), string.Empty));
                                        settingInfo.faceDirHModel_file_path = (Database.GetString(reader, nameof(settingInfo.faceDirHModel_file_path), string.Empty));
                                        settingInfo.faceDirVModel_file_path = (Database.GetString(reader, nameof(settingInfo.faceDirVModel_file_path), string.Empty));
                                        settingInfo.glassesModel_file_path = (Database.GetString(reader, nameof(settingInfo.glassesModel_file_path), string.Empty));
                                        settingInfo.maskModel_file_path = (Database.GetString(reader, nameof(settingInfo.maskModel_file_path), string.Empty));
                                        settingInfo.eyeDetectModel_file_path = (Database.GetString(reader, nameof(settingInfo.eyeDetectModel_file_path), string.Empty));
                                        settingInfo.eyeStatusModel_file_path = (Database.GetString(reader, nameof(settingInfo.eyeStatusModel_file_path), string.Empty));
                                        settingInfo.eyeDirModel_file_path = (Database.GetString(reader, nameof(settingInfo.eyeDirModel_file_path), string.Empty));
                                        settingInfo.minFaceWidth_param = Database.GetInt32(reader, nameof(settingInfo.minFaceWidth_param), MIN_FACE_WIDTH);
                                        settingInfo.maxFaceWidth_param = Database.GetInt32(reader, nameof(settingInfo.maxFaceWidth_param), MAX_FACE_WIDTH);
                                        settingInfo.faceAreaMinRatio_param = (float)Database.GetDouble(reader, nameof(settingInfo.faceAreaMinRatio_param), FACE_AREA_MIN_RATIO);
                                        settingInfo.faceAreaMaxRatio_param = (float)Database.GetDouble(reader, nameof(settingInfo.faceAreaMaxRatio_param), FACE_AREA_MAX_RATIO);
                                        settingInfo.edgePosErrMode_param = Database.GetBoolean(reader, nameof(settingInfo.edgePosErrMode_param), EDGE_POS_ERR_MODE);
                                        settingInfo.isFakeMode_param = Database.GetBoolean(reader, nameof(settingInfo.isFakeMode_param), IS_FAKE_MODE);
                                        settingInfo.fakeJudgeTh_param = (float)Database.GetDouble(reader, nameof(settingInfo.fakeJudgeTh_param), FAKE_JUDGE_TH);
                                        settingInfo.isFaceDirMode_param = Database.GetBoolean(reader, nameof(settingInfo.isFaceDirMode_param), IS_FACE_DIR_MODE);
                                        settingInfo.isGlassesMode_param = Database.GetBoolean(reader, nameof(settingInfo.isGlassesMode_param), IS_GLASSES_MODE);
                                        settingInfo.isMaskMode_param = Database.GetBoolean(reader, nameof(settingInfo.isMaskMode_param), IS_MASK_MODE);
                                        settingInfo.maskJudgeTh_param = (float)Database.GetDouble(reader, nameof(settingInfo.maskJudgeTh_param), MASK_JUDGE_TH);
                                        settingInfo.isEyeMode_param = Database.GetBoolean(reader, nameof(settingInfo.isEyeMode_param), IS_EYE_MODE);
                                        settingInfo.isEyeDirMode_param = Database.GetBoolean(reader, nameof(settingInfo.isEyeDirMode_param), IS_EYE_DIR_MODE);
                                        //なりすまし判定方法
                                        settingInfo.is_livenessCheck = Database.GetBoolean(reader, nameof(settingInfo.is_livenessCheck), IS_LIVENESS_CHECK);
                                        settingInfo.judge_time = Database.GetInt32(reader, nameof(settingInfo.judge_time), JUDGE_TIME);
                                        settingInfo.judge_ratio = Database.GetInt32(reader, nameof(settingInfo.judge_ratio), JUDGE_RATIO);
                                        settingInfo.liveness_check_timeout_error_message = Database.GetString(reader, nameof(settingInfo.liveness_check_timeout_error_message), string.Empty);
                                        settingInfo.liveness_check_error_message = Database.GetString(reader, nameof(settingInfo.liveness_check_error_message), string.Empty);
                                        settingInfo.liveness_check_timeout = Database.GetInt32(reader, nameof(settingInfo.liveness_check_timeout), LIVENESS_CHECK_TIMEOUT);

                                        settingInfo_buf = settingInfo;
                                    }
                                );
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }

            return settingInfo_buf;
        }

        /// <summary>
        /// 顔認証時に使用するメッセージを取得する
        /// </summary>
        /// <param name="messageDic_buf">メッセージ情報</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        public bool GetFaceAuthMessages(out Dictionary<string, string> messageDic_buf)
        {
            bool regProprietyFlg = true;
            messageDic_buf = new Dictionary<string, string>();

            try
            {
                //設定値を取得する
                string query = "SELECT * FROM t_face_auth_messages";

                var messageDic = new Dictionary<string, string>();

                int affectedRows = this._db.ExecuteQuery(
                                    query,
                                    delegate (DbDataReader reader)
                                    {
                                        var messageId = Database.GetString(reader, "message_id", string.Empty);
                                        var message = Database.GetString(reader, "message", string.Empty);

                                        messageDic[messageId] = message;
                                    }
                                );

                messageDic_buf = messageDic;
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                regProprietyFlg = false;
            }

            return regProprietyFlg;
        }

        /// <summary>
        /// 使用可能なカメラデバイス情報を取得する
        /// </summary>
        /// <param name="deviceList_buf">デバイス名リスト</param>
        /// <returns>処理結果(true:成功 false:失敗)</returns>
        public bool GetFaceAuthUseDevice(out List<string> deviceList_buf)
        {
            deviceList_buf = new List<string>();

            try
            {
                var deviceList = new List<string>();
                // 使用不可能なカメラデバイス情報を取得する
                var query = $"SELECT * FROM t_face_auth_use_devices where invalidated = 'false' AND logically_deleted = 'false'";

                this._db.ExecuteQuery(
                                    query,
                                    delegate (DbDataReader reader)
                                    {
                                        var deviceName = Database.GetString(reader, "device", null);
                                        if (!string.IsNullOrEmpty(deviceName))
                                        {
                                            deviceList.Add(deviceName);
                                        }
                                    }
                                );

                deviceList_buf = deviceList;
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 使用不可能なカメラデバイス情報を取得する
        /// </summary>
        /// <param name="deviceList_buf">デバイス名リスト</param>
        /// <returns>処理結果(true:成功 false:失敗)</returns>
        public bool GetFaceAuthNotUseDevice(out List<string> deviceList_buf)
        {
            deviceList_buf = new List<string>();

            try
            {
                var deviceList = new List<string>();
                // 使用不可能なカメラデバイス情報を取得する
                var query = $"SELECT * FROM t_face_auth_not_use_devices where invalidated = 'false' AND logically_deleted = 'false'";

                this._db.ExecuteQuery(
                                    query,
                                    delegate (DbDataReader reader)
                                    {
                                        var deviceName = Database.GetString(reader, "device", null);
                                        if (!string.IsNullOrEmpty(deviceName))
                                        {
                                            deviceList.Add(deviceName);
                                        }
                                    }
                                );

                deviceList_buf = deviceList;
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                return false;
            }

            return true;
        }

        #endregion

        #region ユーザ情報取得

        /// <summary>
        /// 職員IDに紐づくユーザ情報を取得する
        /// </summary>
        /// <param name="logonId">ログオンID</param>
        /// <param name="errId">エラーID</param>
        /// <returns>ユーザ情報</returns>
        public LoginUserInfo GetUserInfo(string logonId, out EnumErrorId errId)
        {
            errId = EnumErrorId.NoErr;
            var loginUserInfo = new LoginUserInfo();

            try
            {
                if (logonId == null)
                {
                    Trace.OutputDebugTrace("該当職員なし");
                    errId = EnumErrorId.InputDataErr;
                    return loginUserInfo;
                }

                StringBuilder query = new StringBuilder();

                // ログオンIDに紐づくidと職員名を取得する
                query.AppendFormat("SELECT t_accounts.id, t_accounts.logon_id, t_accounts.user_id, t_users.name");
                query.AppendFormat(" FROM t_accounts join t_users on t_users.id = t_accounts.user_id ");
                query.AppendFormat(" AND t_accounts.logon_id = '{0}' ", logonId);
                query.AppendFormat(" AND t_accounts.invalidated = 'false' AND t_accounts.logically_deleted = 'false' ");
                query.AppendFormat(" AND (t_accounts.usage_start_datetime IS NULL OR t_accounts.usage_start_datetime <= GETDATE()) ");
                query.AppendFormat(" AND (t_accounts.usage_end_datetime IS NULL OR t_accounts.usage_end_datetime  >= GETDATE()) ");
                query.AppendFormat(" AND t_users.invalidated = 'false' AND t_users.logically_deleted = 'false' ");
                query.AppendFormat(" AND (t_users.usage_start_datetime IS NULL OR t_users.usage_start_datetime <= GETDATE()) ");
                query.AppendFormat(" AND (t_users.usage_end_datetime IS NULL OR t_users.usage_end_datetime  >= GETDATE()) ");

                int affectedRows = this._db.ExecuteQuery(
                                    query.ToString(),
                                    delegate (DbDataReader reader)
                                    {
                                        loginUserInfo.account_id = (Database.GetInt32(reader, "id", 0));
                                        loginUserInfo.logon_id = (Database.GetString(reader, nameof(loginUserInfo.logon_id), string.Empty));
                                        loginUserInfo.user_id = Database.GetInt32(reader, "user_id", 1);
                                        loginUserInfo.name = (Database.GetString(reader, nameof(loginUserInfo.name), string.Empty));
                                    }
                                );

                if (affectedRows <= 0)
                {
                    errId = EnumErrorId.NoStaff;
                }
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                errId = EnumErrorId.DbErr;
            }

            return loginUserInfo;
        }

        /// <summary>
        /// 職員IDとパスワードでユーザ情報を取得
        /// </summary>
        /// <param name="logonId">ログオンID</param>
        /// <param name="password">パスワード</param>
        /// <param name="cooperativePassword">富士通暗号化パスワード</param>
        /// <param name="errId">エラーID</param>
        /// <param name="identificationResult">本人確認結果</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        public LoginUserInfo GetUserInfo(string logonId, string password, string cooperativePassword, out EnumErrorId errId, out EnumIdentificationResult identificationResult)
        {
            errId = EnumErrorId.NoErr;
            identificationResult = EnumIdentificationResult.AuthNG;
            var loginUserInfo = new LoginUserInfo();

            try
            {
                if (logonId == null)
                {
                    return loginUserInfo;
                }

                StringBuilder query = new StringBuilder();

                // ログオンIDに紐づくidと職員名を取得する
                query.AppendFormat("SELECT t_accounts.id, t_accounts.logon_id, t_accounts.user_id, t_users.name");
                query.AppendFormat(" FROM t_accounts join t_users on t_users.id = t_accounts.user_id ");
                query.AppendFormat(" AND t_accounts.logon_id = '{0}' ", logonId);
                if (string.IsNullOrWhiteSpace(cooperativePassword))
                {
                    query.AppendFormat(" AND t_accounts.password = '{0}' ", password);
                }
                else
                {
                    query.AppendFormat(" AND t_accounts.encrypted_password = '{0}' ", cooperativePassword);
                }
                query.AppendFormat(" AND t_accounts.invalidated = 'false' AND t_accounts.logically_deleted = 'false' ");
                query.AppendFormat(" AND (t_accounts.usage_start_datetime IS NULL OR t_accounts.usage_start_datetime <= GETDATE()) ");
                query.AppendFormat(" AND (t_accounts.usage_end_datetime IS NULL OR t_accounts.usage_end_datetime  >= GETDATE()) ");
                query.AppendFormat(" AND t_users.invalidated = 'false' AND t_users.logically_deleted = 'false' ");
                query.AppendFormat(" AND (t_users.usage_start_datetime IS NULL OR t_users.usage_start_datetime <= GETDATE()) ");
                query.AppendFormat(" AND (t_users.usage_end_datetime IS NULL OR t_users.usage_end_datetime  >= GETDATE()) ");

                eDoktor.Common.Trace.OutputDebugTrace("query={0}", query.ToString());

                int affectedRows = this._db.ExecuteQuery(
                                    query.ToString(),
                                    delegate (DbDataReader reader)
                                    {
                                        loginUserInfo.account_id = (Database.GetInt32(reader, "id", 0));
                                        loginUserInfo.logon_id = (Database.GetString(reader, nameof(loginUserInfo.logon_id), string.Empty));
                                        loginUserInfo.user_id = Database.GetInt32(reader, nameof(loginUserInfo.user_id), 1);
                                        loginUserInfo.name = (Database.GetString(reader, nameof(loginUserInfo.name), string.Empty));
                                    }
                                );

                if (affectedRows > 0)
                {
                    identificationResult = EnumIdentificationResult.AuthOK;
                }
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                errId = EnumErrorId.DbErr;
            }

            return loginUserInfo;
        }

        /// <summary>
        /// アカウントIDからログインユーザの情報を取得
        /// </summary>
        /// <param name="userId">ユーザーID(t_accounts.id)</param>
        /// <param name="errId_buf">エラーID</param>
        /// <returns>ログインユーザー情報</returns>
        public LoginUserInfo GetUserInfo(uint userId, out EnumErrorId errId)
        {
            var loginUserInfo = new LoginUserInfo();
            errId = EnumErrorId.NoErr;

            try
            {
                StringBuilder query = new StringBuilder();

                // ログオンIDに紐づくidと職員名を取得する
                query.AppendFormat("SELECT t_accounts.id, t_accounts.logon_id, t_accounts.user_id, t_users.name");
                query.AppendFormat(" FROM t_accounts join t_users on t_users.id = t_accounts.user_id ");
                query.AppendFormat(" AND t_accounts.id = '{0}' ", userId);
                query.AppendFormat(" AND t_accounts.invalidated = 'false' AND t_accounts.logically_deleted = 'false' ");
                query.AppendFormat(" AND (t_accounts.usage_start_datetime IS NULL OR t_accounts.usage_start_datetime <= GETDATE()) ");
                query.AppendFormat(" AND (t_accounts.usage_end_datetime IS NULL OR t_accounts.usage_end_datetime  >= GETDATE()) ");
                query.AppendFormat(" AND t_users.invalidated = 'false' AND t_users.logically_deleted = 'false' ");
                query.AppendFormat(" AND (t_users.usage_start_datetime IS NULL OR t_users.usage_start_datetime <= GETDATE()) ");
                query.AppendFormat(" AND (t_users.usage_end_datetime IS NULL OR t_users.usage_end_datetime  >= GETDATE()) ");

                int affectedRows = this._db.ExecuteQuery(
                                    query.ToString(),
                                    delegate (DbDataReader reader)
                                    {
                                        loginUserInfo.account_id = Database.GetInt32(reader, "id", 1);
                                        loginUserInfo.logon_id = Database.GetString(reader, "logon_id", null);
                                        loginUserInfo.user_id = Database.GetInt32(reader, "user_id", 1);
                                        loginUserInfo.name = Database.GetString(reader, "name", null);
                                    }
                                );

                if (affectedRows <= 0)
                {
                    Trace.OutputErrorTrace("該当職員なし");
                    errId = EnumErrorId.NoStaff;
                }
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                errId = EnumErrorId.DbErr;
            }

            return loginUserInfo;
        }

        #endregion

        #region テンプレート取得

        /// <summary>
        /// アカウントIDに紐付く顔テンプレートをすべて取得する。
        /// </summary>
        /// <param name="accountId">アカウントID</param>
        /// <param name="templateList_buf">テンプレートデータリスト</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        public bool GetFaceAuthTemplateData(int accountId, out List<FaceTemplateDataRegister> templateList_buf)
        {
            templateList_buf = new List<FaceTemplateDataRegister>();

            bool regProprietyFlg = true;

            try
            {
                var templateDataRegList = new List<FaceTemplateDataRegister>();

                // ログオンIDに紐づくidと職員名を取得する
                string query = $"SELECT * FROM t_face_auth_templates where account_id = '{accountId}'";

                int affectedRows = this._db.ExecuteQuery(
                                    query,
                                    delegate (DbDataReader reader)
                                    {
                                        var userId = Database.GetInt32(reader, "account_id", 0);
                                        var faceId = Database.GetInt32(reader, "template_id", 0);
                                        var strTemplateData = Database.GetString(reader, "template_data", string.Empty);

                                        if (userId > 0 && faceId > 0 && !string.IsNullOrWhiteSpace(strTemplateData))
                                        {
                                            try
                                            {
                                                var templateData = Convert.FromBase64String(strTemplateData);

                                                //  人物IDと顔IDの付与
                                                var faceTemplateID = new FaceTemplateID()
                                                {
                                                    UserID = (uint)userId,
                                                    FaceID = (byte)faceId,
                                                };

                                                templateDataRegList.Add(new FaceTemplateDataRegister(faceTemplateID, templateData));
                                            }
                                            catch (Exception ex)
                                            {
                                                Trace.OutputExceptionTrace(ex);
                                            }
                                        }
                                    }
                                );

                // 正常に実行できた場合
                if (templateDataRegList.Count > 0)
                {
                    templateList_buf = templateDataRegList;
                }
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                regProprietyFlg = false;
            }

            return regProprietyFlg;
        }

        /// <summary>
        /// (2)全職員のテンプレートデータ取得処理①
        /// </summary>
        /// <param name="faceTemplateDataRegisters_buf">FaceTemplateDataRegister型の情報</param>
        /// <returns>true:取得成功 false:取得失敗</returns>
        public bool GetAllTemplateData(out List<FaceTemplateDataRegister> faceTemplateDataRegisters_buf)
        {
            bool regProprietyFlg = true;

            faceTemplateDataRegisters_buf = new List<FaceTemplateDataRegister>();

            try
            {
                var templateDataRegList = new List<FaceTemplateDataRegister>();

                var query = new StringBuilder();
                query.Append("SELECT t_face_auth_templates.account_id, t_face_auth_templates.template_id, t_face_auth_templates.template_data ");
                query.Append("FROM t_face_auth_templates INNER JOIN t_accounts ON t_accounts.id = t_face_auth_templates.account_id ");
                query.Append(" AND t_accounts.invalidated = 0 AND t_accounts.logically_deleted = 0 ");
                query.Append(" AND (t_accounts.usage_start_datetime IS NULL OR t_accounts.usage_start_datetime <= GETDATE()) ");
                query.Append(" AND (t_accounts.usage_end_datetime IS NULL OR t_accounts.usage_end_datetime >= GETDATE()) ");
                query.Append(" INNER JOIN t_users ON t_users.id = t_accounts.user_id ");
                query.Append(" AND t_users.invalidated = 0 AND t_users.logically_deleted = 0 ");
                query.Append(" AND (t_users.usage_start_datetime IS NULL OR t_users.usage_start_datetime <= GETDATE()) ");
                query.Append(" AND (t_users.usage_end_datetime IS NULL OR t_users.usage_end_datetime >= GETDATE())");

                int affectedRows = this._db.ExecuteQuery(
                    query.ToString(),
                    delegate (DbDataReader reader)
                    {
                        var userId = Database.GetInt32(reader, "account_id", 0);
                        var faceId = Database.GetInt32(reader, "template_id", 0);
                        var strTemplateData = Database.GetString(reader, "template_data", string.Empty);

                        if (userId > 0 && faceId > 0 && !string.IsNullOrWhiteSpace(strTemplateData))
                        {
                            try
                            {
                                var templateData = Convert.FromBase64String(strTemplateData);

                                //  人物IDと顔IDの付与
                                var faceTemplateID = new FaceTemplateID()
                                {
                                    UserID = (uint)userId,
                                    FaceID = (byte)faceId,
                                };

                                templateDataRegList.Add(new FaceTemplateDataRegister(faceTemplateID, templateData));
                            }
                            catch (Exception ex)
                            {
                                Trace.OutputExceptionTrace(ex);
                            }
                        }
                    }
                );

                Trace.OutputDebugTrace("全テンプレート情報取得:{0}件", templateDataRegList.Count);
                faceTemplateDataRegisters_buf = templateDataRegList;
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                regProprietyFlg = false;
            }

            return regProprietyFlg;
        }

        /// <summary>
        /// 対象のテンプレートデータを取得する
        /// </summary>
        /// <param name="accountId">アカウントID</param>
        /// <param name="templateList_buf">t_face_auth_templates テーブルの情報</param>
        /// <returns>true:取得成功 false:取得失敗</returns>
        // ▼ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
        //public bool GetTemplateData(int accountId, out List<TemplateInfo> templateList_buf)
        public bool GetTemplateData(int accountId, out List<TemplateInfo> templateList_buf, bool? withMask = null)
        // ▲ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
        {
            var result = true;
            templateList_buf = new List<TemplateInfo>();

            try
            {
                var templateDataRegList = new List<TemplateInfo>();

                StringBuilder query = new StringBuilder();
                query.Append("SELECT t_face_auth_templates.template_id, t_face_auth_templates.template_quality, t_face_auth_templates.modification_datetime ");
                query.AppendFormat("FROM t_face_auth_templates WHERE t_face_auth_templates.account_id = '{0}'", accountId);
                // ▼ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
                if (withMask.HasValue)
                {
                    query.AppendFormat(" AND t_face_auth_templates.mask_score {0} {1}", (withMask.Value) ? ">=" : "<", _maskScoreThresholdForWearingMask);
                }
                // ▲ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara

        int affectedRows = this._db.ExecuteQuery(
                    query.ToString(),
                    delegate (DbDataReader reader)
                    {
                        var template = new TemplateInfo();

                        template.template_id = Database.GetInt32(reader, nameof(template.template_id), 1);
                        template.template_quality = (float)Database.GetDouble(reader, nameof(template.template_quality), 0);
                        template.modification_datetime = Database.GetDateTime(reader, nameof(template.modification_datetime), DateTime.Now);

                        templateDataRegList.Add(template);
                    }
                );

                if (affectedRows != 0)
                {
                    Trace.OutputDebugTrace("取得テンプレート数 : {0}", templateDataRegList.Count);
                    templateList_buf = templateDataRegList;
                }
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                result = false;
            }

            return result;
        }

        #endregion

        #region テンプレート登録・削除

        /// <summary>
        /// 顔認証テンプレート管理テーブルに登録する
        /// </summary>
        /// <param name="accountId">アカウントID</param>
        /// <param name="templateId">テンプレートID</param>
        /// <param name="templateData">テンプレートデータ</param>
        /// <param name="templateQuality">テンプレート評価値</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        /// <returns></returns>
        // ▼ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
        //public bool InsertTemplateData(int accountId, int templateId, byte[] templateData, float templateQuality)
        public bool InsertTemplateData(int accountId, int templateId, byte[] templateData, float templateQuality, float maskScore)
        // ▲ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
        {
            bool regProprietyFlg = false;

            try
            {
                var query = new StringBuilder();
                // ▼ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
                //query.Append("INSERT INTO t_face_auth_templates(account_id, template_id, template_data, template_quality) ");
                //query.AppendFormat(" VALUES('{0}', '{1}', '{2}', '{3}')", accountId, templateId, Convert.ToBase64String(templateData), templateQuality);
                query.Append("INSERT INTO t_face_auth_templates(account_id, template_id, template_data, template_quality, mask_score) ");
                query.AppendFormat(" VALUES('{0}', '{1}', '{2}', '{3}', {4})", accountId, templateId, Convert.ToBase64String(templateData), templateQuality, maskScore);
                // ▲ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara

                int affectedRows = this._db.ExecuteNonQuery(query.ToString());

                // 正常に実行できた場合
                if (affectedRows != 0)
                {
                    regProprietyFlg = true;
                }
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
            return regProprietyFlg;
        }

        /// <summary>
        /// 顔データを登録(上書き)する。
        /// </summary>
        /// <param name="accountId">アカウントID</param>
        /// <param name="templateId">テンプレートID</param>
        /// <param name="templateData">テンプレートデータ</param>
        /// <param name="templateQuality">テンプレート評価値</param>
        /// <param name="staffId">職員ID</param>
        /// <param name="faceData">顔データ</param>
        /// <returns>true:登録成功 false:登録失敗</returns>
        // ▼ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
        //public bool UpdateTemplateData(int accountId, int templateId, byte[] templateData, float templateQuality)
        public bool UpdateTemplateData(int accountId, int templateId, byte[] templateData, float templateQuality, float maskScore)
        // ▲ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
        {
            bool regProprietyFlg = false;

            if (accountId == 0 || templateId == 0)
            {
                return regProprietyFlg;
            }

            try
            {
                var query = new StringBuilder();
                // ▼ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
                //query.AppendFormat("UPDATE t_face_auth_templates SET template_data = '{0}', template_quality = '{1}' ", Convert.ToBase64String(templateData), templateQuality);
                query.AppendFormat("UPDATE t_face_auth_templates SET template_data = '{0}', template_quality = '{1}', mask_score = {2} ", Convert.ToBase64String(templateData), templateQuality, maskScore);
                // ▲ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
                query.AppendFormat(" WHERE account_id = '{0}' AND template_id = '{1}'", accountId, templateId);

                int affectedRows = this._db.ExecuteNonQuery(query.ToString());

                // SQL実行に成功した場合
                if (affectedRows != 0)
                {
                    regProprietyFlg = true;
                }

            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }

            return regProprietyFlg;
        }

        /// <summary>
        /// 顔データを削除する。
        /// </summary>
        /// <param name="staffId">アカウントID</param>
        /// <returns>true:削除成功 false:削除失敗</returns>
        public bool DeleteTemplateData(int accountId)
        {
            bool regProprietyFlg = true;

            try
            {
                string query = string.Format("DELETE FROM t_face_auth_templates WHERE account_id = '{0}'", accountId);

                int affectedRows = this._db.ExecuteNonQuery(query);
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                regProprietyFlg = false;
            }

            return regProprietyFlg;
        }

        #endregion

        #region DBに認証ログデータを登録する

        /// <summary>
        /// DBに認証ログデータを登録する
        /// </summary>
        /// <param name="authResult">認証結果</param>
        /// <param name="ipAddress">送信元IP</param>
        /// <param name="authRequestRecieveDatetime">認証要求受信日時</param>
        /// <param name="logContents">ログ内容</param>
        /// <param name="authMode">認証モード</param>
        /// <param name="isUserSpecifiedMode">ユーザ指定認証モードか</param>
        /// <param name="faceRetrieveResult">顔認証SDKから取得した顔照合結果</param>
        /// <param name="thumbnailData">サムネイルデータ</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        public bool RegistAuthLog(bool authResult, string ipAddress, DateTime authRequestRecieveDatetime, string logContents, EnumAuthMode authMode, bool isUserSpecifiedMode, FaceRetrieveResult faceRetrieveResult, byte[] thumbnailData)
        {
            try
            {
                Trace.OutputDebugTrace("RegistAuthLog START");

                var strThumnailData = this.ConvertToStringTthumbnailData(thumbnailData);

                var recogScoreData = faceRetrieveResult.ScoreList.FirstOrDefault();

                var query = new StringBuilder();
                query.Append("INSERT INTO t_face_auth_logs ");
                // 送信元IPアドレス、認証日時、認証結果、アカウントID、テンプレートID、顔照合スコア、テンプレート評価値、マスクスコア
                query.Append("(ip_address, auth_datetime, is_auth_success, account_id, template_id, face_recognition_score, template_quality, mask_score");
                // ログ内容、サムネイルデータ、認証モード、ユーザ指定有無、 ログオンID、ユーザID、組織内ID、ユーザ名
                query.Append(", log_contents, thumbnail_data, auth_mode, is_user_specified, logon_id, user_id, organizational_id, user_name)  ");

                query.Append("SELECT ");
                query.Append($"  '{ipAddress}' ");                                                  // 送信元IPアドレス
                query.Append($", '{authRequestRecieveDatetime}' ");                                 // 認証日時
                query.Append($", '{authResult}' ");                                                 // 認証結果
                query.Append($", t_accounts.id ");                                                  // アカウントID
                query.Append($", {recogScoreData.TemplateID.FaceID} ");                             // テンプレートID
                query.Append($", {recogScoreData.Score} ");                                         // 顔照合スコア
                query.Append($", {faceRetrieveResult.FaceInfo.FaceFeature.TemplateQuality} ");      // テンプレート評価値
                query.Append($", {faceRetrieveResult.FaceInfo.FaceFeature.MaskScore} ");            // マスクスコア
                query.Append($", '{logContents}' ");                                                // ログ内容
                query.Append($", '{strThumnailData}' ");                                            // サムネイルデータ
                query.Append($", '{(int)authMode}' ");                                              // 認証モード
                query.Append($", '{isUserSpecifiedMode}' ");                                        // ユーザ指定有無									
                // ログオンID、ユーザID、組織内ID、ユーザ名
                query.Append(", t_accounts.logon_id, t_users.id, t_users.organizational_id, t_users.name ");
                query.Append("FROM t_accounts INNER JOIN t_users ");
                query.Append($" ON {recogScoreData.TemplateID.UserID} > 0 AND t_accounts.id = {recogScoreData.TemplateID.UserID} AND t_users.id= t_accounts.user_id");

                // アカウント、ユーザ情報が取得できなかった場合用
                query.Append(" UNION ALL ");

                query.Append("SELECT ");
                query.Append($"  '{ipAddress}' ");                                                  // 送信元IPアドレス
                query.Append($", '{authRequestRecieveDatetime}' ");                                 // 認証日時
                query.Append($", '{authResult}' ");                                                 // 認証結果
                query.Append($", {recogScoreData.TemplateID.UserID} ");                             // アカウントID
                query.Append($", {recogScoreData.TemplateID.FaceID} ");                             // テンプレートID
                query.Append($", {recogScoreData.Score} ");                                         // 顔照合スコア
                query.Append($", {faceRetrieveResult.FaceInfo.FaceFeature.TemplateQuality} ");      // テンプレート評価値
                query.Append($", {faceRetrieveResult.FaceInfo.FaceFeature.MaskScore} ");            // マスクスコア
                query.Append($", '{logContents}' ");                                                // ログ内容
                query.Append($", '{strThumnailData}' ");                                            // サムネイルデータ
                query.Append($", '{(int)authMode}' ");                                              // 認証モード
                query.Append($", '{isUserSpecifiedMode}' ");                                        // ユーザ指定有無									
                // ログオンID、ユーザID、組織内ID、ユーザ名
                query.Append(" , '', 0, '', ''  ");
                query.Append(" WHERE NOT EXISTS(SELECT * FROM t_accounts INNER JOIN t_users ");
                query.Append($" ON {recogScoreData.TemplateID.UserID} > 0 AND t_accounts.id = {recogScoreData.TemplateID.UserID} AND t_users.id= t_accounts.user_id) ");

                Trace.OutputDebugTrace(query.ToString());
                int affectedRows = this._db.ExecuteNonQuery(query.ToString());

                // 正常に実行できた場合
                if (affectedRows != 0)
                {
                    return true;
                }
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }

            return false;
        }

        /// <summary>
        /// DBに認証NGログデータを登録する
        /// </summary>
        /// <param name="ipAddress">送信元IP</param>
        /// <param name="authRequestRecieveDatetime">認証要求受信日時</param>
        /// <param name="logContents">ログ内容</param>
        /// <param name="authMode">認証モード</param>
        /// <param name="isUserSpecifiedMode">ユーザ指定認証モードか</param>
        /// <param name="faceSearchResult">顔サーチ結果</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        public bool RegistNgAuthLog(string ipAddress, int accountId, DateTime authRequestRecieveDatetime, string logContents, EnumAuthMode authMode, bool isUserSpecifiedMode, FaceSearchResult faceSearchResult)
        {
            var strThumnailData = this.ConvertToStringTthumbnailData(faceSearchResult?.FaceThumbnail);

            return RegistNgAuthLog(ipAddress, accountId, authRequestRecieveDatetime, logContents, authMode, isUserSpecifiedMode, strThumnailData, faceSearchResult?.RecogQuality ?? 0);
        }

        /// <summary>
        /// DBに認証NGログデータを登録する
        /// </summary>
        /// <param name="ipAddress">送信元IP</param>
        /// <param name="accountId">一番合致したアカウントID</param>
        /// <param name="authRequestRecieveDatetime">認証要求受信日時</param>
        /// <param name="logContents">ログ内容</param>
        /// <param name="authMode">認証モード</param>
        /// <param name="isUserSpecifiedMode">ユーザ指定認証モードか</param>
        /// <param name="thumbnailData">サムネイルデータ</param>
        /// <param name="recogQuality">顔照合適合度(≒テンプレート評価値)(顔サーチまでたどり着いていない場合はNullの想定)</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        public bool RegistNgAuthLog(string ipAddress, int accountId, DateTime authRequestRecieveDatetime, string logContents, EnumAuthMode authMode, bool isUserSpecifiedMode, string thumbnailData, float recogQuality)
        {
            try
            {
                Trace.OutputDebugTrace("RegistNgAuthLog START");

                var query = new StringBuilder();

                query.Append("INSERT INTO t_face_auth_logs ");
                // 送信元IPアドレス、認証日時、認証結果、アカウントID、テンプレート評価値
                query.Append("(ip_address, auth_datetime, is_auth_success, account_id, template_quality");
                // ログ内容、サムネイルデータ、認証モード、ユーザ指定有無、 ログオンID、ユーザID、組織内ID、ユーザ名
                query.Append(", log_contents, thumbnail_data, auth_mode, is_user_specified, logon_id, user_id, organizational_id, user_name)  ");

                query.Append("SELECT ");
                query.Append($"  '{ipAddress}' ");                  // 送信元IPアドレス
                query.Append($", '{authRequestRecieveDatetime}' "); // 認証日時
                query.Append($", 'false' ");                        // 認証結果
                query.Append($", t_accounts.id ");                  // アカウントID
                query.Append($", {recogQuality} ");                 // テンプレート評価値
                query.Append($", '{logContents}' ");                // ログ内容
                query.Append($", '{thumbnailData}' ");              // サムネイルデータ
                query.Append($", '{(int)authMode}' ");              // 認証モード
                query.Append($", '{isUserSpecifiedMode}' ");        // ユーザ指定有無									
                // ログオンID、ユーザID、組織内ID、ユーザ名
                query.Append(", t_accounts.logon_id, t_users.id, t_users.organizational_id, t_users.name ");
                query.Append("FROM t_accounts INNER JOIN t_users ");
                query.Append($" ON {accountId} > 0 AND t_accounts.id = {accountId} AND t_users.id= t_accounts.user_id");

                // アカウント、ユーザ情報が取得できなかった場合用
                query.Append(" UNION ALL ");

                query.Append("SELECT ");
                query.Append($"  '{ipAddress}' ");                  // 送信元IPアドレス
                query.Append($", '{authRequestRecieveDatetime}' "); // 認証日時
                query.Append($", 'false' ");                        // 認証結果
                query.Append($", {accountId} ");                    // アカウントID
                query.Append($", {recogQuality} ");                 // テンプレート評価値
                query.Append($", '{logContents}' ");                // ログ内容
                query.Append($", '{thumbnailData}' ");              // サムネイルデータ
                query.Append($", '{(int)authMode}' ");              // 認証モード
                query.Append($", '{isUserSpecifiedMode}' ");        // ユーザ指定有無									
                // ログオンID、ユーザID、組織内ID、ユーザ名
                query.Append(" , '', 0, '', ''  ");
                query.Append(" WHERE NOT EXISTS(SELECT * FROM t_accounts INNER JOIN t_users ");
                query.Append($" ON {accountId} > 0 AND t_accounts.id = {accountId} AND t_users.id= t_accounts.user_id) ");

                Trace.OutputDebugTrace(query.ToString());

                int affectedRows = this._db.ExecuteNonQuery(query.ToString());

                // 正常に実行できた場合
                if (affectedRows != 0)
                {
                    return true;
                }
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }

            return false;
        }

        /// <summary>
        /// サムネイルを登録用に変換
        /// </summary>
        /// <param name="thumbnailData"></param>
        /// <returns></returns>
        private string ConvertToStringTthumbnailData(byte[] thumbnailData)
        {
            var strThumbnailData = string.Empty;

            if (this._isRegistThumbnailLog && thumbnailData != null && thumbnailData.Length > 0)
            {
                strThumbnailData = Convert.ToBase64String(thumbnailData);
            }

            return strThumbnailData;
        }
        #endregion
    }
}
