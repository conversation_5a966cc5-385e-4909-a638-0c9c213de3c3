﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="RowNo.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="LogonId.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="LogonName.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Result.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ErrorMessage.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="IsResult.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="button1.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAaUAAABYCAYAAABVqejdAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAs
        SgAALEoBd3p0TQAAB+ZJREFUeF7t3c91o0gUxeEOwSE4BIfgAGbhEHo7O3UGDqFDmBA6hA6hQ+gQJoOe
        d/WAoYoLAgRItn7vnG/hEvrnxbunCih9+evvb3fjz58/r+EUvoef4VegKIqitiv1VfVX9Vn121fXj2/F
        Dh4l/hkv4T3oH0RRFEXdrtSH1Y9fXL8+ih3ck75wUEL/DhRFUdT9lfqz+vSz6+N7soN7iC/3NTAjoiiK
        +lilvv3V9fU92MEt6cuEpbOidr1TU8m3oHNNrcOTGwA+ouiXWpnq90/1U/XV9rz9klIf3z2c7OAW4sPr
        y88Nox9BJ9xuupYJAI9Gfbfpv+rDc0p9/c291hbs4DXiwz6HOQmsf8BhU0IAwLToyU/qy01/vlTq85uv
        XNnBteIDKm3/1acdKT2mqSNLcABwx9Snm359qaef3PPXsoNLxYdSuk7NjtowenLPBwDcJ/Xtpn9PhZNm
        Vpv0dzu4RHwQrUdOnTvSCTXCCAA+MPXxpp+PlXLg6usC7OBc8QG09jiWnrprmAsXAOATUV9v+rsr5cFV
        1wrYwTn0xueP4OvdPQcA8Dmoz2e7t7U6mOzgJXrDfN9BKSXvah8lAMA+1O+bvu9qVTDZwSl6o3y/QWk6
        x7kjAHgg0fd1ld7Yct7iYLKDY/QG+T6D0pV3BBIAPCD1/yYHXC0KJjvoxAtrmubqH3c8AOCxKA8yFgY1
        +7SOHazFC2p65tYNCSQAQEe5kPFQlPJj1qYJdrAvXkjTMrdeyDkkAEBBudDkQ12/3PE1O9gXL+RuliKQ
        AACW8iG4TRW+u+P77GArXsCdR9I0jJtiAQCjlBNNXtQ1eX7JDko8cSzprrpbFwDwGJQXGRtFKVdGV9rs
        oMST3N26P9yxAAA4yo2Mj6JGd/2xg/EEXW1Xl6ZhnEcCAMym3Gjyoy57Nd5gQOJgd0kfy3YAgMWUHxkj
        RdlbigYDcaCbJf2sjwMAYC7lSMZJUYPZUvGHxEFulsQmqwCA1ZQjGSdFDWZLxR9xgNb+6mKWBAC4mvIk
        Y6Wo4lqF+gnuijtmSQCAqylPMlaKOvWPqZ9Q35c0a1sIAADmUK5kvHT1u/94/0DdfVsXV9wBADajXMl4
        KarbJah/oNvjjvuSAACbUa5kvBTV7YnXP7BeuuNnKQAAm4t8qXd56Jbw2gPcvUks3QEANqd8yZgp6nzP
        0tQBLN0BADanfMmYKeo8EWoPqG+Y5ao7AMBulDMZN12dTxmNPXjxh5gAAFhLOZNx09V5MtQ+WBfnkwAA
        u1HOZNz8XxrXA+7+JH5ZFgCwG+VMxk1RL3pgsO2DewEAALbURE6/XjVY73dXbPkAAMAeIm/qH/87uVBi
        V3AAwO6UNxk7Xb0TSgCAm1DeZOx0RSgBAG5DeZOx09U5lOo9iNjzDgCwu8gbQgkAcB8ib1i+AwDcB+VN
        xk5XhBIA4DaUNxk7XRFKAIDbUN5k7HRlQ4mbZwEAu4u8sTfPss0QAOBwTeT067zNEBuyAgAOpZzJuCnq
        pX2wrrf6BQAA2ErkjP/piuZBfuQPAHAY5UzGTVfFj/zxc+gAgMMoZzJuuip+Dn0wjYp66r8AAABbUL5k
        zBR1/sXz9oDnHCuKn0QHAGxO+ZIxU9SzHusf9DvHu2IPPADA5iJf6j1Xu/tj+wfVJ51ULOEBADajXMl4
        Kaq7uK5/oLtmnCU8AMBmlCsZL0V198bWB9dLeFyFBwDYjHIl46WrYmu7+uB6HzzVa/8YAADWUJ5krBR1
        6h9TP8Gt9bFrOADgasqTjJWiimsXiidIHFDfSKtitgQAWE05knFS1OAq7+IPiYPcPUvMlgAAqylHMk6K
        Ot+b1Ff80YoD3WyJK/EAAIspPzJGirL3wg4GJA52syX9GBP3LQEAZlNuNPlR12CWJIOBVjzBXYn3wx0L
        AICj3Mj4KOrdHSt2UOJJSrf6viUVy3gAgIuUFxkbRSlXRlfd7GArnuiultA0jF+mBQCMUk40eVHX5NXc
        drAvXsDtiac7cjm/BAAYUD4Et9J28Qdk7WBfvIhevN4WQkUwAQAKyoUmH+qatW2dHazFi+lqPDcN4+ct
        AAAd5ULGQ1HKD3u1Xc0OOvGC7vySimACAIwFkmr2rkB2cEy8sLuSQqU7dVnKA4AHpP7f5ICrRVds28Ep
        eoN8n0FxjgkAHkz0fZ3eceeQVItvIbKDl+iN8v0GpXVDNm8FgAegft/0fVer7mm1g3PoDfN9bY3erQsA
        +PjU57Pd21q9yYIdnEtvHMZSUtM5brIFgE9Efb3p766UB1ft+mMHl4gPoA/obpJqSzffcq4JAD4w9fGm
        n4+VcuDqiYgdXCo+yNSVFyqlp6Z6hBMAfCDq203/HlsVU2nT1U36ux1cKz7UKUx98DacZt1EBQC4DfXp
        pl9f6ukn9/y17OA14gPqi0zNmtpSsl619ggA2E70ZM2KdK2A+7mJutTnN59g2MEtxId9C1Pnmvqlf4Bm
        WVwYAQAHUt9t+u+cIFKpr7+519qCHdxSfHil7txwaksJrBNqmjoq3HQtfIulPwCYIfqlAqffP9VP1VfV
        X+esaPVLfXz31S07uAd9mbD0n0BRFEXdttS3DzvVYgf3FF9Oya2UXjp7oiiKoo4p9Wf16cNXpuzgUeIL
        K6A0lWQGRVEUddtSH1Y/vum5fTt4K/HP0JqnTri1651jdw1TFEVR60p9Vf1VfVb99o72K/325T+8fCZ3
        nGdyEQAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="button2.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAaUAAABYCAYAAABVqejdAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAs
        SgAALEoBd3p0TQAAB+ZJREFUeF7t3c91o0gUxeEOwSE4BIfgAGbhEHo7O3UGDqFDmBA6hA6hQ+gQJoOe
        d/WAoYoLAgRItn7vnG/hEvrnxbunCih9+evvb3fjz58/r+EUvoef4VegKIqitiv1VfVX9Vn121fXj2/F
        Dh4l/hkv4T3oH0RRFEXdrtSH1Y9fXL8+ih3ck75wUEL/DhRFUdT9lfqz+vSz6+N7soN7iC/3NTAjoiiK
        +lilvv3V9fU92MEt6cuEpbOidr1TU8m3oHNNrcOTGwA+ouiXWpnq90/1U/XV9rz9klIf3z2c7OAW4sPr
        y88Nox9BJ9xuupYJAI9Gfbfpv+rDc0p9/c291hbs4DXiwz6HOQmsf8BhU0IAwLToyU/qy01/vlTq85uv
        XNnBteIDKm3/1acdKT2mqSNLcABwx9Snm359qaef3PPXsoNLxYdSuk7NjtowenLPBwDcJ/Xtpn9PhZNm
        Vpv0dzu4RHwQrUdOnTvSCTXCCAA+MPXxpp+PlXLg6usC7OBc8QG09jiWnrprmAsXAOATUV9v+rsr5cFV
        1wrYwTn0xueP4OvdPQcA8Dmoz2e7t7U6mOzgJXrDfN9BKSXvah8lAMA+1O+bvu9qVTDZwSl6o3y/QWk6
        x7kjAHgg0fd1ld7Yct7iYLKDY/QG+T6D0pV3BBIAPCD1/yYHXC0KJjvoxAtrmubqH3c8AOCxKA8yFgY1
        +7SOHazFC2p65tYNCSQAQEe5kPFQlPJj1qYJdrAvXkjTMrdeyDkkAEBBudDkQ12/3PE1O9gXL+RuliKQ
        AACW8iG4TRW+u+P77GArXsCdR9I0jJtiAQCjlBNNXtQ1eX7JDko8cSzprrpbFwDwGJQXGRtFKVdGV9rs
        oMST3N26P9yxAAA4yo2Mj6JGd/2xg/EEXW1Xl6ZhnEcCAMym3Gjyoy57Nd5gQOJgd0kfy3YAgMWUHxkj
        RdlbigYDcaCbJf2sjwMAYC7lSMZJUYPZUvGHxEFulsQmqwCA1ZQjGSdFDWZLxR9xgNb+6mKWBAC4mvIk
        Y6Wo4lqF+gnuijtmSQCAqylPMlaKOvWPqZ9Q35c0a1sIAADmUK5kvHT1u/94/0DdfVsXV9wBADajXMl4
        KarbJah/oNvjjvuSAACbUa5kvBTV7YnXP7BeuuNnKQAAm4t8qXd56Jbw2gPcvUks3QEANqd8yZgp6nzP
        0tQBLN0BADanfMmYKeo8EWoPqG+Y5ao7AMBulDMZN12dTxmNPXjxh5gAAFhLOZNx09V5MtQ+WBfnkwAA
        u1HOZNz8XxrXA+7+JH5ZFgCwG+VMxk1RL3pgsO2DewEAALbURE6/XjVY73dXbPkAAMAeIm/qH/87uVBi
        V3AAwO6UNxk7Xb0TSgCAm1DeZOx0RSgBAG5DeZOx09U5lOo9iNjzDgCwu8gbQgkAcB8ib1i+AwDcB+VN
        xk5XhBIA4DaUNxk7XRFKAIDbUN5k7HRlQ4mbZwEAu4u8sTfPss0QAOBwTeT067zNEBuyAgAOpZzJuCnq
        pX2wrrf6BQAA2ErkjP/piuZBfuQPAHAY5UzGTVfFj/zxc+gAgMMoZzJuuip+Dn0wjYp66r8AAABbUL5k
        zBR1/sXz9oDnHCuKn0QHAGxO+ZIxU9SzHusf9DvHu2IPPADA5iJf6j1Xu/tj+wfVJ51ULOEBADajXMl4
        Kaq7uK5/oLtmnCU8AMBmlCsZL0V198bWB9dLeFyFBwDYjHIl46WrYmu7+uB6HzzVa/8YAADWUJ5krBR1
        6h9TP8Gt9bFrOADgasqTjJWiimsXiidIHFDfSKtitgQAWE05knFS1OAq7+IPiYPcPUvMlgAAqylHMk6K
        Ot+b1Ff80YoD3WyJK/EAAIspPzJGirL3wg4GJA52syX9GBP3LQEAZlNuNPlR12CWJIOBVjzBXYn3wx0L
        AICj3Mj4KOrdHSt2UOJJSrf6viUVy3gAgIuUFxkbRSlXRlfd7GArnuiultA0jF+mBQCMUk40eVHX5NXc
        drAvXsDtiac7cjm/BAAYUD4Et9J28Qdk7WBfvIhevN4WQkUwAQAKyoUmH+qatW2dHazFi+lqPDcN4+ct
        AAAd5ULGQ1HKD3u1Xc0OOvGC7vySimACAIwFkmr2rkB2cEy8sLuSQqU7dVnKA4AHpP7f5ICrRVds28Ep
        eoN8n0FxjgkAHkz0fZ3eceeQVItvIbKDl+iN8v0GpXVDNm8FgAegft/0fVer7mm1g3PoDfN9bY3erQsA
        +PjU57Pd21q9yYIdnEtvHMZSUtM5brIFgE9Efb3p766UB1ft+mMHl4gPoA/obpJqSzffcq4JAD4w9fGm
        n4+VcuDqiYgdXCo+yNSVFyqlp6Z6hBMAfCDq203/HlsVU2nT1U36ux1cKz7UKUx98DacZt1EBQC4DfXp
        pl9f6ukn9/y17OA14gPqi0zNmtpSsl619ggA2E70ZM2KdK2A+7mJutTnN59g2MEtxId9C1Pnmvqlf4Bm
        WVwYAQAHUt9t+u+cIFKpr7+519qCHdxSfHil7txwaksJrBNqmjoq3HQtfIulPwCYIfqlAqffP9VP1VfV
        X+esaPVLfXz31S07uAd9mbD0n0BRFEXdttS3DzvVYgf3FF9Oya2UXjp7oiiKoo4p9Wf16cNXpuzgUeIL
        K6A0lWQGRVEUddtSH1Y/vum5fTt4K/HP0JqnTri1651jdw1TFEVR60p9Vf1VfVb99o72K/325T+8fCZ3
        nGdyEQAAAABJRU5ErkJggg==
</value>
  </data>
</root>