﻿<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
	<PropertyGroup>
		<NativeDlls>$(MSBuildThisFileDirectory)..\..\runtimes</NativeDlls>
	</PropertyGroup>
	<ItemGroup Condition="!$(TargetFramework.Contains('netstandard')) And !$(TargetFramework.Contains('netcoreapp')) And !$(TargetFramework.Contains('net5'))">
		<Content Include="$(NativeDlls)\win-x86\native\OpenCvSharpExtern.dll">
			<Link>dll\x86\OpenCvSharpExtern.dll</Link>
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="$(NativeDlls)\win-x86\native\opencv_videoio_ffmpeg452.dll">
			<Link>dll\x86\opencv_videoio_ffmpeg452.dll</Link>
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>
	<ItemGroup Condition="!$(TargetFramework.Contains('netstandard')) And !$(TargetFramework.Contains('netcoreapp')) And !$(TargetFramework.Contains('net5'))">
		<Content Include="$(NativeDlls)\win-x64\native\OpenCvSharpExtern.dll">
			<Link>dll\x64\OpenCvSharpExtern.dll</Link>
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="$(NativeDlls)\win-x64\native\opencv_videoio_ffmpeg452_64.dll">
			<Link>dll\x64\opencv_videoio_ffmpeg452_64.dll</Link>
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>
</Project>
