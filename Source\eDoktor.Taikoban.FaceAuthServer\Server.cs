﻿//#define USE_HL_KEY // Use Hardware Key
#define USE_EOM // Use EngineObjectManager （インスタンス管理用）

#define LICENCE_ENABLED

using System;
using System.Collections.Generic;
using System.Threading; // ReaderWriterLockSlim
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSDK;
using GFRL.FaceTemplate;

namespace eDoktor.Taikoban.FaceAuthServer
{
    /// <summary>
    /// Server
    /// </summary>
    public class Server : System.IDisposable
    {
        #region Private Fields

        private System.ServiceProcess.ServiceBase _serviceBase;
        private readonly object SyncRoot = new object();
        private volatile bool Disposed;
        private eDoktor.Taikoban.FaceInterprocess.Server _server;

        /// <summary>
        /// データベース操作用インスタンス
        /// </summary>
        private Database _database;

        /// <summary>
        /// 設定情報
        /// </summary>
        private FaceAuthSettingsInfo.SettingsInfo _settingInfo;

        /// <summary>
        /// テンプレートリスト
        /// </summary>
        public List<FaceTemplateDataRegister> _templateDataRegList = new List<FaceTemplateDataRegister>();

        /// <summary>
        /// 顔テンプレートリスト更新用ロック
        /// </summary>
        private ReaderWriterLockSlim _lockObj = new ReaderWriterLockSlim();

        #region 暗号化用
        private static eDoktor.Common.CryptoParams CryptoParams;
        private static bool gettingCryptParms = false;
        #endregion

        #endregion

        #region Constructors

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="serviceBase"></param>
        public Server(System.ServiceProcess.ServiceBase serviceBase)
        {
            this._serviceBase = serviceBase;
            var connectionStringSettings = Configuration.ConnectionStringSettings(Properties.Settings.Default.ConnectionStringSettingsLabel);
            connectionStringSettings.ConnectionString = DecryptString(connectionStringSettings.ConnectionString);
            this._database = new Database(connectionStringSettings);
        }

        #endregion

        #region Finalizer
        ~Server()
        {
            Dispose(false);
        }
        #endregion

        #region IDisposable
        public void Dispose()
        {
            Dispose(true);
            System.GC.SuppressFinalize(this);
        }
        #endregion

        #region Protected Methods
        protected virtual void Dispose(bool disposing)
        {
            using (eDoktor.Common.TimedLock.Lock(SyncRoot))
            {
                if (Disposed)
                {
                    return;
                }

                Stop();

                Disposed = true;
            }
        }
        #endregion

        #region Public Methods

        /// <summary>
        /// サービス開始
        /// </summary>
        public void Start()
        {
            using (eDoktor.Common.TimedLock.Lock(SyncRoot))
            {
                if (Disposed)
                {
                    throw new System.ObjectDisposedException("SdncInfoAcquisition.Server");
                }
                Trace.OutputTrace("FaceAuthServer.Server 開始");

                Stop();

                // DBから各設定値を取得する
                var req = new ExecuteFaceSettingGetReq(this._database);
                var isGetSetting = req.InitialSettings(out this._settingInfo, out Dictionary<string, string> messageDic);

                if (isGetSetting == false)
                {
                    Trace.OutputErrorTrace("設定値の取得に失敗しました。");
                    throw new Exception("SettingInfo Acquisition Error");
                }

                // 取得した設定をログに出力
                Trace.OutputDebugTrace("設定情報取得");
                foreach (var property in this._settingInfo.GetType().GetProperties())
                {
                    Trace.OutputDebugTrace(" {0} : {1}", property.Name, property.GetValue(this._settingInfo));
                }

                Trace.OutputDebugTrace("メッセージ情報取得");
                foreach (var msg in messageDic)
                {
                    Messages.SetMessageList(msg.Key, msg.Value);
                    Trace.OutputDebugTrace(" {0} : {1}", msg.Key, msg.Value);
                }

#if LICENCE_ENABLED
                // サーバーモードの場合は、SDKのライセンスを確認する
                if (this._settingInfo.auth_mode == EnumAuthMode.Server)
                {
                    var isEnableLicense = LicenseCheck.CheckLicenseFile();

                    if (isEnableLicense == false)
                    {
                        Trace.OutputErrorTrace("ライセンスファイルの確認に失敗しました。");
                        throw new Exception("License Check Error");
                    }
                    else
                    {
                        Trace.OutputTrace("ライセンスファイルの確認に成功しました。");
                        Trace.OutputTrace("SDKの各インスタンスの初期化を行います。");

                        // SDKの各インスタンスの初期化を行う
                        var sDKInitialize = new SDKInitialize();
                        sDKInitialize.Initialize(this._settingInfo);

                        Trace.OutputTrace("SDKの各インスタンスの初期化が完了しました。");
                    }
                }
#endif // LICENCE_ENABLED

                // テンプレートを保持する設定の場合、テンプレートデータの保持更新処理のスレッドを起動する。
                if (this._settingInfo.is_hold_template_data)
                {
                    System.Threading.Tasks.Task task = System.Threading.Tasks.Task.Run(() =>
                    {
                        Trace.OutputDebugTrace("テンプレートデータ更新スレッド起動");
                        var db = new DB(this._database);

                        // テンプレート取得更新処理
                        this.GetTemplateDataUpdate(db);

                        // 設定値のテンプレート更新間隔を指定する
                        System.Timers.Timer timer = new System.Timers.Timer(_settingInfo.template_get_wait_time * 1000);

                        // タイマー処理
                        timer.Elapsed += (sender, e) =>
                        {
                            Trace.OutputDebugTrace("定期テンプレートデータ更新開始");

                            // ３．(2)-① テンプレート取得更新処理
                            this.GetTemplateDataUpdate(db);
                        };

                        Trace.OutputDebugTrace("タイマーを開始");
                        timer.Start();
                    });
                }

                _server = new eDoktor.Taikoban.FaceInterprocess.Server(FaceInterprocess.NodeType.FaceServer);
                _server.PacketReceived += new EventHandler<eDoktor.Common.PacketEventArgs>(OnPacketReceived);
                _server.Start();
            }
        }

        /// <summary>
        /// サービス停止
        /// </summary>
        public void Stop()
        {
            //StopRegistrationProc();
            using (eDoktor.Common.TimedLock.Lock(SyncRoot))
            {
                if (_server != null)
                {
                    Trace.OutputTrace("FaceAuthSever.Server 終了");
                    _server.Dispose();
                    _server = null;
                }
            }
        }

        #endregion

        /// <summary>
        /// テンプレートデータ取得更新処理
        /// </summary>
        /// <param name="db"></param>
        /// <returns>true:更新成功 false:更新失敗</returns>
        private bool GetTemplateDataUpdate(DB db)
        {
            try
            {
                // (3)-① 全職員のテンプレートリストを取得する。
                db.GetAllTemplateData(out List<FaceTemplateDataRegister> templateDataRegList);

                if (templateDataRegList.Count > 0)
                {
                    // (3)-② データが取得できた場合、(1)のテンプレートデータリストにロックをかけ、更新する。更新後ロックを解除する。
                    _lockObj.EnterWriteLock();
                    // Trace.OutputDebugTrace("テンプレートデータ更新時のロック開始");

                    try
                    {
                        _templateDataRegList = templateDataRegList;
                    }
                    finally
                    {
                        if (this._lockObj.IsWriteLockHeld)
                        {
                            this._lockObj.ExitWriteLock();
                            // Trace.OutputDebugTrace("テンプレートデータ更新時のロック解除");
                        }
                    }

                    Trace.OutputDebugTrace("定期テンプレートデータ更新成功");

                    return true;
                }
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }

            Trace.OutputErrorTrace("定期テンプレートデータ更新失敗");

            return false;
        }

        private string DecryptString(string text)
        {
            if (gettingCryptParms != true)
            {
                try
                {
                    const string algorithmName = "Rijndael";
                    const string password = "=o8^2a<~\"/R*";
                    const string salt = "E-/~mo?Z";
                    const int iterationCount = 1226;
                    const int keySize = 256;
                    const int blockSize = 128;

                    byte[] key = null;
                    byte[] iv = null;
                    eDoktor.Common.Crypto.DeriveKeyFromPassword(password, salt, iterationCount, keySize, out key, blockSize, out iv);
                    CryptoParams = new eDoktor.Common.CryptoParams();
                    CryptoParams.AlgorithmName = algorithmName;
                    CryptoParams.KeySize = keySize;
                    CryptoParams.Key = key;
                    CryptoParams.BlockSize = blockSize;
                    CryptoParams.IV = iv;
                    gettingCryptParms = true;
                }
                catch (System.Exception ex)
                {
                    Trace.OutputExceptionTrace(ex);
                }
            }
            return eDoktor.Common.Crypto.Decrypt(text, CryptoParams);
        }

        /// <summary>
        /// パケット受信
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void OnPacketReceived(object sender, eDoktor.Common.PacketEventArgs e)
        {
            try
            {
                FaceInterprocess.TcpClient client = sender as FaceInterprocess.TcpClient;
                FaceInterprocess.Packet packet = e.Packet as FaceInterprocess.Packet;

                switch (packet.Command)
                {
                    case FaceInterprocess.Packet.CommandType.FaceSettingGetReq:
                        Trace.OutputTrace("設定取得要求コマンドを受信しました({0})", client.RemoteEndPoint.Address.ToString());
                        ExecuteCmdQueryFaceSettingGetReq(client, packet);
                        break;

                    case FaceInterprocess.Packet.CommandType.FaceTemplateRegConfirmReq:
                        Trace.OutputTrace("顔テンプレート登録確認要求コマンドを受信しました({0})", client.RemoteEndPoint.Address.ToString());
                        ExecuteCmdQueryFaceTemplateRegConfirmReq(client, packet);
                        break;

                    case FaceInterprocess.Packet.CommandType.FaceTemplateGetReq:
                        Trace.OutputTrace("顔テンプレート取得要求コマンドを受信しました({0})", client.RemoteEndPoint.Address.ToString());
                        ExecuteCmdQueryFaceTemplateGetReq(client, packet);
                        break;

                    case FaceInterprocess.Packet.CommandType.FaceAuthtReq:
                        Trace.OutputTrace("顔認証要求コマンドを受信しました({0})", client.RemoteEndPoint.Address.ToString());
                        ExecuteCmdQueryFaceAuthtReq(client, packet);
                        break;

                    case FaceInterprocess.Packet.CommandType.FaceTemplateRegServerReq:
                        Trace.OutputTrace("顔テンプレート登録要求(サーバ認証モード)コマンドを受信しました({0})", client.RemoteEndPoint.Address.ToString());
                        ExecuteCmdQueryFaceTemplateRegServerReq(client, packet);
                        break;

                    case FaceInterprocess.Packet.CommandType.FaceTemplateRegClientReq:
                        Trace.OutputTrace("顔テンプレート登録要求(クライアント認証モード)コマンドを受信しました({0})", client.RemoteEndPoint.Address.ToString());
                        ExecuteCmdQueryFaceTemplateRegClientReq(client, packet);
                        break;

                    case FaceInterprocess.Packet.CommandType.FaceTemplateDelReq:
                        Trace.OutputTrace("顔テンプレート削除要求コマンドを受信しました({0})", client.RemoteEndPoint.Address.ToString());
                        ExecuteCmdQueryFaceTemplateDelReq(client, packet);
                        break;

                    case FaceInterprocess.Packet.CommandType.FaceTemplateAddUpdateReq:
                        Trace.OutputTrace("顔テンプレート追加更新要求コマンドを受信しました({0})", client.RemoteEndPoint.Address.ToString());
                        ExecuteCmdQueryFaceTemplateAddUpdateReq(client, packet);
                        break;

                    case FaceInterprocess.Packet.CommandType.IdentificationFaceTemplateRegConfirmReq:
                        Trace.OutputTrace("本人確認・顔テンプレート登録確認要求コマンドを受信しました({0})", client.RemoteEndPoint.Address.ToString());
                        ExecuteCmdQueryIdentificationFaceTemplateRegConfirmReq(client, packet);
                        break;

                    case FaceInterprocess.Packet.CommandType.UserSearchFaceAuthReq:
                        Trace.OutputTrace("ユーザ検索顔認証要求コマンドを受信しました({0})", client.RemoteEndPoint.Address.ToString());
                        ExecuteCmdQueryUserSearchFaceAuthReq(client, packet);
                        break;

                    case FaceInterprocess.Packet.CommandType.FaceAuthLogRegistReq:
                        Trace.OutputTrace("認証ログ登録(クライアント認証モード)要求コマンドを受信しました({0})", client.RemoteEndPoint.Address.ToString());
                        ExecuteCmdQueryFaceAuthLogRegistReq(client, packet);
                        break;

                    case FaceInterprocess.Packet.CommandType.LivenessLogRegistReq:
                        Trace.OutputTrace("なりすまし画像登録要求コマンドを受信しました({0})", client.RemoteEndPoint.Address.ToString());
                        ExecuteCmdQueryLivenessReq(client, packet);
                        break;

                    default:

                        Trace.OutputWarningTrace("invalid packet({0}) from {1}", packet.Command, client.RemoteEndPoint.Address.ToString());
                        break;
                }
            }
            catch (System.Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 設定取得要求コマンドに対する受信処理です
        /// </summary>
        /// <param name="client">コマンド送信もとのクライアント情報</param>
        /// <param name="packet">受信コマンドの内容</param>
        private void ExecuteCmdQueryFaceSettingGetReq(eDoktor.Taikoban.FaceInterprocess.TcpClient client, eDoktor.Taikoban.FaceInterprocess.Packet packet)
        {
            try
            {
                var req = new ExecuteFaceSettingGetReq(this._database);
                var xElement = req.Execute(packet);

                // 応答送信
                int sequence = packet.Sequence;
                eDoktor.Taikoban.FaceInterprocess.Packet.CommandType response = eDoktor.Taikoban.FaceInterprocess.Packet.GetResponseType(packet.Command);
                client.Transmit(packet.CreateRequestOrResponsePacket(response, sequence, xElement));
                Trace.OutputTrace("設定取得応答コマンドを送信しました({0})", client.RemoteEndPoint.Address.ToString());
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 顔テンプレート登録確認要求コマンドに対する受信処理です
        /// </summary>
        /// <param name="client">コマンド送信もとのクライアント情報</param>
        /// <param name="packet">受信コマンドの内容</param>
        private void ExecuteCmdQueryFaceTemplateRegConfirmReq(eDoktor.Taikoban.FaceInterprocess.TcpClient client, eDoktor.Taikoban.FaceInterprocess.Packet packet)
        {
            try
            {
                var req = new ExecuteFaceTemplateRegConfirmReq(this._database, this._settingInfo);
                var xElement = req.Execute(packet);

                // 応答送信
                int sequence = packet.Sequence;
                eDoktor.Taikoban.FaceInterprocess.Packet.CommandType response = eDoktor.Taikoban.FaceInterprocess.Packet.GetResponseType(packet.Command);
                client.Transmit(packet.CreateRequestOrResponsePacket(response, sequence, xElement));
                Trace.OutputTrace("顔テンプレート登録確認応答コマンドを送信しました({0})", client.RemoteEndPoint.Address.ToString());
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 顔テンプレート取得要求コマンドに対する受信処理です  ※ 未実装 ※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
        /// </summary>
        /// <param name="client">コマンド送信もとのクライアント情報</param>
        /// <param name="packet">受信コマンドの内容</param>
        private void ExecuteCmdQueryFaceTemplateGetReq(eDoktor.Taikoban.FaceInterprocess.TcpClient client, eDoktor.Taikoban.FaceInterprocess.Packet packet)
        {
            try
            {
                var req = new ExecuteFaceTemplateGetReq(this._database, this._settingInfo);
                var xElement = req.Execute(packet);

                // 応答送信
                int sequence = packet.Sequence;
                eDoktor.Taikoban.FaceInterprocess.Packet.CommandType response = eDoktor.Taikoban.FaceInterprocess.Packet.GetResponseType(packet.Command);
                client.Transmit(packet.CreateRequestOrResponsePacket(response, sequence, xElement));
                Trace.OutputTrace("顔テンプレート取得応答コマンドを送信しました({0})", client.RemoteEndPoint.Address.ToString());
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 顔認証要求コマンドに対する受信処理です
        /// </summary>
        /// <param name="client">コマンド送信もとのクライアント情報</param>
        /// <param name="packet">受信コマンドの内容</param>
        private void ExecuteCmdQueryFaceAuthtReq(eDoktor.Taikoban.FaceInterprocess.TcpClient client, eDoktor.Taikoban.FaceInterprocess.Packet packet)
        {
            try
            {
                var req = new ExecuteFaceAuthtReq(this._database, this._settingInfo);
                var xElement = req.Execute(packet);

                int sequence = packet.Sequence;
                eDoktor.Taikoban.FaceInterprocess.Packet.CommandType response = eDoktor.Taikoban.FaceInterprocess.Packet.GetResponseType(packet.Command);
                client.Transmit(packet.CreateRequestOrResponsePacket(response, sequence, xElement));
                var ip = client.RemoteEndPoint.Address.ToString();
                Trace.OutputTrace("顔認証応答コマンドを送信しました({0})", ip);

                // ログ登録
                req.RegistLog(ip.Remove(0, ip.LastIndexOf(':') + 1));
                // 顔学習
                req.UpdateFace();
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 顔テンプレート登録要求(サーバ認証モード)コマンドに対する受信処理です
        /// </summary>
        /// <param name="client">コマンド送信もとのクライアント情報</param>
        /// <param name="packet">受信コマンドの内容</param>
        private void ExecuteCmdQueryFaceTemplateRegServerReq(eDoktor.Taikoban.FaceInterprocess.TcpClient client, eDoktor.Taikoban.FaceInterprocess.Packet packet)
        {
            try
            {
                var req = new ExecuteFaceTemplateRegServerReq(this._database, this._settingInfo);
                var xElement = req.Execute(packet);

                int sequence = packet.Sequence;
                eDoktor.Taikoban.FaceInterprocess.Packet.CommandType response = eDoktor.Taikoban.FaceInterprocess.Packet.GetResponseType(packet.Command);
                client.Transmit(packet.CreateRequestOrResponsePacket(response, sequence, xElement));
                Trace.OutputTrace("顔テンプレート登録応答(サーバ認証モード)コマンドを送信しました({0})", client.RemoteEndPoint.Address.ToString());
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 顔テンプレート登録要求(クライアント認証モード)コマンドに対する受信処理です  ※ 未実装 ※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
        /// </summary>
        /// <param name="client">コマンド送信もとのクライアント情報</param>
        /// <param name="packet">受信コマンドの内容</param>
        private void ExecuteCmdQueryFaceTemplateRegClientReq(eDoktor.Taikoban.FaceInterprocess.TcpClient client, eDoktor.Taikoban.FaceInterprocess.Packet packet)
        {
            try
            {
                var req = new ExecuteFaceTemplateRegClientReq(this._database, this._settingInfo);
                var xElement = req.Execute(packet);

                int sequence = packet.Sequence;
                eDoktor.Taikoban.FaceInterprocess.Packet.CommandType response = eDoktor.Taikoban.FaceInterprocess.Packet.GetResponseType(packet.Command);
                client.Transmit(packet.CreateRequestOrResponsePacket(response, sequence, xElement));
                Trace.OutputTrace("顔テンプレート登録応答(クライアント認証モード)コマンドを送信しました({0})", client.RemoteEndPoint.Address.ToString());
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 顔テンプレート削除要求コマンドに対する受信処理です
        /// </summary>
        /// <param name="client">コマンド送信もとのクライアント情報</param>
        /// <param name="packet">受信コマンドの内容</param>
        private void ExecuteCmdQueryFaceTemplateDelReq(eDoktor.Taikoban.FaceInterprocess.TcpClient client, eDoktor.Taikoban.FaceInterprocess.Packet packet)
        {
            try
            {
                var req = new ExecuteFaceTemplateDelReq(this._database);
                var xElement = req.Execute(packet);

                int sequence = packet.Sequence;
                eDoktor.Taikoban.FaceInterprocess.Packet.CommandType response = eDoktor.Taikoban.FaceInterprocess.Packet.GetResponseType(packet.Command);
                client.Transmit(packet.CreateRequestOrResponsePacket(response, sequence, xElement));
                Trace.OutputTrace("顔テンプレート削除応答コマンドを送信しました({0})", client.RemoteEndPoint.Address.ToString());
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 顔テンプレート追加更新要求コマンドに対する受信処理です  ※ 未実装 ※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
        /// </summary>
        /// <param name="client">コマンド送信もとのクライアント情報</param>
        /// <param name="packet">受信コマンドの内容</param>
        private void ExecuteCmdQueryFaceTemplateAddUpdateReq(eDoktor.Taikoban.FaceInterprocess.TcpClient client, eDoktor.Taikoban.FaceInterprocess.Packet packet)
        {
            try
            {
                var req = new ExecuteFaceTemplateAddUpdateReq(this._database, this._settingInfo);
                var xElement = req.Execute(packet);

                int sequence = packet.Sequence;
                eDoktor.Taikoban.FaceInterprocess.Packet.CommandType response = eDoktor.Taikoban.FaceInterprocess.Packet.GetResponseType(packet.Command);
                client.Transmit(packet.CreateRequestOrResponsePacket(response, sequence, xElement));
                Trace.OutputTrace("顔テンプレート追加更新応答コマンドを送信しました({0})", client.RemoteEndPoint.Address.ToString());
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 本人確認・顔テンプレート登録確認要求コマンドに対する受信処理です
        /// </summary>
        /// <param name="client">コマンド送信もとのクライアント情報</param>
        /// <param name="packet">受信コマンドの内容</param>
        private void ExecuteCmdQueryIdentificationFaceTemplateRegConfirmReq(eDoktor.Taikoban.FaceInterprocess.TcpClient client, eDoktor.Taikoban.FaceInterprocess.Packet packet)
        {
            try
            {
                var req = new ExecuteIdentificationFaceTemplateRegConfirmReq(this._database, this._settingInfo);
                var xElement = req.Execute(packet);

                int sequence = packet.Sequence;
                eDoktor.Taikoban.FaceInterprocess.Packet.CommandType response = eDoktor.Taikoban.FaceInterprocess.Packet.GetResponseType(packet.Command);
                client.Transmit(packet.CreateRequestOrResponsePacket(response, sequence, xElement));
                Trace.OutputTrace("本人確認・顔テンプレート登録確認応答コマンドを送信しました({0})", client.RemoteEndPoint.Address.ToString());
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// ユーザ検索顔認証要求コマンドに対する受信処理です
        /// </summary>
        /// <param name="client">コマンド送信もとのクライアント情報</param>
        /// <param name="packet">受信コマンドの内容</param>
        private void ExecuteCmdQueryUserSearchFaceAuthReq(eDoktor.Taikoban.FaceInterprocess.TcpClient client, eDoktor.Taikoban.FaceInterprocess.Packet packet)
        {
            try
            {
                var templateDataRegList = new List<FaceTemplateDataRegister>();
                if (this._settingInfo.is_hold_template_data)
                {
                    this._lockObj.EnterReadLock();
                    Trace.OutputDebugTrace("テンプレートデータ取得時のロック開始");

                    try
                    {
                        templateDataRegList = this._templateDataRegList;
                    }
                    finally
                    {
                        if (this._lockObj.IsReadLockHeld)
                        {
                            this._lockObj.ExitReadLock();
                            Trace.OutputDebugTrace("テンプレートデータ取得時のロック解除");
                        }
                    }
                }

                var req = new ExecuteUserSearchFaceAuthReq(this._database, this._settingInfo, templateDataRegList);
                var xElement = req.Execute(packet);

                int sequence = packet.Sequence;
                eDoktor.Taikoban.FaceInterprocess.Packet.CommandType response = eDoktor.Taikoban.FaceInterprocess.Packet.GetResponseType(packet.Command);
                client.Transmit(packet.CreateRequestOrResponsePacket(response, sequence, xElement));
                var ip = client.RemoteEndPoint.Address.ToString();
                Trace.OutputTrace("ユーザ検索顔認証応答コマンドを送信しました({0})", ip);

                // ログ登録
                req.RegistLog(ip.Remove(0, ip.LastIndexOf(':') + 1));
                // 顔学習
                req.UpdateFace();
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 認証ログ登録(クライアント認証モード)要求コマンドに対する受信処理です  ※ 未実装 ※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※※
        /// </summary>
        /// <param name="client">コマンド送信もとのクライアント情報</param>
        /// <param name="packet">受信コマンドの内容</param>
        private void ExecuteCmdQueryFaceAuthLogRegistReq(eDoktor.Taikoban.FaceInterprocess.TcpClient client, eDoktor.Taikoban.FaceInterprocess.Packet packet)
        {
            try
            {
                var req = new ExecuteFaceTemplateAddUpdateReq(this._database, this._settingInfo);
                var xElement = req.Execute(packet);

                int sequence = packet.Sequence;
                eDoktor.Taikoban.FaceInterprocess.Packet.CommandType response = eDoktor.Taikoban.FaceInterprocess.Packet.GetResponseType(packet.Command);
                client.Transmit(packet.CreateRequestOrResponsePacket(response, sequence, xElement));
                Trace.OutputTrace("認証ログ登録(クライアント認証モード)応答コマンドを送信しました({0})", client.RemoteEndPoint.Address.ToString());
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// なりすまし画像登録認証要求コマンドに対する受信処理です
        /// </summary>
        /// <param name="client">コマンド送信もとのクライアント情報</param>
        /// <param name="packet">受信コマンドの内容</param>
        private void ExecuteCmdQueryLivenessReq(eDoktor.Taikoban.FaceInterprocess.TcpClient client, eDoktor.Taikoban.FaceInterprocess.Packet packet)
        {
            try
            {
                var req = new ExecuteLivenessReq(this._database, this._settingInfo);
                var xElement = req.Execute(packet);

                int sequence = packet.Sequence;
                eDoktor.Taikoban.FaceInterprocess.Packet.CommandType response = eDoktor.Taikoban.FaceInterprocess.Packet.GetResponseType(packet.Command);
                client.Transmit(packet.CreateRequestOrResponsePacket(response, sequence, xElement));
                var ip = client.RemoteEndPoint.Address.ToString();
                Trace.OutputTrace("なりすまし画像登録応答コマンドを送信しました({0})", ip);

                // ログ登録
                req.RegistLog(ip.Remove(0, ip.LastIndexOf(':') + 1));
                // 顔学習
                req.UpdateFace();
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }
        }
    }
}
