﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using eDoktor.Taikoban.FaceAuthTemplateUpdateJudge;
using GFRL.FaceRecognition;

namespace eDoktor.Taikoban.FaceAuthServer
{
    /// <summary>
    /// 顔学習処理
    /// </summary>
    public class FaceLearnClass
    {
        /// <summary>
        /// データベース
        /// </summary>
        DB _db;

        /// <summary>
        /// テンプレート更新判定インスタンス
        /// </summary>
        TemplateUpdateJudge _templateUpdateJudge = new TemplateUpdateJudge();

        /// <summary>
        /// テンプレートリスト
        /// </summary>
        List<TemplateInfo> _templateDataInfoList;

        // ▼ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
        /// <summary>
        /// マスク着用と判断するためのマスクスコア閾値（この値以上でマスクあり）
        /// </summary>
        private readonly float _maskScoreThresholdForWearingMask = eDoktor.Common.Configuration.AppSetting("MaskScoreThresholdForWearingMask", 50);
        // ▲ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara

        /// <summary>
        /// コンストラクタ
        /// </summary>
        public FaceLearnClass()
        {
            _templateUpdateJudge = new TemplateUpdateJudge();
        }

        /// <summary>
        /// 顔学習処理
        /// </summary>
        /// <param name="client">コマンド送信もとのクライアント情報</param>
        /// <param name="packet">受信コマンドの内容</param>
        /// <returns>true:正常終了 false:異常終了</returns>
        public void FaceLearn(FaceRetrieveResult faceRetrieveResult, SettingsInfo settingInfo, int accountId, DB db)
        {
            if (faceRetrieveResult == null || settingInfo == null || accountId <= 0 || db == null)
            {
                Trace.OutputErrorTrace("argument is null");
                return;
            }

            this._db = db;

            // ① 登録できるテンプレートデータか判定を行う
            var checkResult = _templateUpdateJudge.TemplateThresholdCheck(faceRetrieveResult.FaceInfo.FaceFeature.TemplateQuality, faceRetrieveResult.FaceInfo.FaceFeature.MaskScore, settingInfo);

            if (checkResult == false)
            {
                Trace.OutputErrorTrace("閾値を満たさないテンプレートの為、追加もしくは更新は行わない。");
                return;
            }

            Trace.OutputDebugTrace("登録対象のテンプレート情報あり");

            // ▼ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
            // ② DBから対象のテンプレートデータを取得する（マスクあり／なし）
            //var result = this._db.GetTemplateData(accountId, out this._templateDataInfoList);
            var result = this._db.GetTemplateData(accountId, out this._templateDataInfoList, faceRetrieveResult.FaceInfo.FaceFeature.MaskScore >= _maskScoreThresholdForWearingMask);
            // ▲ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara

            // ▼ DELETE 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
            //if (this._templateDataInfoList.Count > 0)
            //{
            // ▲ DELETE 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
                if (this._templateDataInfoList.Count >= settingInfo.face_template_regist_max_count)
                {
                    // 取得したテンプレートリスト数が、設定．顔テンプレート登録上限以上の場合、更新
                    this.UpdateTemplate(faceRetrieveResult.FaceInfo.FaceFeature, accountId);
                }
                else
                {
                    // 取得したテンプレートリスト数が設定．顔テンプレート登録上限未満の場合(0件の場合も含む)登録
                    this.InsertTemplate(faceRetrieveResult.FaceInfo.FaceFeature, settingInfo.face_template_regist_max_count, accountId);
                }
            // ▼ DELETE 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
            //}
            // ▲ DELETE 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
        }

        /// <summary>
        /// テンプレート登録
        /// </summary>
        /// <param name="faceFeature">登録顔情報</param>
        /// <param name="registMaxCount">設定.顔テンプレート登録上限</param>
        /// <param name="accountId">アカウントID</param>
        private void InsertTemplate(FaceFeatureProperty faceFeature, int registMaxCount, int accountId)
        {
            // ▼ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
            //ServerCommonProc.GetInsertTemplteId(registMaxCount, this._templateDataInfoList, out int templateId);
            ServerCommonProc.GetInsertTemplteId(registMaxCount, this._templateDataInfoList, faceFeature.MaskScore, out int templateId);
            // ▲ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara

            Trace.OutputDebugTrace("テンプレート登録");

            // 上限に達していない場合は、アカウントIDを基にテンプレートデータを登録する
            // ▼ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
            //var ret = this._db.InsertTemplateData(accountId, templateId, faceFeature.TemplateData, faceFeature.TemplateQuality);
            var ret = this._db.InsertTemplateData(accountId, templateId, faceFeature.TemplateData, faceFeature.TemplateQuality, faceFeature.MaskScore);
            // ▲ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara

            if (ret)
            {
                // 登録成功
                Trace.OutputDebugTrace($"登録が完了しました。アカウントID:{accountId},テンプレートID:{templateId}");
            }
            else
            {
                Trace.OutputDebugTrace($"登録に失敗しましたしました。アカウントID:{accountId},テンプレートID:{templateId}");
            }
        }

        /// <summary>
        /// テンプレート更新
        /// </summary>
        /// <param name="faceFeature">登録顔情報</param>
        /// <param name="registMaxCount">設定.顔テンプレート登録上限</param>
        private void UpdateTemplate(FaceFeatureProperty faceFeature, int accountId)
        {
            // 登録あり（上限）
            Trace.OutputDebugTrace(Messages.Message(MessageId.Message009));

            // ④ 顔テンプレートアップデート対象取得処理を行い、更新対象のテンプレートIDを取得する。
            int updateTemplateId = this._templateUpdateJudge.FaceTemplateUpdateJudge(faceFeature.TemplateQuality, this._templateDataInfoList);

            Trace.OutputDebugTrace("テンプレート更新");

            if (updateTemplateId > 0)
            {
                // ⑤ 対象職員のアカウントID、テンプレートIDのテンプレートデータ、テンプレート評価値を更新する。
                // ▼ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara
                //var ret = this._db.UpdateTemplateData(accountId, updateTemplateId, faceFeature.TemplateData, faceFeature.TemplateQuality);
                var ret = this._db.UpdateTemplateData(accountId, updateTemplateId, faceFeature.TemplateData, faceFeature.TemplateQuality, faceFeature.MaskScore);
                // ▲ MODIFY 顔学習でのマスクありなし対応 2022/03/05 eDoktor Y.Kihara

                if (ret)
                {
                    Trace.OutputDebugTrace($"登録が完了しました。アカウントID:{accountId},テンプレートID:{updateTemplateId}");
                }
                else
                {
                    Trace.OutputDebugTrace($"登録に失敗しましたしました。アカウントID:{accountId},テンプレートID:{updateTemplateId}");
                }
            }
            else
            {
                Trace.OutputErrorTrace("更新対象なし : {0}", updateTemplateId);
            }
        }
    }
}
