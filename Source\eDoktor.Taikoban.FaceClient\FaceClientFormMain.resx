﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="backgroundWorker2.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="picCancel.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAALQAAAAoCAYAAABXadAKAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAA8pJREFUeF7tnOFx2zAMRtM9skgGySBdJH+yRQbJIhnEBcBnlpRIWVRkm+Th3elq
        fgBowfqs0rKjF8dxHGd0LpfLq2zvsn2z/cjmOPdA/fUl2zv2Ow+ZVI2skzvOs1D/vWHJ48gkHzad4/TB
        B9ZsQwr1rFxaUqimy4432V5Jd5xTwV/qs9LKQD2433uaTFGKrmncwM7DUd/JtjT2flOTnHL+wtxxGlEf
        BjtGfgjVkaTlmvkvIcd5OurHYMtIfU0tQV23pPiZ2ekO9WWwZ6S89JBAuk75Qnac7hB/6me6K2WvErzi
        HwAnRY7tHx4Oi/rTXPqf3K8ipKfx6c7O9HVh2C3s5in7yVTd93wUaS29eJEvj0VIlxvTrZ3p624Hl+kN
        pEMwhRt6B9JaehL+Rg6oEHTj918xdgZ93f3g8jSHn4fyQ/WUxlqGMxs6vYixMnQEaTpob3XAFaQVhJte
        E0o2a0hZ5SBnOtIKwhHkqDPc3I+RkdbSdXR+TRrRQJoKWltBuAppza8JZdU6wqs48grCBlK1luHNfZgB
        WjSQAmgG0vDQzpVP/m3qj5JVDXIEOYJcfS7CFudhhiUVIFyME4oxhtW5ZoAWDaQAmoE0HbTX3B9l8Q1x
        JYk1zUlJsYZQdT7Cu2oZNu3baNCigRRAM5CmhBZ390h6BDmC3PSaUVKsIdQcUwjHOMMsH6k6x2jQjoEU
        QDOQpoQWVwf9CnKqV5cqyE2vFyXVGsLFOKHdtQyzfKTqHKNBOwZSAM1AmgpaK0JKFdJWechNrxcl1RrC
        h56LlJjDMKtB2pxnJGjHQAqgGUhDQysZhA4dVEpOnaMEKVkO0q/rGG7OMxq0ZCAF0AykaaHNpj4psRoe
        Hq7fgrTmY0BZVocUNYbNc/cMLRlIARmn34tP/cMkemw6sJQ0m4EyA2kTUg8/D0MDyTQeNs/bO7RlIAVk
        PPVX30vos+kAU7KrhlQDaReUnFKDHEGeBmlp86vv1NDT/7CfPk81G+EIcjOUb0Lq5j4RMpCmQtqq/0JU
        hDR4+2+1Boc+mw40JVOaY0TkUNR/ISrC9g+mHacj1J/BpgHkHNFTx+drEsfpCPHn7T8XlMDyLO1/JOt0
        h/oy2DNSv4ghQb+NgdMt6sdgy8jtW4NJkt9oxukO9WGwY2TfxQtJ1KXH0tS6ZvEPis7DUd/Jll5WVtSf
        +/2oyRQtUWPrO2X6L1+c5yDeUu9db9ZY8mCbmVOk0G+n6/TEsdvppsgk+m5JL5M4zqO5z5JXJtX/BnTy
        5ZrGcc5ClxTqL93Ub/7ZzXEcZwJeXv4B1o93FAddD/8AAAAASUVORK5CYII=
</value>
  </data>
</root>