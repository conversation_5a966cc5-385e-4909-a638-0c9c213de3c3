﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace eDoktor.Taikoban.FaceInterprocess
{
    public class Server : System.IDisposable
    {
        #region Fields
        private readonly object _syncRoot = new object();
        private NodeType _nodeType;
        private TcpServer _tcpServer;
        private int _portNumber;
        private int _backlog;
        private int _maxAcceptableClientCount;
        private uint _keepAliveTime;
        private readonly uint _keepAliveTimeInterval = 10000;
        #endregion

        #region Events
        public event System.EventHandler ClientConnected;
        public event System.EventHandler ClientDisconnected;
        public event System.EventHandler<eDoktor.Common.PacketEventArgs> PacketReceived;
        #endregion

        #region Properties
        public NodeType NodeType
        {
            get { return _nodeType; }
        }
        #endregion

        #region Constructors
        public Server(NodeType nodeType)
        {
            _nodeType = nodeType;

            switch (_nodeType)
            {
                case NodeType.FaceServer:
                    _portNumber = Configuration.FaceServerPortNumber;
                    _backlog = Configuration.FaceServerBacklog;
                    _maxAcceptableClientCount = Configuration.FaceServerMaxAcceptableClientCount;
                    _keepAliveTime = (uint)Configuration.FaceServerKeepAliveTime;
                    
                    break;
                default:
                    throw new System.ArgumentOutOfRangeException("nodeType");
            }
        }
        #endregion

        #region Finalizer
        ~Server()
        {
            Dispose(false);
        }
        #endregion

        #region IDisposable
        public  void Dispose()
        {
            Dispose(true);
            System.GC.SuppressFinalize(this);
        }
        #endregion

        #region Public Methods
        public void Start()
        {
            try
            {
                using (eDoktor.Common.TimedLock.Lock(_syncRoot))
                {
                    Stop();

                    _tcpServer = new TcpServer();
                    _tcpServer.ClientConnected += new System.EventHandler(OnClientConnected);
                    _tcpServer.ClientDisconnected += new System.EventHandler(OnClientDisconnected);
                    _tcpServer.NonResponsePacketReceived += new System.EventHandler<eDoktor.Common.PacketEventArgs>(OnPacketReceived);

                    if (_maxAcceptableClientCount != 0)
                    {
                        _tcpServer.MaxAcceptableClientCount = _maxAcceptableClientCount;
                    }

                    _tcpServer.Listen(_portNumber, _backlog, true, false, _keepAliveTime, _keepAliveTimeInterval);
                }
            }
            catch (System.Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        public void Stop()
        {
            try
            {
                using (eDoktor.Common.TimedLock.Lock(_syncRoot))
                {
                    if (_tcpServer != null)
                    {
                        _tcpServer.Dispose();
                        _tcpServer = null;
                    }
                }
            }
            catch (System.Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }
        #endregion

        #region Protected Methods
        protected virtual void Dispose(bool disposing)
        {
            Stop();

            if (disposing)
            {
                ClientConnected = null;
                ClientDisconnected = null;
                PacketReceived = null;
            }
        }
        #endregion

        #region Private Methods
        private void OnClientConnected(object sender, System.EventArgs e)
        {
            try
            {
                System.EventHandler clientConnected = ClientConnected;

                if (clientConnected != null)
                {
                    clientConnected.Invoke(sender, e);
                }
            }
            catch (System.Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        private void OnClientDisconnected(object sender, System.EventArgs e)
        {
            try
            {
                System.EventHandler clientDisconnected = ClientDisconnected;

                if (clientDisconnected != null)
                {
                    clientDisconnected.Invoke(sender, e);
                }
            }
            catch (System.Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        void OnPacketReceived(object sender, eDoktor.Common.PacketEventArgs e)
        {
            try
            {
                System.EventHandler<eDoktor.Common.PacketEventArgs> packetReceived = PacketReceived;

                if (packetReceived != null)
                {
                    packetReceived.Invoke(sender, e);
                }
            }
            catch (System.Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }
        #endregion
    }
}
