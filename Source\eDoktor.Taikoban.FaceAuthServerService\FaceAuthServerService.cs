﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.ServiceProcess;
using System.Text;
using System.Threading.Tasks;

namespace eDoktor.Taikoban.FaceAuthServerService
{
    public partial class FaceAuthServerService : ServiceBase
    {
        eDoktor.Taikoban.FaceAuthServer.Server _remoteServer = null;

        public FaceAuthServerService()
        {
            InitializeComponent();
        }

        protected override void OnStart(string[] args)
        {
            try
            {
                OnStop();

                _remoteServer = new eDoktor.Taikoban.FaceAuthServer.Server(this);
                _remoteServer.Start();
            }
            catch (Exception e)
            {
                eDoktor.Common.Trace.OutputCriticalTrace(e.Message);
                this.Stop();
            }
        }

        protected override void OnStop()
        {
            try
            {
                if (_remoteServer != null)
                {
                    _remoteServer.Stop();
                    _remoteServer = null;
                }
            }
            catch (Exception e)
            {
                eDoktor.Common.Trace.OutputCriticalTrace(e.Message);
            }
        }
    }
}
