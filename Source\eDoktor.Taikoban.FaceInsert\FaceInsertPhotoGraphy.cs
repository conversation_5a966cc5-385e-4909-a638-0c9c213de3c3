﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AForge.Video;
using AForge.Video.DirectShow;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using eDoktor.Taikoban.FaceDetection;
using eDoktorTaikoban.FaceClientCommon;

namespace eDoktor.Taikoban.FaceInsert
{
    /// <summary>
    /// 撮影登録画面
    /// </summary>
    public partial class FaceInsertPhotoGraphy : Form
    {
        #region Private Fields

        /// <summary>
        /// 顔検出実行カウンター MAX値
        /// </summary>
        private const int CST_FACE_DETECTION_EXECUTION_COUNTER_MAX = 10;
        /// <summary>
        /// 顔検出状態表示間隔(ms)
        /// </summary>
        private const int CST_FACE_DETECTION_STATE_DISPLAY_INTERVAL = 100;

        /// <summary>
        /// 要求送信クラス
        /// </summary>
        RequestSendClass _requestSend;

        /// <summary>
        /// アカウントID
        /// </summary>
        private int _accountId = 0;

        /// <summary>
        /// 設定情報
        /// </summary>
        private SettingsInfo _settingInfo;

        /// <summary>
        /// カメラデバイス ホワイトリスト
        /// </summary>
        private List<string> _whiteList;

        /// <summary>
        /// カメラデバイス ブラックリスト
        /// </summary>
        private List<string> _blackList;

        /// <summary>
        /// カメラ画像反転フラグ(true時反転)
        /// </summary>
        private bool _isFlipHorizon = true;

        /// <summary>
        /// 映像取得
        /// </summary>
        private VideoCaptureDevice _videoCaptureDevice = new VideoCaptureDevice();

        /// <summary>
        /// 画面表示高さ
        /// </summary>
        private int _height = 0;

        /// <summary>
        /// 画面表示幅
        /// </summary>
        private int _width = 0;

        /// <summary>
        /// 現在の取得イメージ
        /// </summary>
        private Bitmap _currentImage;

        /// <summary>
        /// なりすまし判定処理クラス
        /// </summary>
        private FaceDetection.LivenessCheckDetection _livenessCheckDetection = null;

        /// <summary>
        /// 顔検出実行処理用カウンター
        /// </summary>
        private int _faceDetectionCounter = 0;

        /// <summary>
        /// 顔認識状態表示用タイマー
        /// </summary>
        private System.Windows.Forms.Timer _faceDetectionStateTimer = null;

        /// <summary>
        /// 生体判定エラーメッセージダイアログ
        /// </summary>
        private MessageDialog _messageDialog;

        /// <summary>
        /// デキュー用ビットマップ
        /// </summary>
        private Bitmap[] _dequeueBitmap;

        /// <summary>
        /// 現在の取得イメージプロパティ
        /// </summary>
        private Bitmap _CurrentImage
        {
            get
            {
                lock (this._currentImage)
                {
                    return new Bitmap(this._currentImage, this._width, this._height);
                }
            }
            set
            {
                lock (this._currentImage)
                {
                    this._currentImage = (Bitmap)value.Clone();
                }
            }
        }

        /// <summary>
        /// ボタンアクションクラス
        /// </summary>
        BtnImageCahnge _btnImageCahnge = new BtnImageCahnge();

        #endregion

        #region Constructors

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="whiteList">デバイスホワイトリスト</param>
        /// <param name="blackList">デバイスブラックリスト</param>
        /// <param name="staffId">職員ID</param>
        /// <param name="staffName">職員名</param>
        /// <param name="settingInfo">設定情報</param>
        public FaceInsertPhotoGraphy(List<string> whiteList, List<string> blackList, string staffId, string staffName, int accountId, SettingsInfo settingInfo)
        {
            InitializeComponent();

            this._requestSend = new RequestSendClass();
            this._whiteList = whiteList;
            this._blackList = blackList;
            this._accountId = accountId;
            this._settingInfo = settingInfo;

            // 本人確認時に取得した職員IDと職員名を設定する
            this.label_staffId.Text = staffId;
            this.label_staffName.Text = staffName;

            this._currentImage = new Bitmap(this.pictureBox1.Width, this.pictureBox1.Height);
            this._isFlipHorizon = eDoktor.Common.Configuration.AppSetting("IsFlipHorizon", true);

            this._faceDetectionStateTimer = new System.Windows.Forms.Timer();
            this._faceDetectionStateTimer.Tick += new EventHandler(DispFaceDetectionState);
            this._faceDetectionStateTimer.Interval = CST_FACE_DETECTION_STATE_DISPLAY_INTERVAL;
            this._faceDetectionStateTimer.Enabled = false;

        }

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="requestSend">要求送信クラス</param>
        /// <param name="whiteList">デバイスホワイトリスト</param>
        /// <param name="blackList">デバイスブラックリスト</param>
        /// <param name="staffId">職員ID</param>
        /// <param name="staffName">職員名</param>
        /// <param name="settingInfo">設定情報</param>
        public FaceInsertPhotoGraphy(RequestSendClass requestSend, List<string> whiteList, List<string> blackList, string staffId, string staffName, int accountId, SettingsInfo settingInfo)
            : this(whiteList, blackList, staffId, staffName, accountId, settingInfo)
        {
            this._requestSend = requestSend;
        }

        #endregion

        /// <summary>
        /// フォームロード
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FaceInsertPhotoGraphy_Load(object sender, EventArgs e)
		{
			try
			{
				// 使用できるカメラをチェックする
				var useDeviceMonikerString = CameraInfo.GetUseDevice(_whiteList, _blackList);

				// 使用できるカメラがない場合
				if (string.IsNullOrEmpty(useDeviceMonikerString))
				{
					// 使用できるカメラがありません。
					var message = Messages.Message(MessageId.Message007);
					eDoktor.Common.Trace.OutputDebugTrace(message);
					MessageBox.Show(message);

					this.Close();
					return;
				}

				// カメラ起動
				this._videoCaptureDevice = new VideoCaptureDevice(useDeviceMonikerString);

				var capabilities = this._videoCaptureDevice?.VideoCapabilities?.FirstOrDefault();

                // 使用できるカメラがない場合
                if (capabilities == null)
                {
                    // 使用できるカメラがありません。
                    var message = Messages.Message(MessageId.Message007);
                    eDoktor.Common.Trace.OutputDebugTrace(message);
                    MessageBox.Show(message);

                    this.Close();
                    return;
                }

                // 画面の表示領域に入るように調整する高さと幅を計算
                this._height = this.pictureBox1.Height;
				this._width = this.pictureBox1.Width;

				var location = this.pictureBox1.Location;

				// 画面の表示領域に入るように調整
				ClientCommonProc.PictureBoxResize(capabilities.FrameSize, ref this._width, ref this._height, ref location);

				// 表示位置調整
				this.pictureBox1.Location = location;
                this.pictureBox1.Height = this._height;
                this.pictureBox1.Width = this._width;


                this._videoCaptureDevice.VideoResolution = capabilities;
				this._videoCaptureDevice.NewFrame += videoCaptureDevice_NewFrame;


				this._videoCaptureDevice.Start();

				//なりすまし検出処理開始
				_messageDialog = new FaceDetection.MessageDialog();
				_messageDialog.error_message_text.Text = this._settingInfo.liveness_check_error_message;
				this._livenessCheckDetection = new FaceDetection.LivenessCheckDetection(this._settingInfo, _messageDialog);

				//なりすまし判定を使わない設定値の場合、なりすまし判定を開始しない
				if (_settingInfo.is_livenessCheck)
				{
					bool livenessCheckStart = _livenessCheckDetection.Start();
					if (!livenessCheckStart)
					{
						eDoktor.Common.Trace.OutputErrorTrace("なりすまし検出処理を開始できませんでした。");
						//顔登録終了
						this.Close();
						MessageBox.Show(Messages.Message(MessageId.Message032));
					}
				}
				this._faceDetectionStateTimer.Enabled = true;
			}
			catch (Exception ex)
			{
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
			}
		}

		/// <summary>
		/// 新フレーム取得
		/// </summary>
		/// <param name="sender"></param>
		/// <param name="eventArgs"></param>
		void videoCaptureDevice_NewFrame(object sender, NewFrameEventArgs eventArgs)
        {
            Bitmap targetBitmap = (Bitmap)eventArgs.Frame.Clone();
            this._CurrentImage = targetBitmap;

            var old = this.pictureBox1.Image;
            Bitmap tmpBitmap = new Bitmap(targetBitmap, this._width, this._height);

            //なりすまし判定を使わない設定値の場合、フレームのエンキュー・デキューを行わない
            if (_settingInfo.is_livenessCheck)
            {
                _faceDetectionCounter++;
                //if (this._livenessCheckDetection != null && this._livenessCheckDetection._isFake && _messageDialog.Visible)
                if (this._livenessCheckDetection != null && this._livenessCheckDetection._isFake)
                {
                    while (true)
                    {
                        this._livenessCheckDetection._isFake = false;

                        if (this._livenessCheckDetection.ImageQueue.Count <= 0)
                        {
                            break;
                        }

                        if (this._livenessCheckDetection.ImageQueue.TryDequeue(out _dequeueBitmap) != true)
                        {
                            break;
                        }
                    }
                }
                else if (_faceDetectionCounter >= CST_FACE_DETECTION_EXECUTION_COUNTER_MAX)
                {
                    if (this._livenessCheckDetection.IsExecuting())
                    {
                        Bitmap[] queueBitmp = new Bitmap[2];
                        queueBitmp[0] = tmpBitmap;
                        queueBitmp[1] = targetBitmap;
                        this._livenessCheckDetection.ImageQueue.Enqueue(queueBitmp);
                    }
                    _faceDetectionCounter = 0;
                }
            }

            this.pictureBox1.Image = new Bitmap(tmpBitmap, this._width, this._height);
            tmpBitmap.Dispose();

            if (old != null)
            {
                old.Dispose();
            }
        }

        /// <summary>
        /// 撮影ボタン押下処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnPhoto_Click(object sender, EventArgs e)
        {
            try
            {
                //登録確認画面で生体判定処理を中断する
                _livenessCheckDetection.Stop();
                this._faceDetectionStateTimer.Enabled = false;

                if (this._livenessCheckDetection.IsExecutable())
                {
                    while (true)
                    {
                        if (this._livenessCheckDetection.ImageQueue.Count <= 0)
                        {
                            eDoktor.Common.Trace.OutputTrace("デキュー終了");
                            break;
                        }

                        if (this._livenessCheckDetection.ImageQueue.TryDequeue(out _dequeueBitmap) != true)
                        {
                            break;
                        }
                    }
                }

                //登録確認画面遷移
                using (FaceInsertEnrollment fileEnrollment = new FaceInsertEnrollment(this._accountId, this.label_staffId.Text, this.label_staffName.Text, this._CurrentImage, this._settingInfo, this._isFlipHorizon, this._faceDetectionStateTimer, this._livenessCheckDetection))
                {
                    this.Hide();
                    fileEnrollment.ShowDialog(this);
                    if (fileEnrollment.IsSuccess)
                    {
                        // 登録成功時
                        this.Close();
                    }
                    else
                    {
                        this.Show();
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                MessageBox.Show(Messages.Message(MessageId.Message001));
                this.Show();
            }
        }

        /// <summary>
        /// 戻るボタン押下処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnReturn_Click(object sender, EventArgs e)
        {
            this.Close();
            if (_messageDialog != null && _messageDialog.Visible)
            {
                Invoke((Action)(() =>
                {
                    _messageDialog.Visible = false;
                }
                ));
            }
        }


        /// <summary>
        /// マウスカーソルがボタン領域に入った時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseEnter(object sender, EventArgs e)
        {
            _btnImageCahnge.btn_MouseEnter(sender, e);
        }

        /// <summary>
        /// マウスカーソルがボタン領域から抜けた時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseLeave(object sender, EventArgs e)
        {
            _btnImageCahnge.btn_MouseLeave(sender, e);
        }

        /// <summary>
        /// ボタン押下時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            _btnImageCahnge.btn_MouseDown(sender, e);
        }

        /// <summary>
        /// ツールを閉じた際にオブジェクトの解放を行う
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void VideoCaptureForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (this._videoCaptureDevice != null && this._videoCaptureDevice.IsRunning)
            {
                this._videoCaptureDevice.SignalToStop();
                this._videoCaptureDevice.WaitForStop();
                this._videoCaptureDevice = null;
            }

            if (this._faceDetectionStateTimer != null)
            {
                this._faceDetectionStateTimer.Dispose();
            }
            if (this._livenessCheckDetection != null)
            {
                this._livenessCheckDetection.End();
            }
        }

        /// <summary>
        /// 顔検出状態を表示する。
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void DispFaceDetectionState(object sender, EventArgs e)
        {
            //なりすまし判定を使わない設定値の場合、なりすまし判定による切り替え・タイムアウト処理を行わない
            if (_livenessCheckDetection != null && _settingInfo.is_livenessCheck && _livenessCheckDetection.IsExecutable())
            {
                if (_livenessCheckDetection._isCurrentFake || _livenessCheckDetection._isFake || _livenessCheckDetection._isTimeout)
                {
                    this.BtnPhoto.Enabled = false;
                    this.BtnPhoto.FlatAppearance.BorderSize = 0;
                    //this.BtnPhoto.Visible = false;
                }
                else
                {
                    this.BtnPhoto.Enabled = true;
                    this.BtnPhoto.FlatAppearance.BorderSize = 0;
                    //this.BtnPhoto.Visible = true;
                }

                if (this._livenessCheckDetection._isTimeout && this._messageDialog != null)
                {
                    eDoktor.Common.Trace.OutputTrace("設定された時間操作がなかったため、タイムアウトしました。タイムアウト時間（ms） : {0}", this._settingInfo.liveness_check_timeout);
                    this._livenessCheckDetection.End();
                    Invoke((Action)(() =>
                    {
                        _messageDialog.error_message_text.Text = this._settingInfo.liveness_check_timeout_error_message;
                        _messageDialog.error_message_text.Refresh();
                    }
                    ));
                    //タイムアウトクローズ処理
                    this.Close();
                }
            }

            if (this._livenessCheckDetection == null || this._livenessCheckDetection.IsExecuting() != true)
            {
                return;
            }
        }
    }
}
