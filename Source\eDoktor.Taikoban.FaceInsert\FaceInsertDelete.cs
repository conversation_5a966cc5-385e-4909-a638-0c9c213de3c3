﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktorTaikoban.FaceClientCommon;

namespace eDoktor.Taikoban.FaceInsert
{
    /// <summary>
    /// 削除画面
    /// </summary>
    public partial class FaceInsertDelete : Form
    {
        /// <summary>
        /// 要求送信クラス
        /// </summary>
        RequestSendClass _requestSend = new RequestSendClass();

        /// <summary>
        /// ボタンアクションクラス
        /// </summary>
        BtnImageCahnge _btnImageCahnge = new BtnImageCahnge();

        /// <summary>
        /// コンストラクタ
        /// </summary>
        public FaceInsertDelete()
        {
            InitializeComponent();
        }

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="requestSend">要求送信クラス</param>
        public FaceInsertDelete(RequestSendClass requestSend)
            :this()
        {
            this._requestSend = requestSend;
        }

        /// <summary>
        /// フォームロード
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FaceInsertDelete_Load(object sender, EventArgs e)
        {

        }

        /// <summary>
        /// 削除ボタン押下処理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void button1_Click(object sender, EventArgs e)
        {
            if (txtStaffId.Text.Length < 1)
            {
                // 職員IDを入力してください。
                MessageBox.Show(Messages.Message(MessageId.Message022));
                return;
            }

            // 顔テンプレート登録確認要求を送信する
            var res = this._requestSend.RequestFaceTemplateRegistCheck(this.txtStaffId.Text, out FaceInterprocess.FaceTemplateRegConfirmResData resData);

            if (!res)
            {
                // サーバーからの応答がありません。
                Trace.OutputErrorTrace(Messages.Message(MessageId.Message004));
                return;
            }

            if (resData.ProcResult == EnumProcResult.Success)
            {
                // 処理結果が正常終了

                // 登録状態が「登録あり」もしくは、「登録あり(上限)」の場合
                if (resData.RegistState == EnumRegistState.Regist || resData.RegistState == EnumRegistState.RegistMax)
                {
                    // 削除確認画面に遷移する。
                    using (FaceInsertDeleteConfirm faceInsertDeleteConfirm = new FaceInsertDeleteConfirm(resData.AccountId, this.txtStaffId.Text, resData.UserName))
                    {
                        this.Hide();
                        faceInsertDeleteConfirm.ShowDialog(this);
                        this.Show();
                    }
                }
                else
                {
                    // 登録なし
                    string msg = Messages.Message(MessageId.Message008, this.txtStaffId.Text);

                    // 顔情報が未登録
                    MessageBox.Show(msg);
                }
            }
            else
            {
                // 処理結果が異常終了
                var errMsg = ClientCommonProc.ErrorIdToErrorMessage(resData.ErrorId, this.txtStaffId.Text);

                if (!string.IsNullOrEmpty(errMsg))
                {
                    MessageBox.Show(errMsg);
                }
            }
        }

        /// <summary>
        /// 戻るボタン押下処理
        /// </summary>
        private void button2_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// マウスカーソルがボタン領域に入った時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseEnter(object sender, EventArgs e)
        {
            _btnImageCahnge.btn_MouseEnter(sender, e);
        }

        /// <summary>
        /// マウスカーソルがボタン領域から抜けた時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseLeave(object sender, EventArgs e)
        {
            _btnImageCahnge.btn_MouseLeave(sender, e);
        }

        /// <summary>
        /// ボタン押下時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void btn_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            _btnImageCahnge.btn_MouseDown(sender, e);
        }

    }
}
