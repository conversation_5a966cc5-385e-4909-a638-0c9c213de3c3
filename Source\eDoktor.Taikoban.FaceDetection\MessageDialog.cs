﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

using eDoktor.Taikoban.FaceImageCntl;

namespace eDoktor.Taikoban.FaceDetection
{
    public partial class MessageDialog : Form
    {
        #region
        private enum ButtonKind
        {
            /// <summary>
            /// 閉じるボタン
            /// </summary>
            Close = 0,
        }

        #endregion

        #region Private Const
        private const string CST_RESOURCE_PROPERTY_NAME = "eDoktor.Taikoban.FaceDetection.Properties.Resources";
        /// <summary>
        /// メッセージエリアの1行当たりの文字数(半角で計算) フォントはMS UI Gothic 15ポイントでBoldを想定しています。
        /// </summary>
        private const int CST_ONE_LINE_MESSAGE_LENGTH = 38; 
        #endregion

        #region Property
        /// <summary>
        /// 表示メッセージ
        /// </summary>
        public string Message { get; set; }
        #endregion

        #region Private Fields
        /// <summary>
        /// 実行アセンブリー
        /// </summary>
        private Assembly _assembly;

        /// <summary>
        /// イメージデータのディクショナリー
        /// </summary>
        private Dictionary<ButtonKind, ImageData> _dicImageData;

        #endregion


        public MessageDialog()
        {
            InitializeComponent();
            this.StartPosition = FormStartPosition.CenterParent;
            this.TopMost = true;
            this._assembly = Assembly.GetExecutingAssembly();
            SetImageData(this._assembly, CST_RESOURCE_PROPERTY_NAME);
        }

        private void ok_button_Click(object sender, EventArgs e)
        {
            this.Visible = false;
        }

        /// <summary>
        /// マウスカーソルが閉じるボタン領域に入った時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picOK_MouseEnter(object sender, EventArgs e)
        {
            try
            {
                ChangeButtonImage(picOK, ButtonKind.Close, ImageData.ImageKind.MouseEnter);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }

        }

        /// <summary>
        /// マウスカーソルが閉じるボタン領域から抜けた時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picOK_MouseLeave(object sender, EventArgs e)
        {
            try
            {
                ChangeButtonImage(picOK, ButtonKind.Close, ImageData.ImageKind.MouseLeave);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 閉じるボタン押下時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picOK_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                ChangeButtonImage(picOK, ButtonKind.Close, ImageData.ImageKind.MouseDown);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }
        /// <summary>
        /// キャンセルボタン戻し時の処理
        /// </summary>
        /// <param name="sender">イベント発行元</param>
        /// <param name="e">イベント引数</param>
        private void picOK_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                ChangeButtonImage(picOK, ButtonKind.Close, ImageData.ImageKind.MouseUp);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        #region 表示用イメージデータの設定など
        /// <summary>
        /// pictureBoxボタン用のイメージデータの設定を行う。
        /// </summary>
        /// <param name="assembly">リソースの属するアセンブリー</param>
        /// <param name="resourceName">リソース名</param>
        private void SetImageData(Assembly assembly, string resourceName)
        {
            this._dicImageData = new Dictionary<ButtonKind, ImageData>();
            var imageData1 = new ImageData(this._assembly, CST_RESOURCE_PROPERTY_NAME);
            imageData1.AddImageData(ImageData.ImageKind.Normal, "liveness_ok_off");
            imageData1.AddImageData(ImageData.ImageKind.MouseEnter, "liveness_ok_hover");
            imageData1.AddImageData(ImageData.ImageKind.MouseLeave, "liveness_ok_off");
            imageData1.AddImageData(ImageData.ImageKind.MouseDown, "liveness_ok_on");
            imageData1.AddImageData(ImageData.ImageKind.MouseUp, "liveness_ok_off");
            this._dicImageData.Add(ButtonKind.Close, imageData1);
        }

        /// <summary>
        /// PictureBoxボタンのイメージを指定された種類に変更する。
        /// </summary>
        /// <param name="picBox">変更対象PictureBox</param>
        /// <param name="buttonKind">変更対象ボタンタイプ</param>
        /// <param name="imageKind">イメージの種類</param>
        private void ChangeButtonImage(PictureBox picBox, ButtonKind buttonKind, ImageData.ImageKind imageKind)
        {
            var oldImage = picBox.Image;
            picBox.Image = this._dicImageData[buttonKind].GetImage(imageKind);
            if (oldImage != null)
            {
                oldImage.Dispose();
            }
        }

        #endregion

    }
}
