﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace TestImageData
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            eDoktor.Taikoban.FaceAuthCommon.LoginUserInfo loginUserInfo = new eDoktor.Taikoban.FaceAuthCommon.LoginUserInfo();
            loginUserInfo.account_id = 12345;
            loginUserInfo.logon_id = "998877";
            loginUserInfo.name = "画面テストUser";
            eDoktor.Taikoban.FaceClient.FaceClientUserConfirm userConfirm = new eDoktor.Taikoban.FaceClient.FaceClientUserConfirm(loginUserInfo);
            DialogResult dr = userConfirm.ShowDialog(this);
            MessageBox.Show(dr.ToString());

        }

        private void button2_Click(object sender, EventArgs e)
        {
            eDoktor.Taikoban.FaceClient.FaceClientErr connectErr = new eDoktor.Taikoban.FaceClient.FaceClientErr();
            connectErr.Message = this.txtErrMsg.Text;
            DialogResult dr = connectErr.ShowDialog(this);
            MessageBox.Show(dr.ToString());

        }
    }
}
