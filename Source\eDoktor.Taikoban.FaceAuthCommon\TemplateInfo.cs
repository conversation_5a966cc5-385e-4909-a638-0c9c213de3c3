﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace eDoktor.Taikoban.FaceAuthCommon
{
    /// <summary>
    /// テンプレート情報クラス
    /// </summary>
    public class TemplateInfo
    {
        /// <summary>
        /// アカウントID
        /// </summary>
        public int account_id { get; set; }

        /// <summary>
        /// テンプレートID
        /// </summary>
        public int template_id { get; set; }

        /// <summary>
        /// テンプレートデータ
        /// </summary>
        public byte[] template_data { get; set; }

        /// <summary>
        /// テンプレート評価値
        /// </summary>
        public float template_quality { get; set; }

        /// <summary>
        /// 更新日時
        /// </summary>
        public DateTime modification_datetime { get; set; }
    }
}
