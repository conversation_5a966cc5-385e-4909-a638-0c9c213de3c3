﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthCommon;
using eDoktor.Taikoban.FaceInterprocess;

namespace eDoktorTaikoban.FaceClientCommon
{
    /// <summary>
    /// クライアント要求送信クラス
    /// </summary>
    public class RequestSendClass
    {
        #region Private Fields

        /// <summary>
        /// クライアント情報
        /// </summary>
        private Client _client;

        #endregion

        #region Constructors

        /// <summary>
        /// コンストラクタ
        /// </summary>
        public RequestSendClass()
            : this(new Client(NodeType.FaceServer))
        {
        }

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="client">クライアント情報</param>
        public RequestSendClass(Client client)
        {
            this._client = client;
        }

        #endregion

        #region server接続処理

        /// <summary>
        /// server接続処理
        /// </summary>
        /// <returns>処理結果(true:成功 false:失敗)</returns>
        public bool ServerConnect()
        {
            try
            {
                Trace.OutputDebugTrace("サーバーとの接続開始");
                this._client.Connect();
                Trace.OutputDebugTrace("サーバーとの接続完了");
                return true;
            }
            catch (System.Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                Trace.OutputErrorTrace("接続エラー");
                return false;
            }
        }

        #endregion

        #region 設定情報取得要求

        /// <summary>
        /// 設定情報取得要求
        /// </summary>
        /// <param name="resData">応答データ</param>
        /// <returns>処理結果(true:成功 false:失敗)</returns>
        public bool RequestGetSettingInfo(out FaceSettingGetResData resData)
        {
            int timeout = -1;

            // 送信電文を作成する
            var elementData = new ElementDataCreate();

            Trace.OutputDebugTrace(elementData.StrElements);

            Trace.OutputDebugTrace("設定取得要求を送信");

            // 設定取得要求を行う
            resData = this._client.SendRequest101AndReceiveResponse102(elementData.Elements, timeout);

            Trace.OutputDebugTrace("設定取得応答を受信");

            if (resData == null)
            {
                // サーバーからの応答がありません。
                Trace.OutputErrorTrace("サーバからの応答がありませんでした。");
                return false;
            }

            // 処理結果が異常終了
            if (resData.ProcResult == EnumProcResult.Fail)
            {
                Trace.OutputErrorTrace("設定値の取得に失敗しました。");
                MessageBox.Show("設定値の取得に失敗しました。");
                // System.Environment.Exit(0)を呼び出すと直ぐにプロセスが終了してしまうためにコメント
                //System.Environment.Exit(0);
                return false;
            }

            return true;
        }

        #endregion

        #region 顔テンプレート登録確認要求

        /// <summary>
        /// 顔テンプレート登録確認要求
        /// </summary>
        /// <param name="staffId">職員ID</param>
        /// <param name="resData">応答データ</param>
        /// <returns>処理結果(true:成功 false:失敗)</returns>
        public bool RequestFaceTemplateRegistCheck(string staffId, out FaceTemplateRegConfirmResData resData)
        {
            try
            {
                int timeout = -1;

                // 送信電文を作成する
                var elementData = new ElementDataCreate();
                elementData.CreateElem(nameof(FaceTemplateRegConfirmReqData.LogonId), staffId);

                Trace.OutputDebugTrace(elementData.StrElements);

                Trace.OutputDebugTrace("顔テンプレート登録確認要求を送信");

                // 顔テンプレート登録確認要求を送信する
                resData = _client.SendRequest103AndReceiveResponse104(elementData.Elements, timeout);

                Trace.OutputDebugTrace("顔テンプレート登録確認応答を受信");

                if (resData != null)
                {
                    return true;
                }

            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                resData = null;
            }

            Trace.OutputErrorTrace("サーバーからの応答がタイムアウトしました。");
            return false;
        }

        #endregion

        #region 顔テンプレート登録要求(サーバー認証モード)

        /// <summary>
        /// 顔テンプレート登録要求(サーバー認証モード)
        /// </summary>
        /// <param name="accountId">アカウントID</param>
        /// <param name="imageDataStr">画像データ</param>
        /// <param name="resData">応答データ</param>
        /// <returns>処理結果(true:成功 false:失敗)</returns>
        public bool RequestFaceTemplateRegistServerMode(int accountId, string imageDataStr, out FaceTemplateRegServerResData resData)
        {
            try
            {
                int timeout = -1;

                // 送信電文を作成する
                var elementData = new ElementDataCreate();
                elementData.CreateElem(nameof(FaceTemplateRegServerReqData.AccountId), accountId);
                elementData.CreateElem(nameof(FaceTemplateRegServerReqData.Image), imageDataStr);

                // Trace.OutputDebugTrace(elementData.StrElements);

                Trace.OutputDebugTrace("顔テンプレート登録要求(サーバー認証モード)を送信");

                resData = this._client.SendRequest109AndReceiveResponse110(elementData.Elements, timeout);

                Trace.OutputDebugTrace("顔テンプレート登録応答(サーバー認証モード)を受信");


                if (resData != null)
                {
                    return true;
                }

            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                resData = null;
            }

            Trace.OutputErrorTrace("サーバーからの応答がタイムアウトしました。");
            return false;
        }

        #endregion

        #region 本人確認・顔テンプレート登録確認要求

        /// <summary>
        /// 本人確認・顔テンプレート登録確認要求
        /// </summary>
        /// <param name="staffId">職員ID</param>
        /// <param name="password">パスワード</param>
        /// <param name="resData">応答データ</param>
        /// <returns>処理結果(true:成功 false:失敗)</returns>
        /// <returns></returns>
        public bool RequestIdentificationCheck(string staffId, string password, out IdentificationFaceTemplateRegConfirmResData resData)
        {
            try
            {
                int timeout = -1;
                // 送信電文を作成する
                var elementData = new ElementDataCreate();
                elementData.CreateElem(nameof(IdentificationFaceTemplateRegConfirmReqData.LogonId), staffId);
                elementData.CreateElem(nameof(IdentificationFaceTemplateRegConfirmReqData.Password), password);

                Trace.OutputDebugTrace(elementData.StrElements.Replace(password, "*****"));

                Trace.OutputDebugTrace("本人確認・顔テンプレート登録確認要求を送信");

                // 本人確認・顔テンプレート登録確認要求を送信する
                resData = _client.SendRequest117AndReceiveResponse118(elementData.Elements, timeout);

                Trace.OutputDebugTrace("本人確認・顔テンプレート登録確認応答を受信");

                if (resData != null)
                {
                    return true;
                }
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                resData = null;
            }

            return false;
        }

        #endregion

        #region 顔テンプレート削除要求

        /// <summary>
        /// 顔テンプレート削除要求
        /// </summary>
        /// <param name="accountId">アカウントID</param>
        /// <param name="resData">応答データ</param>
        /// <returns>処理結果(true:成功 false:失敗)</returns>
        /// <returns></returns>
        public bool RequestFaceTemplateDelete(int accountId, out FaceTemplateDelResData resData)
        {
            try
            {
                int timeout = -1;

                // 電文には職員IDを付与し、職員IDに紐づくレコードを全て削除する。
                // 送信電文を作成する
                var elementData = new ElementDataCreate();
                elementData.CreateElem(nameof(FaceTemplateDelReqData.AccountId), accountId);

                Trace.OutputDebugTrace(elementData.StrElements);

                Trace.OutputDebugTrace("顔テンプレート削除要求を送信");

                // 顔テンプレート削除要求を送信する
                resData = _client.SendRequest113AndReceiveResponse114(elementData.Elements, timeout);

                Trace.OutputDebugTrace("顔テンプレート削除応答を受信");

                if (resData != null)
                {
                    return true;
                }

            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
                resData = null;
            }

            return false;
        }

        #endregion

        #region 顔認証要求

        /// <summary>
        /// 顔認証要求
        /// </summary>
        /// <param name="accountId">アカウントID</param>
        /// <param name="bitmap">画像データ</param>
        /// <returns>処理結果(true:成功 false:失敗)</returns>
        public bool RequestFaceAuth(int accountId, Bitmap bitmap)
        {
            // 顔認証要求を送信する
            int timeout = -1;
            byte[] imageData;
            string imageDataStr;
            bool ret = false;

            MemoryStream memoryStream = new MemoryStream();
            bitmap.Save(memoryStream, System.Drawing.Imaging.ImageFormat.Png);
            imageData = memoryStream.GetBuffer();

            // base64で暗号化する。
            imageDataStr = Convert.ToBase64String(imageData);

            // 送信電文を作成する
            var elementData = new ElementDataCreate();
            elementData.CreateElem(nameof(FaceAuthtReqData.AccountId), accountId);
            elementData.CreateElem(nameof(FaceAuthtReqData.Image), imageDataStr);

            // Trace.OutputDebugTrace(elementData.StrElements);

            Trace.OutputDebugTrace("顔認証要求を送信");

            FaceAuthtResData resData = _client.SendRequest107AndReceiveResponse108(elementData.Elements, timeout);

            Trace.OutputDebugTrace("顔認証応答を受信");

            if (resData == null)
            {
                // サーバーからの応答がありませんでした。
                Trace.OutputErrorTrace(Messages.Message(MessageId.Message004));
            }
            else if (resData.ProcResult == EnumProcResult.Success && resData.AuthResult == EnumAuthResult.AuthOK)
            {
                // 処理結果が正常終了かつ、認証結果が認証成功時
                Trace.OutputDebugTrace("認証に成功しました。");
                ret = true;
            }

            return ret;
        }

        #endregion

        #region ユーザー検索顔認証要求

        /// <summary>
        /// ユーザー検索顔認証要求
        /// </summary>
        /// <param name="bitmap">画像データ</param>
        /// <param name="loginUserInfo">ユーザ情報</param>
        /// <returns>処理結果(true:成功 false:失敗)</returns>
        public bool RequestUserSearchFaceAuth(Bitmap bitmap, out LoginUserInfo loginUserInfo)
        {
            // 顔認証要求を送信する
            int timeout = -1;
            string imageDataStr = string.Empty;
            bool ret = false;
            loginUserInfo = new LoginUserInfo();

            using (var memoryStream = new MemoryStream())
            {
                bitmap.Save(memoryStream, System.Drawing.Imaging.ImageFormat.Png);
                var imageData = memoryStream.GetBuffer();
                // base64で暗号化する。
                imageDataStr = Convert.ToBase64String(imageData);
            }

            var elementData = new ElementDataCreate();
            elementData.CreateElem(nameof(UserSearchFaceAuthReqData.Image), imageDataStr);

            // Trace.OutputDebugTrace(elementData.StrElements);

            Trace.OutputDebugTrace("ユーザー検索顔認証要求を送信");

            UserSearchFaceAuthResData resData = _client.SendRequest119AndReceiveResponse120(elementData.Elements, timeout);

            Trace.OutputDebugTrace("ユーザー検索顔認証応答を受信");

            if (resData == null)
            {
                // サーバーからの応答がありませんでした。
                Trace.OutputErrorTrace(Messages.Message(MessageId.Message004));
            }
            else if (resData.ProcResult == EnumProcResult.Success && resData.AuthResult == EnumAuthResult.AuthOK)
            {
                // 処理結果が正常終了かつ、認証結果が認証成功時
                Trace.OutputDebugTrace("認証に成功しました。");
                loginUserInfo = resData.UserInfo;
                ret = true;
            }

            return ret;
        }

        #endregion

        #region なりすまし画像登録要求

        /// <summary>
        /// なりすまし画像登録要求
        /// </summary>
        /// <param name="accountId">アカウントID</param>
        /// <param name="bitmap">画像データ</param>
        /// <returns>処理結果(true:成功 false:失敗)</returns>
        public bool RequestLivenessImageRegist(int accountId, Bitmap bitmap)
        {
            // なりすまし画像登録要求を送信する
            int timeout = -1;
            byte[] imageData;
            string imageDataStr;
            bool ret = false;

            MemoryStream memoryStream = new MemoryStream();
            bitmap.Save(memoryStream, System.Drawing.Imaging.ImageFormat.Png);
            imageData = memoryStream.GetBuffer();

            // base64で暗号化する。
            imageDataStr = Convert.ToBase64String(imageData);

            // 送信電文を作成する
            var elementData = new ElementDataCreate();
            elementData.CreateElem(nameof(LivenessReqData.AccountId), accountId);
            elementData.CreateElem(nameof(LivenessReqData.Image), imageDataStr);

            // Trace.OutputDebugTrace(elementData.StrElements);

            Trace.OutputDebugTrace("なりすまし画像登録要求を送信");

            LivenessResData resData = _client.SendRequest123AndReceiveResponse124(elementData.Elements, timeout);

            Trace.OutputDebugTrace("なりすまし画像登録応答を受信");

            if (resData == null)
            {
                // サーバーからの応答がありませんでした。
                Trace.OutputErrorTrace(Messages.Message(MessageId.Message004));
            }
            else if (resData.ProcResult == EnumProcResult.Success && resData.AuthResult == EnumAuthResult.AuthOK)
            {
                // 処理結果が正常終了かつ、認証結果が認証成功時
                Trace.OutputDebugTrace("登録に成功しました。");
                ret = true;
            }

            return ret;
        }

        #endregion

    }
}
