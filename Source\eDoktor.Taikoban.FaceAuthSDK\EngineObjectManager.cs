﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Timers;
using eDoktor.Common;
using eDoktor.Taikoban.FaceAuthSettingsInfo;
using GFRL.Ex;
using GFRL.FaceRecognition;
using GFRL.FaceSearch;
using GFRL.FaceTemplate;

namespace eDoktor.Taikoban.FaceAuthSDK
{
    #region 顔照合クラス

    /// <summary>
    /// 顔照合クラス
    /// </summary>
    public class RetrieverEngineObjectManager : IDisposable
    {
        //  シングルトン
        public static readonly RetrieverEngineObjectManager Instance = new RetrieverEngineObjectManager();

        private Dictionary<FaceRetrieve, Tuple<int, DateTime>> instance_table = new Dictionary<FaceRetrieve, Tuple<int, DateTime>>();
        private Dictionary<FaceRetrieve, Tuple<int, DateTime>> temporary_table = new Dictionary<FaceRetrieve, Tuple<int, DateTime>>();
        private int temporary = 0;

        private int max_capacity = 10;      //  ここはConfigなりで
        private int max_temporary = 10;     //  ここはConfigなりで
        private int lifespan_second = 10;   //  ここはConfigなりで
        private int interval_second = 15;   //  ここはConfigなりで

        private CDisposableTimer watch_timer = new CDisposableTimer();

        public void Dispose()
        {
            lock (this)
            {
                if (this.instance_table != null)
                {
                    // 使用中のものがないかどうか
                    if (this.instance_table.Values.Count(x => x != null) > 0)
                    {
                        throw new InvalidOperationException();
                    }

                    foreach (var instance in this.instance_table.Keys)
                    {
                        // instance.Dispose();
                    }

                    this.instance_table = null;
                }

                if (this.temporary_table != null)
                {
                    // 使用中のものがないかどうか
                    if (this.temporary_table.Values.Count(x => x != null) > 0)
                    {
                        throw new InvalidOperationException();
                    }

                    foreach (var instance in this.temporary_table.Keys)
                    {
                        // instance.Dispose();
                    }

                    this.temporary_table = null;
                }

                //  監視タイマー終了
                this.watch_timer.Dispose();
                this.watch_timer.Stop();
            }
        }

        // ▼ MOD SDK初期化対応 2021/07/13 Y.Tani Start
        /// <summary>
        /// 顔照合 初期化
        /// </summary>
        /// <param name="capacity"></param>
        /// <param name="temporary"></param>
        /// <param name="lifespan"></param>
        /// <param name="settingInfo"></param>
        public void Initialize(int capacity, int temporary, int lifespan, SettingsInfo settingInfo)
        // ▲ MOD SDK初期化対応 2021/07/13 Y.Tani End
        {
            if (0 >= capacity || capacity > this.max_capacity)
            {
                throw new ArgumentOutOfRangeException();
            }

            if (0 >= temporary || temporary > this.max_temporary)
            {
                throw new ArgumentOutOfRangeException();
            }

            this.Dispose();

            lock (this)
            {
                // this.capacity = capacity;
                this.temporary = temporary;

                //  Keyはインスタンス、ValueはスレッドIDと取得時刻で未使用はnull
                this.instance_table = new Dictionary<FaceRetrieve, Tuple<int, DateTime>>();

                // ▼ ADD SDK初期化対応 2021/07/13 Y.Tani Start
                // 顔照合用の初期値を設定
                var retrieveFaceSetting = new FaceRetrieveSetting()
                {
                    FaceFeatureSetting = new FaceFeatureSetting()
                    {
                        IsEstimateGenderAge = settingInfo.is_estimate_gender_age,               // trueにすると、照合する時に性別年齢推定結果が出力されます
                        TemplateQualityThreshold = settingInfo.template_quality_threshold_auth  // テンプレート評価値
                    },
                    Threshold = settingInfo.face_auth_threshold,                                // 照合閾値
                    RecogType = settingInfo.face_recog_data_type,                               // 照合タイプを指定
                };

                Trace.OutputTrace("顔照合用の初期値を設定");
                Trace.OutputDebugTrace("Threshold                : {0}", settingInfo.face_auth_threshold);
                Trace.OutputDebugTrace("IsEstimateGenderAge      : {0}", settingInfo.is_estimate_gender_age);
                Trace.OutputDebugTrace("TemplateQualityThreshold : {0}", settingInfo.template_quality_threshold_auth);
                Trace.OutputDebugTrace("RecogType                : {0}", settingInfo.face_recog_data_type);
                // ▲ ADD SDK初期化対応 2021/07/13 Y.Tani End

                for (int i = 0; i < capacity; i++)
                {
                    var instance = new FaceRetrieve();

                    // ▼ MOD SDK初期化対応 2021/07/13 Y.Tani Start
                    instance.Initialize(retrieveFaceSetting);
                    // ▲ MOD SDK初期化対応 2021/07/13 Y.Tani End
                    this.instance_table[instance] = null;

                    var hash = instance.GetHashCode();
                    Trace.OutputTrace($"    ***** 顔照合インスタンス初期化 [{i}] , Hash: {hash}");
                }

                Trace.OutputTrace($"    ***** 顔照合インスタンス初期化 Total: {this.instance_table.Count}");

                this.temporary_table = new Dictionary<FaceRetrieve, Tuple<int, DateTime>>();

                this.lifespan_second = lifespan;

                this.watch_timer = new CDisposableTimer();
                this.watch_timer.Interval = this.interval_second * 1000;
                this.watch_timer.Elapsed += Watch_timer_Elapsed;

                this.watch_timer.Start();
            }
        }

        // ▼ MOD SDK初期化対応 2021/07/13 Y.Tani Start
        /// <summary>
        /// 顔照合 インスタンス取得
        /// </summary>
        /// <param name="settingInfo"></param>
        /// <returns></returns>
        public FaceRetrieve GetInstance(SettingsInfo settingInfo)
        // ▲ MOD SDK初期化対応 2021/07/13 Y.Tani End
        {
            //  空きがなかったらnull返却、エラーは例外スロー
            FaceRetrieve returnValue = null;

            if (this.instance_table == null || this.instance_table.Count == 0)
            {
                throw new NotInitializedException(typeof(RetrieverEngineObjectManager).Name);
            }

            lock (this)
            {
                //  まずinstance_tableから調べる
                while (true)
                {
                    //  特徴量が合致するもの
                    var instances = this.instance_table.ToList();

                    if (instances.Count() == 0)
                    {
                        //  合致するものがないということはInitializeか引数がおかしいので例外
                        throw new ArgumentException();
                    }

                    //  そのうちで未使用のもの
                    var standby = instances.Where(x => x.Value == null).ToList();

                    if (standby.Count == 0)
                    {
                        Trace.OutputErrorTrace("    ***** 顔照合オブジェクト フル");
                        break;
                    }

                    returnValue = standby.First().Key;

                    var hash = returnValue.GetHashCode();
                    Trace.OutputTrace($"    ***** 顔照合オブジェクト Stanby: {standby.Count} , Hash: {hash}");

                    //  使っている印を付けとく
                    this.instance_table[returnValue] = new Tuple<int, DateTime>(System.Threading.Thread.CurrentThread.ManagedThreadId, DateTime.Now);

                    break;
                }

                //  空いてなかったら拡張テーブルを調べる
                if (returnValue == null)
                {
                    while (true)
                    {
                        if (this.temporary_table.Count >= this.temporary)
                        {
                            //  空きがない
                            Trace.OutputErrorTrace("    ***** 顔照合オブジェクト 拡張オーバー");
                            break;
                        }

                        // ▼ ADD SDK初期化対応 2021/07/13 Y.Tani Start
                        // 顔照合用の初期値を設定
                        var retrieveFaceSetting = new GFRL.FaceRecognition.FaceRetrieveSetting()
                        {
                            Threshold = settingInfo.face_auth_threshold,        //  照合閾値
                            FaceFeatureSetting = new GFRL.FaceRecognition.FaceFeatureSetting()
                            {
                                IsEstimateGenderAge = settingInfo.is_estimate_gender_age,            //  trueにすると、照合する時に性別年齢推定結果が出力されます
                                TemplateQualityThreshold = settingInfo.template_quality_threshold_auth
                            },
                            RecogType = settingInfo.face_recog_data_type, // 照合タイプを指定
                        };
                        // ▲ ADD SDK初期化対応 2021/07/13 Y.Tani End

                        var instance = new FaceRetrieve();

                        instance.Initialize(
                            // ▼ MOD SDK初期化対応 2021/07/13 Y.Tani Start
                            //new FaceRetrieveSetting()
                            retrieveFaceSetting // 上記の初期値で初期化する
                                                // ▲ MOD SDK初期化対応 2021/07/13 Y.Tani End
                        );
                        this.temporary_table[instance] = new Tuple<int, DateTime>(System.Threading.Thread.CurrentThread.ManagedThreadId, DateTime.Now);

                        returnValue = instance;

                        Trace.OutputTrace("    ***** 顔照合オブジェクト 拡張生成");

                        break;
                    }
                }
            }

            return returnValue;
        }

        /// <summary>
        /// 顔照合 インスタンス解放
        /// </summary>
        /// <param name="instance"></param>
        public void ReleaseInstance(FaceRetrieve instance)
        {
            if (instance == null)
            {
                throw new ArgumentNullException();
            }

            if (this.instance_table == null || this.instance_table.Count == 0)
            {
                throw new NotInitializedException(typeof(RetrieverEngineObjectManager).Name);
            }

            lock (this)
            {
                while (true)
                {
                    if (this.instance_table.ContainsKey(instance))
                    {
                        // Valueを未使用（null）に
                        this.instance_table[instance] = null;
                        Trace.OutputTrace($"    ***** 顔照合インスタンス使用済: {instance.GetHashCode()}");
                        break;
                    }

                    if (this.temporary_table.ContainsKey(instance))
                    {
                        //  テンポラリの場合はメモリ解放
                        this.temporary_table.Remove(instance);
                        //instance.Dispose();

                        break;
                    }

                    break;
                }
            }
        }

        /// <summary>
        /// 顔照合 インスタンス制限時間チェック
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Watch_timer_Elapsed(object sender, ElapsedEventArgs e)
        {
            lock (this)
            {
                try
                {
                    //  一旦止めて
                    this.watch_timer.Stop();

                    //  制限時間を超えているものはリリースする
                    if (this.instance_table == null) return;
                    if (this.temporary_table == null) return;

                    var now = DateTime.Now;

                    var timeout = this.instance_table.Where(x => x.Value != null && (now - x.Value.Item2).TotalSeconds > this.lifespan_second).ToList();

                    if (timeout.Count > 0)
                    {
                        foreach (var taget in timeout)
                        {
                            Trace.OutputTrace($"    ***** 顔照合インスタンスチェック {taget.Key.GetHashCode()}");
                            this.instance_table[taget.Key] = null;
                        }
                    }

                    timeout = this.temporary_table.Where(x => x.Value != null && (now - x.Value.Item2).TotalSeconds > this.lifespan_second).ToList();

                    if (timeout.Count > 0)
                    {
                        foreach (var taget in timeout)
                        {
                            Trace.OutputTrace($"    ***** 顔照合インスタンスチェック {taget.Key.GetHashCode()} (temporary)");
                            this.temporary_table.Remove(taget.Key);
                        }
                    }
                }
                finally
                {
                    //  再スタート
                    this.watch_timer.Start();
                }
            }
        }
    }

    #endregion

    #region 顔サーチクラス

    /// <summary>
    /// 顔サーチクラス
    /// </summary>
    public class SearcherEngineObjectManager : IDisposable
    {
        // シングルトン
        public static readonly SearcherEngineObjectManager Instance = new SearcherEngineObjectManager();

        private Dictionary<FaceSearch, Tuple<int, DateTime>> instance_table = new Dictionary<FaceSearch, Tuple<int, DateTime>>();
        private Dictionary<FaceSearch, Tuple<int, DateTime>> temporary_table = new Dictionary<FaceSearch, Tuple<int, DateTime>>();
        //private int capacity = 0;
        private int temporary = 0;

        private int max_capacity = 30;      //  ここはConfigなりで
        private int max_temporary = 10;     //  ここはConfigなりで
        private int lifespan_second = 10;   //  ここはConfigなりで
        private int interval_second = 15;   //  ここはConfigなりで

        private CDisposableTimer watch_timer = new CDisposableTimer();

        public void Dispose()
        {
            lock (this)
            {
                if (this.instance_table != null)
                {
                    // 使用中のものがないかどうか
                    if (this.instance_table.Values.Count(x => x != null) > 0)
                    {
                        throw new InvalidOperationException();
                    }

                    foreach (var instance in this.instance_table.Keys)
                    {
                        // instance.Dispose();
                    }

                    this.instance_table = null;
                }

                if (this.temporary_table != null)
                {
                    // 使用中のものがないかどうか
                    if (this.temporary_table.Values.Count(x => x != null) > 0)
                    {
                        throw new InvalidOperationException();
                    }

                    foreach (var instance in this.temporary_table.Keys)
                    {
                        // instance.Dispose();
                    }

                    this.temporary_table = null;
                }

                //  監視タイマー終了
                this.watch_timer.Dispose();
                this.watch_timer.Stop();
            }
        }

        // ▼ MOD SDK初期化対応 2021/07/13 Y.Tani Start
        /// <summary>
        /// 顔サーチ 初期化
        /// </summary>
        /// <param name="capacity"></param>
        /// <param name="Temporary"></param>
        /// <param name="Lifespan"></param>
        /// <param name="settingInfo"></param>
        public void Initialize(int capacity, int Temporary, int Lifespan, FaceAuthSettingsInfo.SettingsInfo settingInfo)
        // ▲ MOD SDK初期化対応 2021/07/13 Y.Tani End
        {
            if (0 >= capacity || capacity > this.max_capacity)
            {
                throw new ArgumentOutOfRangeException();
            }

            if (0 >= Temporary || Temporary > this.max_temporary)
            {
                throw new ArgumentOutOfRangeException();
            }

            this.Dispose();

            lock (this)
            {
                //this.capacity = capacity;
                this.temporary = Temporary;

                //  Keyはインスタンス、ValueはスレッドIDと取得時刻で未使用はnull
                this.instance_table = new Dictionary<FaceSearch, Tuple<int, DateTime>>();

                // ▼ ADD SDK初期化対応 2021/07/13 Y.Tani Start
                // 顔サーチ用の初期値を設定
                var faceSearchSettings = new GFRL.FaceSearch.FaceSearchSetting();
                faceSearchSettings.ScaleMax = settingInfo.face_search_scale_max;                        // 検出する目間画素最大値
                faceSearchSettings.ScaleMin = settingInfo.face_search_scale_min;                        // 検出する目間画素最小値
                faceSearchSettings.SearchFaceNumMax = settingInfo.face_search_count_max;                // 検出する顔の数
                faceSearchSettings.RecogQualityThreshold = settingInfo.face_recog_quality_threshold;    // 顔照合適合度しきい値
                faceSearchSettings.ImageSetting.Height = settingInfo.input_image_height;                // 入力画像のサイズ(高さ)
                faceSearchSettings.ImageSetting.Width = settingInfo.input_image_width;                  // 入力画像のサイズ(幅)
                // ▲ ADD SDK初期化対応 2021/07/13 Y.Tani End

                for (int i = 0; i < capacity; i++)
                {
                    var instance = new FaceSearch();

                    // ▼ MOD SDK初期化対応 2021/07/13 Y.Tani Start
                    instance.Initialize(faceSearchSettings);
                    // ▲ MOD SDK初期化対応 2021/07/13 Y.Tani End

                    this.instance_table[instance] = null;

                    var hash = instance.GetHashCode();
                    Trace.OutputTrace($"    ***** 顔サーチインスタンス初期化 [{i}] , Hash: {hash}");
                }

                Trace.OutputTrace($"    ***** 顔サーチインスタンス初期化 Total: {this.instance_table.Count}");

                this.temporary_table = new Dictionary<FaceSearch, Tuple<int, DateTime>>();

                this.lifespan_second = Lifespan;

                this.watch_timer = new CDisposableTimer();
                this.watch_timer.Interval = this.interval_second * 1000;
                this.watch_timer.Elapsed += Watch_timer_Elapsed;

                this.watch_timer.Start();
            }
        }

        // ▼ MOD SDK初期化対応 2021/07/13 Y.Tani Start
        /// <summary>
        /// 顔サーチ インスタンス取得
        /// </summary>
        /// <param name="settingInfo"></param>
        /// <returns></returns>
        public FaceSearch GetInstance(SettingsInfo settingInfo)
        // ▲ MOD SDK初期化対応 2021/07/13 Y.Tani End
        {
            //  空きがなかったらnull返却、エラーは例外スロー
            FaceSearch returnValue = null;

            if (this.instance_table == null || this.instance_table.Count == 0)
            {
                throw new NotInitializedException(typeof(SearcherEngineObjectManager).Name);
            }

            lock (this)
            {
                // まずinstance_tableから調べる
                while (true)
                {
                    //  特徴量が合致するもの
                    var instances = this.instance_table;

                    if (instances.Count() == 0)
                    {
                        //  合致するものがないということはInitializeか引数がおかしいので例外
                        throw new ArgumentException();
                    }

                    //  そのうちで未使用のもの
                    var standby = instances.Where(x => x.Value == null).ToList();

                    if (standby.Count == 0)
                    {
                        break;
                    }

                    returnValue = standby.First().Key;

                    var hash = returnValue.GetHashCode();
                    Trace.OutputTrace($"    ***** 顔サーチオブジェクト Stanby: {standby.Count} , Hash: {hash}");

                    //  使っている印を付けとく
                    this.instance_table[returnValue] = new Tuple<int, DateTime>(System.Threading.Thread.CurrentThread.ManagedThreadId, DateTime.Now);

                    break;
                }

                //  空いてなかったら拡張テーブルを調べる
                if (returnValue == null)
                {
                    while (true)
                    {
                        if (this.temporary_table.Count >= this.temporary)
                        {
                            //  空きがない
                            Trace.OutputErrorTrace("    ***** 顔サーチオブジェクト 拡張オーバー");
                            break;
                        }

                        // ▼ ADD SDK初期化対応 2021/07/13 Y.Tani Start
                        // 顔サーチ用の初期値を設定
                        var faceSearchSettings = new FaceSearchSetting();
                        {
                            //  顔サーチ設定

                            faceSearchSettings.ScaleMax = settingInfo.face_search_scale_max;                        // 検出する目間画素最大値
                            faceSearchSettings.ScaleMin = settingInfo.face_search_scale_min;                        // 検出する目間画素最小値
                            faceSearchSettings.SearchFaceNumMax = settingInfo.face_search_count_max;                // 検出する顔の数
                            faceSearchSettings.RecogQualityThreshold = settingInfo.face_recog_quality_threshold;    // 顔照合適合度しきい値
                            faceSearchSettings.ImageSetting.Height = settingInfo.input_image_height;                // 入力画像のサイズ(高さ)
                            faceSearchSettings.ImageSetting.Width = settingInfo.input_image_width;                  // 入力画像のサイズ(幅)
                        }
                        // ▲ ADD SDK初期化対応 2021/07/13 Y.Tani End

                        var instance = new FaceSearch();
                        // ▼ MOD SDK初期化対応 2021/07/13 Y.Tani Start
                        instance.Initialize(faceSearchSettings);
                        // ▲ MOD SDK初期化対応 2021/07/13 Y.Tani End


                        this.temporary_table[instance] = new Tuple<int, DateTime>(System.Threading.Thread.CurrentThread.ManagedThreadId, DateTime.Now);

                        returnValue = instance;

                        Trace.OutputTrace("    ***** 顔サーチオブジェクト 拡張生成");

                        break;
                    }
                }
            }

            return returnValue;
        }

        /// <summary>
        /// 顔サーチ インスタンス解放
        /// </summary>
        /// <param name="instance"></param>
        public void ReleaseInstance(FaceSearch instance)
        {
            if (instance == null)
            {
                throw new ArgumentNullException();
            }

            if (this.instance_table == null || this.instance_table.Count == 0)
            {
                throw new NotInitializedException(typeof(SearcherEngineObjectManager).Name);
            }

            lock (this)
            {
                while (true)
                {
                    if (this.instance_table.ContainsKey(instance))
                    {
                        // Valueを未使用（null）に
                        this.instance_table[instance] = null;
                        Trace.OutputTrace($"    ***** 顔サーチインスタンス使用済: {instance.GetHashCode()}");
                        break;
                    }

                    if (this.temporary_table.ContainsKey(instance))
                    {
                        //  テンポラリの場合はメモリ解放
                        this.temporary_table.Remove(instance);
                        //instance.Dispose();

                        break;
                    }

                    break;
                }
            }
        }

        /// <summary>
        /// 顔サーチ インスタンス制限時間チェック
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Watch_timer_Elapsed(object sender, ElapsedEventArgs e)
        {
            lock (this)
            {
                try
                {
                    //  一旦止めて
                    this.watch_timer.Stop();

                    //  制限時間を超えているものはリリースする
                    if (this.instance_table == null) return;
                    if (this.temporary_table == null) return;

                    var now = DateTime.Now;

                    var timeout = this.instance_table.Where(x => x.Value != null && (now - x.Value.Item2).TotalSeconds > this.lifespan_second).ToList();

                    if (timeout.Count > 0)
                    {
                        timeout.Select(x => this.instance_table[x.Key] = null);

                        foreach (var taget in timeout)
                        {
                            Trace.OutputTrace($"    ***** 顔サーチインスタンスチェック {taget.Key.GetHashCode()}");
                            this.instance_table[taget.Key] = null;
                        }
                    }

                    timeout = this.temporary_table.Where(x => x.Value != null && (now - x.Value.Item2).TotalSeconds > this.lifespan_second).ToList();

                    if (timeout.Count > 0)
                    {
                        foreach (var taget in timeout)
                        {
                            Trace.OutputTrace($"    ***** 顔サーチインスタンスチェック {taget.Key.GetHashCode()} (temporary)");
                            this.temporary_table.Remove(taget.Key);
                        }
                    }
                }
                finally
                {
                    //  再スタート
                    this.watch_timer.Start();
                }
            }
        }

    }

    #endregion

    #region テンプレート作成クラス

    /// <summary>
    /// テンプレート作成クラス
    /// </summary>
    public class CreatorEngineObjectManager : IDisposable
    {
        //  シングルトン
        public static readonly CreatorEngineObjectManager Instance = new CreatorEngineObjectManager();

        private Dictionary<FaceTemplateCreate, Tuple<int, DateTime>> instance_table = new Dictionary<FaceTemplateCreate, Tuple<int, DateTime>>();
        private Dictionary<FaceTemplateCreate, Tuple<int, DateTime>> temporary_table = new Dictionary<FaceTemplateCreate, Tuple<int, DateTime>>();
        //private int capacity = 0;
        private int temporary = 0;

        private int max_capacity = 10;      //  ここはConfigなりで
        private int max_temporary = 10;     //  ここはConfigなりで
        private int lifespan_second = 10;   //  ここはConfigなりで
        private int interval_second = 15;   //  ここはConfigなりで

        private CDisposableTimer watch_timer = new CDisposableTimer();

        public void Dispose()
        {
            lock (this)
            {
                if (this.instance_table != null)
                {
                    // 使用中のものがないかどうか
                    if (this.instance_table.Values.Count(x => x != null) > 0)
                    {
                        throw new InvalidOperationException();
                    }

                    foreach (var instance in this.instance_table.Keys)
                    {
                        // instance.Dispose();
                    }

                    this.instance_table = null;
                }

                if (this.temporary_table != null)
                {
                    // 使用中のものがないかどうか
                    if (this.temporary_table.Values.Count(x => x != null) > 0)
                    {
                        throw new InvalidOperationException();
                    }

                    foreach (var instance in this.temporary_table.Keys)
                    {
                        // instance.Dispose();
                    }

                    this.temporary_table = null;
                }

                //  監視タイマー終了
                this.watch_timer.Dispose();
                this.watch_timer.Stop();
            }
        }

        // ▼ MOD SDK初期化対応 2021/07/13 Y.Tani Start
        /// <summary>
        /// テンプレート作成　初期化
        /// </summary>
        /// <param name="capacity"></param>
        /// <param name="Temporary"></param>
        /// <param name="Lifespan"></param>
        /// <param name="settingInfo"></param>
        public void Initialize(int capacity, int Temporary, int Lifespan, FaceAuthSettingsInfo.SettingsInfo settingInfo)
        // ▲ MOD SDK初期化対応 2021/07/13 Y.Tani End
        {
            if (0 >= capacity || capacity > this.max_capacity)
            {
                throw new ArgumentOutOfRangeException();
            }
            //if (0 >= temporary || temporary > this.max_temporary)
            if (0 >= Temporary || Temporary > this.max_temporary)
            {
                throw new ArgumentOutOfRangeException();
            }

            this.Dispose();

            lock (this)
            {
                //this.capacity = capacity;
                this.temporary = Temporary;

                //  Keyはインスタンス、ValueはスレッドIDと取得時刻で未使用はnull
                this.instance_table = new Dictionary<FaceTemplateCreate, Tuple<int, DateTime>>();

                // ▼ ADD SDK初期化対応 2021/07/13 Y.Tani Start
                // 特徴抽出用の初期値を設定
                var faceFeatureSetting = new GFRL.FaceRecognition.FaceFeatureSetting();
                {
                    faceFeatureSetting.IsEstimateGenderAge = settingInfo.is_estimate_gender_age;                // 性別年齢推定を行うか（true：する、false：しない）
                    faceFeatureSetting.TemplateQualityThreshold = settingInfo.template_quality_threshold_auth;  // テンプレート評価値のしきい値
                }

                Trace.OutputDebugTrace("IsEstimateGenderAge      : {0}", faceFeatureSetting.IsEstimateGenderAge);
                Trace.OutputDebugTrace("TemplateQualityThreshold : {0}", faceFeatureSetting.TemplateQualityThreshold);
                Trace.OutputDebugTrace("RecogDataType            : {0}", settingInfo.face_recog_data_type);
                // ▲ ADD SDK初期化対応 2021/07/13 Y.Tani End

                for (int i = 0; i < capacity; i++)
                {
                    var instance = new FaceTemplateCreate();

                    instance.Initialize(
                        // ▼ MOD SDK初期化対応 2021/07/13 Y.Tani Start
                        //new FaceFeatureSetting()
                        faceFeatureSetting,
                        settingInfo.face_recog_data_type                    // 照合タイプを指定
                                                                            // ▲ MOD SDK初期化対応 2021/07/13 Y.Tani End
                    );
                    this.instance_table[instance] = null;

                    var hash = instance.GetHashCode();
                    Trace.OutputTrace($"    ***** 特徴抽出インスタンス初期化 [{i}] ,  Hash: {hash}");

                }

                Trace.OutputTrace($"    ***** 特徴抽出インスタンス初期化 Total: {this.instance_table.Count}");

                this.temporary_table = new Dictionary<FaceTemplateCreate, Tuple<int, DateTime>>();

                this.lifespan_second = Lifespan;

                this.watch_timer = new CDisposableTimer();
                this.watch_timer.Interval = this.interval_second * 1000;
                this.watch_timer.Elapsed += Watch_timer_Elapsed;

                this.watch_timer.Start();
            }
        }



        // ▼ MOD SDK初期化対応 2021/07/13 Y.Tani Start
        /// <summary>
        /// テンプレート作成　インスタンス取得
        /// </summary>
        /// <param name="settingInfo"></param>
        /// <returns></returns>
        public FaceTemplateCreate GetInstance(FaceAuthSettingsInfo.SettingsInfo settingInfo)
        // ▲ MOD SDK初期化対応 2021/07/13 Y.Tani End
        {
            //  空きがなかったらnull返却、エラーは例外スロー
            FaceTemplateCreate returnValue = null;

            if (this.instance_table == null || this.instance_table.Count == 0)
            {
                throw new NotInitializedException(typeof(CreatorEngineObjectManager).Name);
            }

            lock (this)
            {
                //  まずinstance_tableから調べる
                while (true)
                {
                    //  特徴量が合致するもの
                    var instances = this.instance_table.ToList();

                    if (instances.Count() == 0)
                    {
                        //  合致するものがないということはInitializeか引数がおかしいので例外
                        throw new ArgumentException();
                    }

                    //  そのうちで未使用のもの
                    var standby = instances.Where(x => x.Value == null).ToList();
                    if (standby.Count == 0)
                    {
                        Trace.OutputErrorTrace("    ***** 特徴抽出オブジェクト フル");
                        break;
                    }

                    returnValue = standby.First().Key;

                    var hash = returnValue.GetHashCode();
                    Trace.OutputTrace($"    ***** 特徴抽出オブジェクト Stanby: {standby.Count} , Hash: {hash}");

                    //  使っている印を付けとく
                    this.instance_table[returnValue] = new Tuple<int, DateTime>(System.Threading.Thread.CurrentThread.ManagedThreadId, DateTime.Now);

                    break;
                }

                //  空いてなかったら拡張テーブルを調べる
                if (returnValue == null)
                {
                    while (true)
                    {
                        if (this.temporary_table.Count >= this.temporary)
                        {
                            //  空きがない
                            Trace.OutputErrorTrace("    ***** 特徴抽出オブジェクト 拡張オーバー");
                            break;
                        }

                        // ▼ ADD SDK初期化対応 2021/07/13 Y.Tani Start
                        // 特徴抽出用の初期値を設定
                        var faceFeatureSetting = new GFRL.FaceRecognition.FaceFeatureSetting();
                        {
                            faceFeatureSetting.IsEstimateGenderAge = settingInfo.is_estimate_gender_age;                // 性別年齢推定を行うか（true：する、false：しない）
                            faceFeatureSetting.TemplateQualityThreshold = settingInfo.template_quality_threshold_auth;  // テンプレート評価値のしきい値
                        }
                        // ▲ ADD SDK初期化対応 2021/07/13 Y.Tani End

                        var instance = new FaceTemplateCreate();

                        // ▼ MOD SDK初期化対応 2021/07/13 Y.Tani Start
                        instance.Initialize(
                            faceFeatureSetting,
                            settingInfo.face_recog_data_type                    // 照合タイプを指定
                        );
                        // ▲ MOD SDK初期化対応 2021/07/13 Y.Tani End

                        this.temporary_table[instance] = new Tuple<int, DateTime>(System.Threading.Thread.CurrentThread.ManagedThreadId, DateTime.Now);

                        returnValue = instance;

                        Trace.OutputTrace("    ***** 特徴抽出オブジェクト 拡張生成");

                        break;
                    }
                }
            }

            return returnValue;
        }

        /// <summary>
        /// テンプレート作成　インスタンス解放
        /// </summary>
        /// <param name="instance"></param>
        public void ReleaseInstance(FaceTemplateCreate instance)
        {
            if (instance == null)
            {
                throw new ArgumentNullException();
            }

            if (this.instance_table == null || this.instance_table.Count == 0)
            {
                throw new NotInitializedException(typeof(CreatorEngineObjectManager).Name);
            }

            lock (this)
            {
                while (true)
                {
                    if (this.instance_table.ContainsKey(instance))
                    {
                        // Valueを未使用（null）に
                        this.instance_table[instance] = null;
                        Trace.OutputTrace($"    ***** 特徴抽出インスタンス使用済: {instance.GetHashCode()}");
                        break;
                    }

                    if (this.temporary_table.ContainsKey(instance))
                    {
                        //  テンポラリの場合はメモリ解放
                        this.temporary_table.Remove(instance);
                        //instance.Dispose();

                        break;
                    }

                    break;
                }
            }
        }

        /// <summary>
        /// テンプレート作成　インスタンス制限時間チェック
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Watch_timer_Elapsed(object sender, ElapsedEventArgs e)
        {
            lock (this)
            {
                try
                {
                    //  一旦止めて
                    this.watch_timer.Stop();

                    //  制限時間を超えているものはリリースする
                    if (this.instance_table == null) return;
                    if (this.temporary_table == null) return;

                    var now = DateTime.Now;
                    var timeout = this.instance_table.Where(x => x.Value != null && (now - x.Value.Item2).TotalSeconds > this.lifespan_second).ToList();

                    if (timeout.Count > 0)
                    {
                        foreach (var taget in timeout)
                        {
                            Trace.OutputTrace($"    ***** 特徴抽出インスタンスチェック {taget.Key.GetHashCode()}");
                            this.instance_table[taget.Key] = null;
                        }
                    }

                    timeout = this.temporary_table.Where(x => x.Value != null && (now - x.Value.Item2).TotalSeconds > this.lifespan_second).ToList();

                    if (timeout.Count > 0)
                    {
                        foreach (var taget in timeout)
                        {
                            Trace.OutputTrace($"    ***** 特徴抽出インスタンスチェック {taget.Key.GetHashCode()} (temporary)");
                            this.temporary_table.Remove(taget.Key);
                        }
                    }
                }
                finally
                {
                    //  再スタート
                    this.watch_timer.Start();
                }
            }
        }

    }
    #endregion

    #region 性別年齢推定クラス

    /// <summary>
    /// 性別年齢推定クラス
    /// </summary>
    public class EstimatorEngineObjectManager : IDisposable
    {
        //  シングルトン
        public static readonly EstimatorEngineObjectManager Instance = new EstimatorEngineObjectManager();

        private Dictionary<GenderAgeEstimator, Tuple<int, DateTime>> instance_table = new Dictionary<GenderAgeEstimator, Tuple<int, DateTime>>();
        private Dictionary<GenderAgeEstimator, Tuple<int, DateTime>> temporary_table = new Dictionary<GenderAgeEstimator, Tuple<int, DateTime>>();
        //private int capacity = 0;
        private int temporary = 0;

        private int max_capacity = 30;      //  ここはConfigなりで
        private int max_temporary = 10;     //  ここはConfigなりで
        private int lifespan_second = 10;   //  ここはConfigなりで
        private int interval_second = 15;   //  ここはConfigなりで

        private CDisposableTimer watch_timer = new CDisposableTimer();

        public void Dispose()
        {
            lock (this)
            {
                if (this.instance_table != null)
                {
                    // 使用中のものがないかどうか
                    if (this.instance_table.Values.Count(x => x != null) > 0)
                    {
                        throw new InvalidOperationException();
                    }

                    foreach (var instance in this.instance_table.Keys)
                    {
                        // instance.Dispose();
                    }
                    this.instance_table = null;
                }

                if (this.temporary_table != null)
                {
                    // 使用中のものがないかどうか
                    if (this.temporary_table.Values.Count(x => x != null) > 0)
                    {
                        throw new InvalidOperationException();
                    }

                    foreach (var instance in this.temporary_table.Keys)
                    {
                        // instance.Dispose();
                    }
                    this.temporary_table = null;
                }

                //  監視タイマー終了
                this.watch_timer.Dispose();
                this.watch_timer.Stop();
            }
        }

        /// <summary>
        /// 性別年齢推定クラス 初期化
        /// </summary>
        /// <param name="capacity"></param>
        /// <param name="temporary"></param>
        /// <param name="lifespan"></param>
        public void Initialize(int capacity, int temporary, int lifespan)
        {
            if (0 >= capacity || capacity > this.max_capacity)
            {
                throw new ArgumentOutOfRangeException();
            }

            if (0 >= temporary || temporary > this.max_temporary)
            {
                throw new ArgumentOutOfRangeException();
            }

            this.Dispose();

            lock (this)
            {
                //this.capacity = capacity;
                this.temporary = temporary;

                //  Keyはインスタンス、ValueはスレッドIDと取得時刻で未使用はnull
                this.instance_table = new Dictionary<GenderAgeEstimator, Tuple<int, DateTime>>();

                for (int i = 0; i < capacity; i++)
                {
                    var instance = new GenderAgeEstimator();

                    instance.Initialize();
                    this.instance_table[instance] = null;

                    var hash = instance.GetHashCode();
                    Trace.OutputTrace($"    ***** 性別年齢推定インスタンス初期化 [{i}] , Hash: {hash}");
                }

                Trace.OutputTrace($"    ***** 性別年齢推定インスタンス初期化 Total: {this.instance_table.Count}");

                this.temporary_table = new Dictionary<GenderAgeEstimator, Tuple<int, DateTime>>();

                this.lifespan_second = lifespan;

                this.watch_timer = new CDisposableTimer();
                this.watch_timer.Interval = this.interval_second * 1000;
                this.watch_timer.Elapsed += Watch_timer_Elapsed;

                this.watch_timer.Start();
            }
        }

        /// <summary>
        /// 性別年齢推定クラス　インスタンス取得
        /// </summary>
        /// <returns></returns>
        public GenderAgeEstimator GetInstance()
        {
            //  空きがなかったらnull返却、エラーは例外スロー
            GenderAgeEstimator returnValue = null;

            if (this.instance_table == null || this.instance_table.Count == 0)
            {
                throw new NotInitializedException(typeof(EstimatorEngineObjectManager).Name);
            }

            lock (this)
            {
                //  まずinstance_tableから調べる
                while (true)
                {
                    //  特徴量が合致するもの
                    var instances = this.instance_table;

                    if (instances.Count() == 0)
                    {
                        //  合致するものがないということはInitializeか引数がおかしいので例外
                        throw new ArgumentException();
                    }

                    //  そのうちで未使用のもの
                    var standby = instances.Where(x => x.Value == null).ToList();

                    if (standby.Count == 0)
                    {
                        break;
                    }

                    returnValue = standby.First().Key;

                    var hash = returnValue.GetHashCode();

                    Trace.OutputTrace($"    ***** 性別年齢推定オブジェクト Stanby: {standby.Count} , Hash: {hash}");

                    //  使っている印を付けとく
                    this.instance_table[returnValue] = new Tuple<int, DateTime>(System.Threading.Thread.CurrentThread.ManagedThreadId, DateTime.Now);

                    break;
                }

                //  空いてなかったら拡張テーブルを調べる
                if (returnValue == null)
                {
                    while (true)
                    {
                        if (this.temporary_table.Count >= this.temporary)
                        {
                            //  空きがない
                            Trace.OutputErrorTrace("    ***** 性別年齢推定オブジェクト 拡張オーバー");
                            break;
                        }

                        var instance = new GenderAgeEstimator();

                        instance.Initialize();
                        this.temporary_table[instance] = new Tuple<int, DateTime>(System.Threading.Thread.CurrentThread.ManagedThreadId, DateTime.Now);

                        returnValue = instance;

                        Trace.OutputTrace("    ***** 性別年齢推定オブジェクト 拡張生成");

                        break;
                    }
                }
            }
            return returnValue;
        }

        /// <summary>
        /// 性別年齢推定クラス　インスタンス解放
        /// </summary>
        /// <param name="instance"></param>
        public void ReleaseInstance(GenderAgeEstimator instance)
        {
            if (instance == null)
            {
                throw new ArgumentNullException();
            }

            if (this.instance_table == null || this.instance_table.Count == 0)
            {
                throw new NotInitializedException(typeof(EstimatorEngineObjectManager).Name);
            }

            lock (this)
            {
                while (true)
                {
                    if (this.instance_table.ContainsKey(instance))
                    {
                        // Valueを未使用（null）に
                        this.instance_table[instance] = null;
                        Trace.OutputTrace($"    ***** 性別年齢推定インスタンス使用済: {instance.GetHashCode()}");
                        break;
                    }

                    if (this.temporary_table.ContainsKey(instance))
                    {
                        //  テンポラリの場合はメモリ解放
                        this.temporary_table.Remove(instance);
                        //instance.Dispose();

                        break;
                    }

                    break;
                }
            }
        }

        /// <summary>
        /// 性別年齢推定クラス　インスタンス制限時間チェック
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Watch_timer_Elapsed(object sender, ElapsedEventArgs e)
        {
            lock (this)
            {
                try
                {
                    //  一旦止めて
                    this.watch_timer.Stop();

                    //  制限時間を超えているものはリリースする
                    if (this.instance_table == null) return;
                    if (this.temporary_table == null) return;

                    var now = DateTime.Now;

                    var timeout = this.instance_table.Where(x => x.Value != null && (now - x.Value.Item2).TotalSeconds > this.lifespan_second).ToList();

                    if (timeout.Count > 0)
                    {
                        foreach (var taget in timeout)
                        {
                            Trace.OutputTrace($"    ***** 性別年齢推定インスタンスチェック {taget.Key.GetHashCode()}");
                            this.instance_table[taget.Key] = null;
                        }
                    }

                    timeout = this.temporary_table.Where(x => x.Value != null && (now - x.Value.Item2).TotalSeconds > this.lifespan_second).ToList();

                    if (timeout.Count > 0)
                    {
                        foreach (var taget in timeout)
                        {
                            Trace.OutputTrace($"    ***** 性別年齢推定インスタンスチェック {taget.Key.GetHashCode()} (temporary)");
                            this.temporary_table.Remove(taget.Key);
                        }
                    }
                }
                finally
                {
                    //  再スタート
                    this.watch_timer.Start();
                }
            }
        }
    }
    #endregion

    #region タイマー

    /// <summary>
    /// タイマー
    /// </summary>
    public class CDisposableTimer : Timer
    {
        private bool dispose = false;

        public CDisposableTimer()
        {

        }

        public new void Dispose()
        {
            lock (this)
            {
                this.dispose = true;
            }
        }

        public new void Start()
        {
            lock (this)
            {
                if (!this.dispose)
                {
                    base.Start();
                }
            }
        }
        public new void Stop()
        {
            lock (this)
            {
                base.Stop();
            }
        }

        public void Reset()
        {
            lock (this)
            {
                this.dispose = false;
            }
        }
    }
    #endregion
}
