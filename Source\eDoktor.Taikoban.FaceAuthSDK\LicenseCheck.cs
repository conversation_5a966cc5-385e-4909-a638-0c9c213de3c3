﻿//#define USE_HL_KEY // Use Hardware Key
#define USE_EOM // Use EngineObjectManager （インスタンス管理用）

using System;
using eDoktor.Common;

namespace eDoktor.Taikoban.FaceAuthSDK
{
    /// <summary>
    /// ライセンス確認
    /// </summary>
    public class LicenseCheck
    {
        /// <summary>
        /// Configキー グローリーSDKライセンスIファイルパス
        /// </summary>
        private const string CONFIG_KEY_LICENSE_FILE_PATH = "LicenseFilePath";
        
        /// <summary>
        /// Configキー グローリーSDKライセンスID
        /// </summary>
        private const string CONFIG_KEY_LICENSE_ID = "LicenseID";

        /// <summary>
        /// ライセンス情報を取得する
        /// </summary>
        /// <returns>true:正常終了 false:異常終了</returns>
        public static bool CheckLicenseFile()
        {
            bool ret = false;

            try
            {
                Trace.OutputDebugTrace("### Mode : License File");

                var licenseFilePath = eDoktor.Common.Configuration.AppSetting(CONFIG_KEY_LICENSE_FILE_PATH);

                if (uint.TryParse(eDoktor.Common.Configuration.AppSetting(CONFIG_KEY_LICENSE_ID), out uint licenseId))
                {
                    GFRL.LicenseSetting.SetLicenseSetting(new GFRL.LicenseSetting()
                    {
                        LicenseFilePath = licenseFilePath,  // ライセンスファイルのパス
                        LicenseID = licenseId,              // ベンダーID,
                    });
                    ret = true;
                }
            }
            catch (Exception ex)
            {
                Trace.OutputExceptionTrace(ex);
            }

            return ret;
        }
    }
}
