﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace eDoktor.Taikoban.FaceInterprocess
{
    class Util
    {
        public static string CSV_SEPERATOR = ",";
        public static string EscapeDouble(string param)
        {
            return (string.IsNullOrEmpty(param)) ? string.Empty : param.Replace("\"", "\"\"");
        }

        public static string EncloseQuotationMark(string param)
        {
            return "\"" + EscapeDouble(param) + "\"";
        }

        public static string EscapeQuotationMark(string param)
        {
            if (string.IsNullOrEmpty(param))
            {
                return string.Empty;
            }
            if (param.Length < 2)
            {
                return param;
            }
            if (param.Substring(0, 1) == "\"" && param.Substring(param.Length -1, 1) == "\"")
            {
                string temp = param.Remove(param.Length - 1, 1);
                temp = temp.Remove(0, 1);
                temp = temp.Replace("\"\"", "\"");
                return temp;
            }
            else
            {
                string result = param.Trim('\''); // tani add ★★★ アポストロフィがあるとDB登録にコケるので、削除した。認証時に影響がないか要確認 ★★★
                return param;
            }
        }

        public static string ConvertEncodeling(string src, System.Text.Encoding destEnc)
        {
            // tani mod ★★★ 今回は日本語を電文に乗せるので、ASCIIではなく、Unicodeを指定するよう変更
//            byte[] srcTemp = System.Text.Encoding.ASCII.GetBytes(src);
//            byte[] destTemp = System.Text.Encoding.Convert(System.Text.Encoding.ASCII, destEnc, srcTemp);
            byte[] srcTemp = System.Text.Encoding.Unicode.GetBytes(src);
            byte[] destTemp = System.Text.Encoding.Convert(System.Text.Encoding.Unicode, destEnc, srcTemp);
            string retString = destEnc.GetString(destTemp);
            return retString;
        }

        public static string ConvertEncodeling(byte[] src, System.Text.Encoding srcEnc)
        {
            // tani mod ★★★ 今回は日本語を電文に乗せるので、ASCIIではなく、Unicodeを指定するよう変更
//            byte[] destTemp = System.Text.Encoding.Convert(srcEnc, System.Text.Encoding.ASCII, src);
//            string retString = System.Text.Encoding.ASCII.GetString(destTemp);
            byte[] destTemp = System.Text.Encoding.Convert(srcEnc, System.Text.Encoding.Unicode, src);
            string retString = System.Text.Encoding.Unicode.GetString(destTemp);
            return retString;
        }
    }
}
